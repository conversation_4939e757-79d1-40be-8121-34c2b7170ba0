{"ast": null, "code": "import { useContext } from 'react';\nimport { getI18n, I18nContext } from './context.js';\nexport const useSSR = (initialI18nStore, initialLanguage, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n.options?.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce((mem, lngResources) => {\n      Object.keys(lngResources).forEach(ns => {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n};", "map": {"version": 3, "names": ["useContext", "getI18n", "I18nContext", "useSSR", "initialI18nStore", "initialLanguage", "props", "i18n", "i18nFromProps", "i18nFromContext", "options", "isClone", "initializedStoreOnce", "services", "resourceStore", "data", "ns", "Object", "values", "reduce", "mem", "lngResources", "keys", "for<PERSON>ach", "indexOf", "push", "isInitialized", "initializedLanguageOnce", "changeLanguage"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/node_modules/react-i18next/dist/es/useSSR.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { getI18n, I18nContext } from './context.js';\nexport const useSSR = (initialI18nStore, initialLanguage, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n.options?.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce((mem, lngResources) => {\n      Object.keys(lngResources).forEach(ns => {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n};"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,OAAO,EAAEC,WAAW,QAAQ,cAAc;AACnD,OAAO,MAAMC,MAAM,GAAGA,CAACC,gBAAgB,EAAEC,eAAe,EAAEC,KAAK,GAAG,CAAC,CAAC,KAAK;EACvE,MAAM;IACJC,IAAI,EAAEC;EACR,CAAC,GAAGF,KAAK;EACT,MAAM;IACJC,IAAI,EAAEE;EACR,CAAC,GAAGT,UAAU,CAACE,WAAW,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMK,IAAI,GAAGC,aAAa,IAAIC,eAAe,IAAIR,OAAO,CAAC,CAAC;EAC1D,IAAIM,IAAI,CAACG,OAAO,EAAEC,OAAO,EAAE;EAC3B,IAAIP,gBAAgB,IAAI,CAACG,IAAI,CAACK,oBAAoB,EAAE;IAClDL,IAAI,CAACM,QAAQ,CAACC,aAAa,CAACC,IAAI,GAAGX,gBAAgB;IACnDG,IAAI,CAACG,OAAO,CAACM,EAAE,GAAGC,MAAM,CAACC,MAAM,CAACd,gBAAgB,CAAC,CAACe,MAAM,CAAC,CAACC,GAAG,EAAEC,YAAY,KAAK;MAC9EJ,MAAM,CAACK,IAAI,CAACD,YAAY,CAAC,CAACE,OAAO,CAACP,EAAE,IAAI;QACtC,IAAII,GAAG,CAACI,OAAO,CAACR,EAAE,CAAC,GAAG,CAAC,EAAEI,GAAG,CAACK,IAAI,CAACT,EAAE,CAAC;MACvC,CAAC,CAAC;MACF,OAAOI,GAAG;IACZ,CAAC,EAAEb,IAAI,CAACG,OAAO,CAACM,EAAE,CAAC;IACnBT,IAAI,CAACK,oBAAoB,GAAG,IAAI;IAChCL,IAAI,CAACmB,aAAa,GAAG,IAAI;EAC3B;EACA,IAAIrB,eAAe,IAAI,CAACE,IAAI,CAACoB,uBAAuB,EAAE;IACpDpB,IAAI,CAACqB,cAAc,CAACvB,eAAe,CAAC;IACpCE,IAAI,CAACoB,uBAAuB,GAAG,IAAI;EACrC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}