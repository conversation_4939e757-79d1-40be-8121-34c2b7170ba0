{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\Chekpoints.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext, useRef } from \"react\";\nimport { Box, Typography, TextField, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport DraggableCheckpoint from \"./DraggableCheckpoint\";\nimport { warning } from \"../../assets/icons/icons\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport CheckPointEditPopup from \"./CheckpointEditPopup\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport CheckPointAddPopup from \"./CheckpointAddPopup\";\nimport { AccountContext } from \"../login/AccountContext\";\nimport { useTranslation } from 'react-i18next';\nimport '../../styles/rtl_styles.scss';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet editInteractionName;\nconst Checkpoints = () => {\n  _s();\n  var _checklistGuideMetaDa3;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    ShowLauncherSettings,\n    setShowLauncherSettings,\n    setCheckPointsEditPopup,\n    checkpointsEditPopup,\n    titlePopup,\n    setTitlePopup,\n    setDesignPopup,\n    titleColor,\n    setTitleColor,\n    checkpointsPopup,\n    setCheckPointsPopup,\n    checkpointTitleColor,\n    setCheckpointTitleColor,\n    checkpointTitleDescription,\n    setCheckpointTitleDescription,\n    checkpointIconColor,\n    setCheckpointIconColor,\n    setUnlockCheckPointInOrder,\n    unlockCheckPointInOrder,\n    checkPointMessage,\n    setCheckPointMessage,\n    setCheckPointsAddPopup,\n    checkpointsAddPopup,\n    checklistGuideMetaData,\n    updateChecklistCheckPoints,\n    deleteCheckpoint,\n    setIsUnSavedChanges,\n    isUnSavedChanges\n  } = useDrawerStore(state => state);\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const [messageError, setMessageError] = useState(\"\");\n  const [checklistCheckpointProperties, setChecklistCheckpointProperties] = useState(() => {\n    var _checklistGuideMetaDa;\n    const initialchecklistCheckpointProperties = ((_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : _checklistGuideMetaDa.checkpoints) || {\n      checkpointsList: [],\n      checkpointTitles: \"#333\",\n      checkpointsDescription: \"#8D8D8D\",\n      checkpointsIcons: \"#333\",\n      unlockCHeckpointInOrder: false,\n      message: \"complete items in order\"\n    };\n    return initialchecklistCheckpointProperties;\n  });\n  // State for tracking changes and apply button\n  const [isDisabled, setIsDisabled] = useState(true);\n  const [hasChanges, setHasChanges] = useState(false);\n  const [initialState, setInitialState] = useState(checklistCheckpointProperties);\n\n  // Keep local state in sync with the store\n  useEffect(() => {\n    var _checklistGuideMetaDa2;\n    if ((_checklistGuideMetaDa2 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa2 !== void 0 && _checklistGuideMetaDa2.checkpoints) {\n      const newCheckpoints = checklistGuideMetaData[0].checkpoints;\n      setChecklistCheckpointProperties(newCheckpoints);\n      setInitialState(newCheckpoints);\n      setHasChanges(false);\n      setIsDisabled(true);\n    }\n  }, [(_checklistGuideMetaDa3 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa3 === void 0 ? void 0 : _checklistGuideMetaDa3.checkpoints]);\n  // Function to check if the Apply button should be enabled\n  const updateApplyButtonState = (changed, hasErrors = false) => {\n    setIsDisabled(!changed || hasErrors);\n  };\n\n  // Removed duplicate declaration of messageError\n\n  // Effect to check for any changes compared to initial state\n  useEffect(() => {\n    // Compare current properties with initial state\n    const hasAnyChanges = JSON.stringify(checklistCheckpointProperties) !== JSON.stringify(initialState);\n    setHasChanges(hasAnyChanges);\n\n    // Check for validation errors\n    const hasValidationErrors = !!messageError;\n    updateApplyButtonState(hasAnyChanges, hasValidationErrors);\n  }, [checklistCheckpointProperties, initialState, messageError]);\n  const [interactions, setInteractions] = useState([]);\n  const [skip, setSkip] = useState(0);\n  const top = 5;\n  const [loading, setLoading] = useState(false);\n  const dropdownRef = useRef(null);\n  const handleClose = () => {\n    setCheckPointsPopup(false);\n  };\n  const handledesignclose = () => {\n    setDesignPopup(false);\n  };\n  const onReselectElement = () => {};\n  const onPropertyChange = (key, value) => {\n    setChecklistCheckpointProperties(prevState => {\n      const newState = {\n        ...prevState,\n        [key]: value\n      };\n      // Mark that changes have been made\n      setHasChanges(true);\n      return newState;\n    });\n  };\n  const [kkk, setKKK] = useState(false);\n  const handleApplyChanges = () => {\n    if (kkk == false) {\n      // If no changes were made to the order, use the original list\n      checklistCheckpointProperties.checkpointsList = checklistGuideMetaData[0].checkpoints.checkpointsList;\n    }\n    updateChecklistCheckPoints(checklistCheckpointProperties);\n    // Update the initial state to the current state after applying changes\n    setInitialState({\n      ...checklistCheckpointProperties\n    });\n    // Reset the changes flag\n    setHasChanges(false);\n    // Disable the Apply button\n    setIsDisabled(true);\n    handleClose();\n    setIsUnSavedChanges(true);\n  };\n  const handleEditClick = id => {\n    editInteractionName = id;\n    setCheckPointsEditPopup(true);\n  };\n  const handleDeleteClick = interaction => {\n    setChecklistCheckpointProperties(prevState => ({\n      ...prevState,\n      checkpointsList: prevState.checkpointsList.filter(checkpoint => checkpoint.interaction !== interaction)\n    }));\n    setHasChanges(true);\n    deleteCheckpoint(interaction);\n  };\n  let checkpoints = checklistCheckpointProperties.checkpointsList;\n  const [draggedItemIndex, setDraggedItemIndex] = useState(null);\n\n  // Handle drag start\n  const handleDragStart = index => {\n    setDraggedItemIndex(index);\n  };\n\n  // Handle drag over (allows dropping)\n  const handleDragOver = event => {\n    event.preventDefault();\n  };\n\n  // Handle drop (reorder items)\n  const handleDrop = index => {\n    if (draggedItemIndex === null || draggedItemIndex === index) return;\n    const updatedCheckpoints = [...checkpoints];\n    const [draggedItem] = updatedCheckpoints.splice(draggedItemIndex, 1);\n    updatedCheckpoints.splice(index, 0, draggedItem);\n    setKKK(true);\n    checkpoints = updatedCheckpoints;\n    setDraggedItemIndex(null);\n\n    // Update state correctly and mark changes\n    setChecklistCheckpointProperties(prevState => ({\n      ...prevState,\n      // Copy the existing state\n      checkpointsList: updatedCheckpoints // Update only the checkpointsList\n    }));\n    setHasChanges(true);\n  };\n  const handleAddCheckpoint = () => {\n    setCheckPointsAddPopup(true);\n  };\n  const handleMessageChange = e => {\n    const value = e.target.value;\n    let errorMessage = \"\";\n    if (value.length < 2) {\n      errorMessage = translate(\"Min: 2 Characters\");\n    } else if (value.length > 30) {\n      errorMessage = translate(\"Max: 30 Characters\");\n    }\n    setMessageError(errorMessage);\n    onPropertyChange(\"message\", value);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"back\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: translate(\"Steps\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-canblock\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls qadpt-errmsg\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              backgroundColor: \"#EAE2E2\",\n              borderRadius: \"var(--button-border-radius)\",\n              height: \"auto\",\n              padding: \"10px\",\n              marginBottom: \"5px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: checkpoints.map((checkpoint, index) => /*#__PURE__*/_jsxDEV(DraggableCheckpoint, {\n                checkpoint: checkpoint,\n                index: index,\n                handleEditClick: () => handleEditClick(checkpoint === null || checkpoint === void 0 ? void 0 : checkpoint.id),\n                handleDeleteClick: () => handleDeleteClick(checkpoint === null || checkpoint === void 0 ? void 0 : checkpoint.interaction),\n                handleDragStart: handleDragStart,\n                handleDragOver: handleDragOver,\n                handleDrop: handleDrop,\n                isDragging: index === draggedItemIndex\n              }, checkpoint.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 10\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAddCheckpoint,\n              className: \"qadpt-memberButton qadpt-check\",\n              style: {\n                width: \"100%\",\n                backgroundColor: \"#D3D9DA\",\n                color: \"var(--primarycolor) !important\",\n                placeContent: \"center\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: translate(\"Add Step\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Step Title\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: checklistCheckpointProperties.checkpointTitles,\n                onChange: e => onPropertyChange(\"checkpointTitles\", e.target.value),\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Step Description\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: checklistCheckpointProperties === null || checklistCheckpointProperties === void 0 ? void 0 : checklistCheckpointProperties.checkpointsDescription,\n                onChange: e => onPropertyChange(\"checkpointsDescription\", e.target.value),\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-control-label\",\n              children: translate(\"Icon Color\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: checklistCheckpointProperties === null || checklistCheckpointProperties === void 0 ? void 0 : checklistCheckpointProperties.checkpointsIcons,\n                onChange: e => {\n                  // Update the global Icons color\n                  onPropertyChange(\"checkpointsIcons\", e.target.value);\n                },\n                className: \"qadpt-color-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            sx: {\n              height: \"auto !important\",\n              flexDirection: \"column\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Unlock Checkpoints in Order\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"toggle-switch\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: checklistCheckpointProperties.unlockCHeckpointInOrder,\n                    onChange: e => onPropertyChange(\"unlockCHeckpointInOrder\", e.target.checked),\n                    name: \"showByDefault\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"slider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 8\n            }, this), checklistCheckpointProperties.unlockCHeckpointInOrder === true && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  backgroundColor: \"#EAE2E2\",\n                  borderRadius: \"var(--button-border-radius)\",\n                  height: \"auto\",\n                  padding: \"8px 8px 0 8px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    paddingBottom: \"5px\",\n                    textAlign: \"left\"\n                  },\n                  children: translate(\"Message\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  className: \"qadpt-control-box\",\n                  sx: {\n                    padding: \"0 !important\",\n                    height: \"auto !important\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    variant: \"outlined\",\n                    size: \"small\",\n                    placeholder: translate(\"Checklist Title\"),\n                    className: \"qadpt-control-input\",\n                    value: checklistCheckpointProperties.message,\n                    style: {\n                      width: \"100%\"\n                    },\n                    onChange: handleMessageChange,\n                    error: Boolean(messageError) // Show error if message exists\n                    ,\n                    helperText: messageError ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        display: \"flex\",\n                        fontSize: \"12px\",\n                        alignItems: \"center\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          marginRight: \"4px\"\n                        },\n                        dangerouslySetInnerHTML: {\n                          __html: warning\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 16\n                      }, this), messageError]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 15\n                    }, this) : null,\n                    InputProps: {\n                      endAdornment: \"\",\n                      sx: {\n                        \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                          border: \"none\"\n                        },\n                        \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                          border: \"none\"\n                        },\n                        \"& fieldset\": {\n                          border: \"none\"\n                        },\n                        \"& input\": {\n                          textAlign: \"left !important\",\n                          paddingLeft: \"10px !important\"\n                        },\n                        \"&.MuiInputBase-root\": {\n                          height: \"auto !important\",\n                          marginBottom: \"5px\"\n                        }\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 10\n              }, this)\n            }, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: `qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`,\n          disabled: isDisabled,\n          children: translate(\"Apply\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 4\n    }, this), checkpointsEditPopup && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(CheckPointEditPopup, {\n        checkpointsEditPopup: checkpointsEditPopup,\n        editInteractionName: editInteractionName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 6\n      }, this)\n    }, void 0, false), checkpointsAddPopup && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(CheckPointAddPopup, {\n        checklistCheckpointProperties: checklistCheckpointProperties,\n        setChecklistCheckpointProperties: setChecklistCheckpointProperties\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 6\n      }, this)\n    }, void 0, false)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 3\n  }, this);\n};\n_s(Checkpoints, \"M5UmP3Uki4XW6lxp6HMl2D3OEgs=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = Checkpoints;\nexport default Checkpoints;\nexport { editInteractionName };\nvar _c;\n$RefreshReg$(_c, \"Checkpoints\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "useRef", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "CloseIcon", "useDrawerStore", "DraggableCheckpoint", "warning", "ArrowBackIosNewOutlinedIcon", "CheckPointEditPopup", "AddIcon", "CheckPointAddPopup", "AccountContext", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "editInteractionName", "Checkpoints", "_s", "_checklistGuideMetaDa3", "t", "translate", "ShowLauncherSettings", "setShowLauncherSettings", "setCheckPointsEditPopup", "checkpointsEditPopup", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "checkpointsPopup", "setCheckPointsPopup", "checkpointTitleColor", "setCheckpointTitleColor", "checkpointTitleDescription", "setCheckpointTitleDescription", "checkpointIconColor", "setCheckpointIconColor", "setUnlockCheckPointInOrder", "unlockCheckPointInOrder", "checkPointMessage", "setCheckPointMessage", "setCheckPointsAddPopup", "checkpointsAddPopup", "checklistGuideMetaData", "updateChecklistCheckPoints", "deleteCheckpoint", "setIsUnSavedChanges", "isUnSavedChanges", "state", "accountId", "messageError", "setMessageError", "checklistCheckpointProperties", "setChecklistCheckpointProperties", "_checklistGuideMetaDa", "initialchecklistCheckpointProperties", "checkpoints", "checkpointsList", "checkpointTitles", "checkpointsDescription", "checkpointsIcons", "unlockCHeckpointInOrder", "message", "isDisabled", "setIsDisabled", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "initialState", "setInitialState", "_checklistGuideMetaDa2", "newCheckpoints", "updateApplyButtonState", "changed", "hasErrors", "hasAnyChanges", "JSON", "stringify", "hasValidationErrors", "interactions", "setInteractions", "skip", "setSkip", "top", "loading", "setLoading", "dropdownRef", "handleClose", "handledesignclose", "onReselectElement", "onPropertyChange", "key", "value", "prevState", "newState", "kkk", "setKKK", "handleApplyChanges", "handleEditClick", "id", "handleDeleteClick", "interaction", "filter", "checkpoint", "draggedItemIndex", "setDraggedItemIndex", "handleDragStart", "index", "handleDragOver", "event", "preventDefault", "handleDrop", "updatedCheckpoints", "draggedItem", "splice", "handleAddCheckpoint", "handleMessageChange", "e", "target", "errorMessage", "length", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "sx", "backgroundColor", "borderRadius", "height", "padding", "marginBottom", "map", "isDragging", "style", "width", "color", "place<PERSON><PERSON>nt", "type", "onChange", "flexDirection", "display", "alignItems", "checked", "name", "paddingBottom", "textAlign", "variant", "placeholder", "error", "Boolean", "helperText", "fontSize", "marginRight", "dangerouslySetInnerHTML", "__html", "InputProps", "endAdornment", "border", "paddingLeft", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/checklist/Chekpoints.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect, useContext, useRef } from \"react\";\r\nimport { <PERSON>, Typo<PERSON>, <PERSON>Field, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport DraggableCheckpoint from \"./DraggableCheckpoint\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n    Solid,\r\n    editicon,\r\n\tdeleteicon,\r\n\tdeletestep,\r\n\teditpricol,\r\n\twarning\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport CheckPointEditPopup from \"./CheckpointEditPopup\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport CheckPointAddPopup from \"./CheckpointAddPopup\";\r\nimport { getAllGuides } from \"../../services/GuideListServices\";\r\nimport { AccountContext } from \"../login/AccountContext\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport '../../styles/rtl_styles.scss';\r\n\r\nlet editInteractionName: string;\r\nconst Checkpoints = () => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tShowLauncherSettings,\r\n\t\tsetShowLauncherSettings,\r\n\t\tsetCheckPointsEditPopup,\r\n\t\tcheckpointsEditPopup,\r\n\t\ttitlePopup,\r\n\t\tsetTitlePopup,\r\n\t\tsetDesignPopup,\r\n\t\ttitleColor,\r\n\t\tsetTitleColor,\r\n\t\tcheckpointsPopup,\r\n\t\tsetCheckPointsPopup,\r\n\t\tcheckpointTitleColor,\r\n\t\tsetCheckpointTitleColor,\r\n\t\tcheckpointTitleDescription,\r\n\t\tsetCheckpointTitleDescription,\r\n\t\tcheckpointIconColor,\r\n\t\tsetCheckpointIconColor,\r\n\t\tsetUnlockCheckPointInOrder,\r\n\t\tunlockCheckPointInOrder,\r\n\t\tcheckPointMessage,\r\n\t\tsetCheckPointMessage,\r\n\t\tsetCheckPointsAddPopup,\r\n\t\tcheckpointsAddPopup,\r\n\t\tchecklistGuideMetaData,\r\n\t\tupdateChecklistCheckPoints,\r\n\t\tdeleteCheckpoint,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tisUnSavedChanges,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst { accountId } = useContext(AccountContext);\r\n\tconst [messageError, setMessageError] = useState<string>(\"\");\r\n\tconst [checklistCheckpointProperties, setChecklistCheckpointProperties] = useState<any>(() => {\r\n\t\tconst initialchecklistCheckpointProperties = checklistGuideMetaData[0]?.checkpoints || {\r\n\t\t\tcheckpointsList: [],\r\n\t\t\tcheckpointTitles: \"#333\",\r\n\t\t\tcheckpointsDescription: \"#8D8D8D\",\r\n\t\t\tcheckpointsIcons: \"#333\",\r\n\t\t\tunlockCHeckpointInOrder: false,\r\n\t\t\tmessage: \"complete items in order\",\r\n\t\t};\r\n\t\treturn initialchecklistCheckpointProperties;\r\n\t});\r\n\t// State for tracking changes and apply button\r\n\tconst [isDisabled, setIsDisabled] = useState(true);\r\n\tconst [hasChanges, setHasChanges] = useState(false);\r\n\tconst [initialState, setInitialState] = useState(checklistCheckpointProperties);\r\n\r\n\t// Keep local state in sync with the store\r\n\tuseEffect(() => {\r\n\t\tif (checklistGuideMetaData[0]?.checkpoints) {\r\n\t\t\tconst newCheckpoints = checklistGuideMetaData[0].checkpoints;\r\n\t\t\tsetChecklistCheckpointProperties(newCheckpoints);\r\n\t\t\tsetInitialState(newCheckpoints);\r\n\t\t\tsetHasChanges(false);\r\n\t\t\tsetIsDisabled(true);\r\n\t\t}\r\n\t}, [checklistGuideMetaData[0]?.checkpoints]);\r\n\t// Function to check if the Apply button should be enabled\r\n\tconst updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {\r\n\t\tsetIsDisabled(!changed || hasErrors);\r\n\t};\r\n\r\n\t// Removed duplicate declaration of messageError\r\n\r\n\t// Effect to check for any changes compared to initial state\r\n\tuseEffect(() => {\r\n\t\t// Compare current properties with initial state\r\n\t\tconst hasAnyChanges = JSON.stringify(checklistCheckpointProperties) !== JSON.stringify(initialState);\r\n\t\tsetHasChanges(hasAnyChanges);\r\n\r\n\t\t// Check for validation errors\r\n\t\tconst hasValidationErrors = !!messageError;\r\n\r\n\t\tupdateApplyButtonState(hasAnyChanges, hasValidationErrors);\r\n\t}, [checklistCheckpointProperties, initialState, messageError]);\r\n\tconst [interactions, setInteractions] = useState<any[]>([]);\r\n\tconst [skip, setSkip] = useState(0);\r\n\tconst top = 5;\r\n\tconst [loading, setLoading] = useState(false);\r\n\tconst dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetCheckPointsPopup(false);\r\n\t};\r\n\tconst handledesignclose = () => {\r\n\t\tsetDesignPopup(false);\r\n\t};\r\n\r\n\tconst onReselectElement = () => {};\r\n\r\n\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\tsetChecklistCheckpointProperties((prevState: any) => {\r\n\t\t\tconst newState = {\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t};\r\n\t\t\t// Mark that changes have been made\r\n\t\t\tsetHasChanges(true);\r\n\t\t\treturn newState;\r\n\t\t});\r\n\t};\r\n\tconst [kkk, setKKK] = useState(false);\r\n\tconst handleApplyChanges = () => {\r\n\t\tif (kkk == false) {\r\n\t\t\t// If no changes were made to the order, use the original list\r\n\t\t\tchecklistCheckpointProperties.checkpointsList = checklistGuideMetaData[0].checkpoints.checkpointsList;\r\n\t\t}\r\n\r\n\t\tupdateChecklistCheckPoints(checklistCheckpointProperties);\r\n\t\t// Update the initial state to the current state after applying changes\r\n\t\tsetInitialState({ ...checklistCheckpointProperties });\r\n\t\t// Reset the changes flag\r\n\t\tsetHasChanges(false);\r\n\t\t// Disable the Apply button\r\n\t\tsetIsDisabled(true);\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\tconst handleEditClick = (id: any) => {\r\n\t\teditInteractionName = id;\r\n\t\tsetCheckPointsEditPopup(true);\r\n\t};\r\n\tconst handleDeleteClick = (interaction: any) => {\r\n\t\tsetChecklistCheckpointProperties((prevState: any) => ({\r\n\t\t\t...prevState,\r\n\t\t\tcheckpointsList: prevState.checkpointsList.filter((checkpoint: any) => checkpoint.interaction !== interaction),\r\n\t\t}));\r\n\t\tsetHasChanges(true);\r\n\t\tdeleteCheckpoint(interaction);\r\n\t};\r\n\tlet checkpoints = checklistCheckpointProperties.checkpointsList;\r\n\tconst [draggedItemIndex, setDraggedItemIndex] = useState<number | null>(null);\r\n\r\n\t// Handle drag start\r\n\tconst handleDragStart = (index: number) => {\r\n\t\tsetDraggedItemIndex(index);\r\n\t};\r\n\r\n\t// Handle drag over (allows dropping)\r\n\tconst handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {\r\n\t\tevent.preventDefault();\r\n\t};\r\n\r\n\t// Handle drop (reorder items)\r\n\tconst handleDrop = (index: number) => {\r\n\t\tif (draggedItemIndex === null || draggedItemIndex === index) return;\r\n\r\n\t\tconst updatedCheckpoints = [...checkpoints];\r\n\t\tconst [draggedItem] = updatedCheckpoints.splice(draggedItemIndex, 1);\r\n\t\tupdatedCheckpoints.splice(index, 0, draggedItem);\r\n\t\tsetKKK(true);\r\n\t\tcheckpoints = updatedCheckpoints;\r\n\t\tsetDraggedItemIndex(null);\r\n\r\n\t\t// Update state correctly and mark changes\r\n\t\tsetChecklistCheckpointProperties((prevState: any) => ({\r\n\t\t\t...prevState, // Copy the existing state\r\n\t\t\tcheckpointsList: updatedCheckpoints, // Update only the checkpointsList\r\n\t\t}));\r\n\t\tsetHasChanges(true);\r\n\t};\r\n\r\n\tconst handleAddCheckpoint = () => {\r\n\t\tsetCheckPointsAddPopup(true);\r\n\t};\r\n\r\n\tconst handleMessageChange = (e: any) => {\r\n\t\tconst value = e.target.value;\r\n\t\tlet errorMessage = \"\";\r\n\r\n\t\tif (value.length < 2) {\r\n\t\t\terrorMessage = translate(\"Min: 2 Characters\");\r\n\t\t} else if (value.length > 30) {\r\n\t\t\terrorMessage = translate(\"Max: 30 Characters\");\r\n\t\t}\r\n\r\n\t\tsetMessageError(errorMessage);\r\n\t\tonPropertyChange(\"message\", value);\r\n\t};\r\n\treturn (\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Steps\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t<div className=\"qadpt-controls qadpt-errmsg\">\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\tpadding: \"10px\",\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t\t\t{checkpoints.map((checkpoint: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t<DraggableCheckpoint\r\n\t\t\t\t\t\t\t\t\t\tkey={checkpoint.id}\r\n\t\t\t\t\t\t\t\t\t\tcheckpoint={checkpoint}\r\n\t\t\t\t\t\t\t\t\t\tindex={index}\r\n\t\t\t\t\t\t\t\t\t\thandleEditClick={() => handleEditClick(checkpoint?.id)}\r\n\t\t\t\t\t\t\t\t\t\thandleDeleteClick={() => handleDeleteClick(checkpoint?.interaction)}\r\n\t\t\t\t\t\t\t\t\t\thandleDragStart={handleDragStart}\r\n\t\t\t\t\t\t\t\t\t\thandleDragOver={handleDragOver}\r\n\t\t\t\t\t\t\t\t\t\thandleDrop={handleDrop}\r\n\t\t\t\t\t\t\t\t\t\tisDragging={index === draggedItemIndex}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tonClick={handleAddCheckpoint}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-memberButton qadpt-check\"\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#D3D9DA\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor) !important\",\r\n\t\t\t\t\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<AddIcon  />\r\n\t\t\t\t\t\t\t\t{/* <i className=\"fal fa-add-plus\"></i> */}\r\n\t\t\t\t\t\t\t\t<span>{translate(\"Add Step\")}</span>\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Step Title\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.checkpointTitles}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"checkpointTitles\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Step Description\")}</div>\r\n\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties?.checkpointsDescription}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"checkpointsDescription\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Icon Color\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties?.checkpointsIcons}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t// Update the global Icons color\r\n\t\t\t\t\t\t\t\t\t\tonPropertyChange(\"checkpointsIcons\", e.target.value);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\tsx={{ height: \"auto !important\", flexDirection: \"column\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Unlock Checkpoints in Order\")}</div>\r\n\r\n\t\t\t\t\t\t\t\t{/* Show by Default Toggle */}\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n\t\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\t\ttype=\"checkbox\"\r\n\t\t\t\t\t\t\t\t\t\t\tchecked={checklistCheckpointProperties.unlockCHeckpointInOrder}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"unlockCHeckpointInOrder\", e.target.checked)}\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"showByDefault\"\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t{checklistCheckpointProperties.unlockCHeckpointInOrder === true && (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"var(--button-border-radius)\",\r\n\t\t\t\t\t\t\t\t\t\t\theight: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px 8px 0 8px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ paddingBottom: \"5px\", textAlign: \"left\" }}>{translate(\"Message\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\", height: \"auto !important\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Checklist Title\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointProperties.message}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleMessageChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\terror={Boolean(messageError)} // Show error if message exists\r\n\t\t\t\t\t\t\t\t\t\t\t\thelperText={\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmessageError ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ marginRight: \"4px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{messageError}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t) : null\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\", paddingLeft: \"10px !important\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\": { height: \"auto !important\", marginBottom: \"5px\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\tclassName={`qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\tdisabled={isDisabled}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t{checkpointsEditPopup && (\r\n\t\t\t\t<>\r\n\t\t\t\t\t<CheckPointEditPopup\r\n\t\t\t\t\t\tcheckpointsEditPopup={checkpointsEditPopup}\r\n\t\t\t\t\t\teditInteractionName={editInteractionName}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</>\r\n\t\t\t)}\r\n\r\n\t\t\t{checkpointsAddPopup && (\r\n\t\t\t\t<>\r\n\t\t\t\t\t<CheckPointAddPopup\r\n\t\t\t\t\t\tchecklistCheckpointProperties={checklistCheckpointProperties}\r\n\t\t\t\t\t\tsetChecklistCheckpointProperties={setChecklistCheckpointProperties}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default Checkpoints;\r\nexport {editInteractionName};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAgBC,QAAQ,EAACC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,OAAO;AACjF,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAEC,MAAM,QAAyH,eAAe;AACrM,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAA6E,yBAAyB;AAE3H,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SASCC,OAAO,QACD,0BAA0B;AACjC,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,kBAAkB,MAAM,sBAAsB;AAErD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,IAAIC,mBAA2B;AAC/B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,sBAAA;EACzB,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGV,cAAc,CAAC,CAAC;EACzC,MAAM;IACLW,oBAAoB;IACpBC,uBAAuB;IACvBC,uBAAuB;IACvBC,oBAAoB;IACpBC,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,UAAU;IACVC,aAAa;IACbC,gBAAgB;IAChBC,mBAAmB;IACnBC,oBAAoB;IACpBC,uBAAuB;IACvBC,0BAA0B;IAC1BC,6BAA6B;IAC7BC,mBAAmB;IACnBC,sBAAsB;IACtBC,0BAA0B;IAC1BC,uBAAuB;IACvBC,iBAAiB;IACjBC,oBAAoB;IACpBC,sBAAsB;IACtBC,mBAAmB;IACnBC,sBAAsB;IACtBC,0BAA0B;IAC1BC,gBAAgB;IAChBC,mBAAmB;IACnBC;EACD,CAAC,GAAG9C,cAAc,CAAE+C,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM;IAAEC;EAAU,CAAC,GAAGxD,UAAU,CAACe,cAAc,CAAC;EAChD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAAC6D,6BAA6B,EAAEC,gCAAgC,CAAC,GAAG9D,QAAQ,CAAM,MAAM;IAAA,IAAA+D,qBAAA;IAC7F,MAAMC,oCAAoC,GAAG,EAAAD,qBAAA,GAAAX,sBAAsB,CAAC,CAAC,CAAC,cAAAW,qBAAA,uBAAzBA,qBAAA,CAA2BE,WAAW,KAAI;MACtFC,eAAe,EAAE,EAAE;MACnBC,gBAAgB,EAAE,MAAM;MACxBC,sBAAsB,EAAE,SAAS;MACjCC,gBAAgB,EAAE,MAAM;MACxBC,uBAAuB,EAAE,KAAK;MAC9BC,OAAO,EAAE;IACV,CAAC;IACD,OAAOP,oCAAoC;EAC5C,CAAC,CAAC;EACF;EACA,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC0E,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC6D,6BAA6B,CAAC;;EAE/E;EACA5D,SAAS,CAAC,MAAM;IAAA,IAAA6E,sBAAA;IACf,KAAAA,sBAAA,GAAI1B,sBAAsB,CAAC,CAAC,CAAC,cAAA0B,sBAAA,eAAzBA,sBAAA,CAA2Bb,WAAW,EAAE;MAC3C,MAAMc,cAAc,GAAG3B,sBAAsB,CAAC,CAAC,CAAC,CAACa,WAAW;MAC5DH,gCAAgC,CAACiB,cAAc,CAAC;MAChDF,eAAe,CAACE,cAAc,CAAC;MAC/BJ,aAAa,CAAC,KAAK,CAAC;MACpBF,aAAa,CAAC,IAAI,CAAC;IACpB;EACD,CAAC,EAAE,EAAA/C,sBAAA,GAAC0B,sBAAsB,CAAC,CAAC,CAAC,cAAA1B,sBAAA,uBAAzBA,sBAAA,CAA2BuC,WAAW,CAAC,CAAC;EAC5C;EACA,MAAMe,sBAAsB,GAAGA,CAACC,OAAgB,EAAEC,SAAkB,GAAG,KAAK,KAAK;IAChFT,aAAa,CAAC,CAACQ,OAAO,IAAIC,SAAS,CAAC;EACrC,CAAC;;EAED;;EAEA;EACAjF,SAAS,CAAC,MAAM;IACf;IACA,MAAMkF,aAAa,GAAGC,IAAI,CAACC,SAAS,CAACxB,6BAA6B,CAAC,KAAKuB,IAAI,CAACC,SAAS,CAACT,YAAY,CAAC;IACpGD,aAAa,CAACQ,aAAa,CAAC;;IAE5B;IACA,MAAMG,mBAAmB,GAAG,CAAC,CAAC3B,YAAY;IAE1CqB,sBAAsB,CAACG,aAAa,EAAEG,mBAAmB,CAAC;EAC3D,CAAC,EAAE,CAACzB,6BAA6B,EAAEe,YAAY,EAAEjB,YAAY,CAAC,CAAC;EAC/D,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAQ,EAAE,CAAC;EAC3D,MAAM,CAACyF,IAAI,EAAEC,OAAO,CAAC,GAAG1F,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM2F,GAAG,GAAG,CAAC;EACb,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM8F,WAAW,GAAG3F,MAAM,CAAiB,IAAI,CAAC;EAEhD,MAAM4F,WAAW,GAAGA,CAAA,KAAM;IACzBxD,mBAAmB,CAAC,KAAK,CAAC;EAC3B,CAAC;EACD,MAAMyD,iBAAiB,GAAGA,CAAA,KAAM;IAC/B7D,cAAc,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAM8D,iBAAiB,GAAGA,CAAA,KAAM,CAAC,CAAC;EAElC,MAAMC,gBAAgB,GAAGA,CAACC,GAAQ,EAAEC,KAAU,KAAK;IAClDtC,gCAAgC,CAAEuC,SAAc,IAAK;MACpD,MAAMC,QAAQ,GAAG;QAChB,GAAGD,SAAS;QACZ,CAACF,GAAG,GAAGC;MACR,CAAC;MACD;MACAzB,aAAa,CAAC,IAAI,CAAC;MACnB,OAAO2B,QAAQ;IAChB,CAAC,CAAC;EACH,CAAC;EACD,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;EACrC,MAAMyG,kBAAkB,GAAGA,CAAA,KAAM;IAChC,IAAIF,GAAG,IAAI,KAAK,EAAE;MACjB;MACA1C,6BAA6B,CAACK,eAAe,GAAGd,sBAAsB,CAAC,CAAC,CAAC,CAACa,WAAW,CAACC,eAAe;IACtG;IAEAb,0BAA0B,CAACQ,6BAA6B,CAAC;IACzD;IACAgB,eAAe,CAAC;MAAE,GAAGhB;IAA8B,CAAC,CAAC;IACrD;IACAc,aAAa,CAAC,KAAK,CAAC;IACpB;IACAF,aAAa,CAAC,IAAI,CAAC;IACnBsB,WAAW,CAAC,CAAC;IACbxC,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMmD,eAAe,GAAIC,EAAO,IAAK;IACpCpF,mBAAmB,GAAGoF,EAAE;IACxB5E,uBAAuB,CAAC,IAAI,CAAC;EAC9B,CAAC;EACD,MAAM6E,iBAAiB,GAAIC,WAAgB,IAAK;IAC/C/C,gCAAgC,CAAEuC,SAAc,KAAM;MACrD,GAAGA,SAAS;MACZnC,eAAe,EAAEmC,SAAS,CAACnC,eAAe,CAAC4C,MAAM,CAAEC,UAAe,IAAKA,UAAU,CAACF,WAAW,KAAKA,WAAW;IAC9G,CAAC,CAAC,CAAC;IACHlC,aAAa,CAAC,IAAI,CAAC;IACnBrB,gBAAgB,CAACuD,WAAW,CAAC;EAC9B,CAAC;EACD,IAAI5C,WAAW,GAAGJ,6BAA6B,CAACK,eAAe;EAC/D,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjH,QAAQ,CAAgB,IAAI,CAAC;;EAE7E;EACA,MAAMkH,eAAe,GAAIC,KAAa,IAAK;IAC1CF,mBAAmB,CAACE,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,KAAsC,IAAK;IAClEA,KAAK,CAACC,cAAc,CAAC,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIJ,KAAa,IAAK;IACrC,IAAIH,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAKG,KAAK,EAAE;IAE7D,MAAMK,kBAAkB,GAAG,CAAC,GAAGvD,WAAW,CAAC;IAC3C,MAAM,CAACwD,WAAW,CAAC,GAAGD,kBAAkB,CAACE,MAAM,CAACV,gBAAgB,EAAE,CAAC,CAAC;IACpEQ,kBAAkB,CAACE,MAAM,CAACP,KAAK,EAAE,CAAC,EAAEM,WAAW,CAAC;IAChDjB,MAAM,CAAC,IAAI,CAAC;IACZvC,WAAW,GAAGuD,kBAAkB;IAChCP,mBAAmB,CAAC,IAAI,CAAC;;IAEzB;IACAnD,gCAAgC,CAAEuC,SAAc,KAAM;MACrD,GAAGA,SAAS;MAAE;MACdnC,eAAe,EAAEsD,kBAAkB,CAAE;IACtC,CAAC,CAAC,CAAC;IACH7C,aAAa,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMgD,mBAAmB,GAAGA,CAAA,KAAM;IACjCzE,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM0E,mBAAmB,GAAIC,CAAM,IAAK;IACvC,MAAMzB,KAAK,GAAGyB,CAAC,CAACC,MAAM,CAAC1B,KAAK;IAC5B,IAAI2B,YAAY,GAAG,EAAE;IAErB,IAAI3B,KAAK,CAAC4B,MAAM,GAAG,CAAC,EAAE;MACrBD,YAAY,GAAGnG,SAAS,CAAC,mBAAmB,CAAC;IAC9C,CAAC,MAAM,IAAIwE,KAAK,CAAC4B,MAAM,GAAG,EAAE,EAAE;MAC7BD,YAAY,GAAGnG,SAAS,CAAC,oBAAoB,CAAC;IAC/C;IAEAgC,eAAe,CAACmE,YAAY,CAAC;IAC7B7B,gBAAgB,CAAC,SAAS,EAAEE,KAAK,CAAC;EACnC,CAAC;EACD,oBACChF,OAAA;IACCuF,EAAE,EAAC,mBAAmB;IACtBsB,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAE7B9G,OAAA;MAAK6G,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7B9G,OAAA;QAAK6G,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnC9G,OAAA,CAACb,UAAU;UACV,cAAW,MAAM;UACjB4H,OAAO,EAAEpC,WAAY;UAAAmC,QAAA,eAErB9G,OAAA,CAACP,2BAA2B;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACbnH,OAAA;UAAK6G,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEtG,SAAS,CAAC,OAAO;QAAC;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvDnH,OAAA,CAACb,UAAU;UACViI,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBL,OAAO,EAAEpC,WAAY;UAAAmC,QAAA,eAErB9G,OAAA,CAACX,SAAS;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNnH,OAAA;QAAK6G,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC9B9G,OAAA;UAAK6G,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC3C9G,OAAA,CAAChB,GAAG;YACHqI,EAAE,EAAE;cACHC,eAAe,EAAE,SAAS;cAC1BC,YAAY,EAAE,6BAA6B;cAC3CC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,MAAM;cACfC,YAAY,EAAE;YACf,CAAE;YAAAZ,QAAA,gBAEF9G,OAAA,CAAChB,GAAG;cAAA8H,QAAA,EACFjE,WAAW,CAAC8E,GAAG,CAAC,CAAChC,UAAe,EAAEI,KAAa,kBAC/C/F,OAAA,CAACT,mBAAmB;gBAEnBoG,UAAU,EAAEA,UAAW;gBACvBI,KAAK,EAAEA,KAAM;gBACbT,eAAe,EAAEA,CAAA,KAAMA,eAAe,CAACK,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEJ,EAAE,CAAE;gBACvDC,iBAAiB,EAAEA,CAAA,KAAMA,iBAAiB,CAACG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEF,WAAW,CAAE;gBACpEK,eAAe,EAAEA,eAAgB;gBACjCE,cAAc,EAAEA,cAAe;gBAC/BG,UAAU,EAAEA,UAAW;gBACvByB,UAAU,EAAE7B,KAAK,KAAKH;cAAiB,GARlCD,UAAU,CAACJ,EAAE;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASlB,CACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNnH,OAAA;cACC+G,OAAO,EAAER,mBAAoB;cAC7BM,SAAS,EAAC,gCAAgC;cAC1CgB,KAAK,EAAE;gBACNC,KAAK,EAAE,MAAM;gBACbR,eAAe,EAAE,SAAS;gBAC1BS,KAAK,EAAE,gCAAgC;gBACvCC,YAAY,EAAE;cACf,CAAE;cAAAlB,QAAA,gBAEF9G,OAAA,CAACL,OAAO;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAEZnH,OAAA;gBAAA8G,QAAA,EAAOtG,SAAS,CAAC,UAAU;cAAC;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENnH,OAAA,CAAChB,GAAG;YAAC6H,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjC9G,OAAA;cAAK6G,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEtG,SAAS,CAAC,YAAY;YAAC;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEnH,OAAA;cAAA8G,QAAA,eACC9G,OAAA;gBACCiI,IAAI,EAAC,OAAO;gBACZjD,KAAK,EAAEvC,6BAA6B,CAACM,gBAAiB;gBACtDmF,QAAQ,EAAGzB,CAAC,IAAK3B,gBAAgB,CAAC,kBAAkB,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;gBACtE6B,SAAS,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENnH,OAAA,CAAChB,GAAG;YAAC6H,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjC9G,OAAA;cAAK6G,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEtG,SAAS,CAAC,kBAAkB;YAAC;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAE1EnH,OAAA;cAAA8G,QAAA,eACC9G,OAAA;gBACCiI,IAAI,EAAC,OAAO;gBACZjD,KAAK,EAAEvC,6BAA6B,aAA7BA,6BAA6B,uBAA7BA,6BAA6B,CAAEO,sBAAuB;gBAC7DkF,QAAQ,EAAGzB,CAAC,IAAK3B,gBAAgB,CAAC,wBAAwB,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAE;gBAC5E6B,SAAS,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENnH,OAAA,CAAChB,GAAG;YAAC6H,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjC9G,OAAA;cAAK6G,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAEtG,SAAS,CAAC,YAAY;YAAC;cAAAwG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpEnH,OAAA;cAAA8G,QAAA,eACC9G,OAAA;gBACCiI,IAAI,EAAC,OAAO;gBACZjD,KAAK,EAAEvC,6BAA6B,aAA7BA,6BAA6B,uBAA7BA,6BAA6B,CAAEQ,gBAAiB;gBACvDiF,QAAQ,EAAGzB,CAAC,IAAK;kBAChB;kBACA3B,gBAAgB,CAAC,kBAAkB,EAAE2B,CAAC,CAACC,MAAM,CAAC1B,KAAK,CAAC;gBACrD,CAAE;gBACF6B,SAAS,EAAC;cAAmB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENnH,OAAA,CAAChB,GAAG;YACH6H,SAAS,EAAC,mBAAmB;YAC7BQ,EAAE,EAAE;cAAEG,MAAM,EAAE,iBAAiB;cAAEW,aAAa,EAAE;YAAS,CAAE;YAAArB,QAAA,gBAE3D9G,OAAA;cAAK6H,KAAK,EAAE;gBAAEO,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAvB,QAAA,gBACrD9G,OAAA;gBAAK6G,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtG,SAAS,CAAC,6BAA6B;cAAC;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAGrFnH,OAAA;gBAAA8G,QAAA,eACC9G,OAAA;kBAAO6G,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC/B9G,OAAA;oBACCiI,IAAI,EAAC,UAAU;oBACfK,OAAO,EAAE7F,6BAA6B,CAACS,uBAAwB;oBAC/DgF,QAAQ,EAAGzB,CAAC,IAAK3B,gBAAgB,CAAC,yBAAyB,EAAE2B,CAAC,CAACC,MAAM,CAAC4B,OAAO,CAAE;oBAC/EC,IAAI,EAAC;kBAAe;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACFnH,OAAA;oBAAM6G,SAAS,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EACL1E,6BAA6B,CAACS,uBAAuB,KAAK,IAAI,iBAC9DlD,OAAA,CAAAE,SAAA;cAAA4G,QAAA,eACC9G,OAAA,CAAChB,GAAG;gBACHqI,EAAE,EAAE;kBACHC,eAAe,EAAE,SAAS;kBAC1BC,YAAY,EAAE,6BAA6B;kBAC3CC,MAAM,EAAE,MAAM;kBACdC,OAAO,EAAE;gBACV,CAAE;gBAAAX,QAAA,gBAEF9G,OAAA,CAACf,UAAU;kBAACoI,EAAE,EAAE;oBAAEmB,aAAa,EAAE,KAAK;oBAAEC,SAAS,EAAE;kBAAO,CAAE;kBAAA3B,QAAA,EAAEtG,SAAS,CAAC,SAAS;gBAAC;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAEhGnH,OAAA,CAAChB,GAAG;kBACH6H,SAAS,EAAC,mBAAmB;kBAC7BQ,EAAE,EAAE;oBAAEI,OAAO,EAAE,cAAc;oBAAED,MAAM,EAAE;kBAAkB,CAAE;kBAAAV,QAAA,eAE3D9G,OAAA,CAACd,SAAS;oBACTwJ,OAAO,EAAC,UAAU;oBAClBtB,IAAI,EAAC,OAAO;oBACZuB,WAAW,EAAEnI,SAAS,CAAC,iBAAiB,CAAE;oBAC1CqG,SAAS,EAAC,qBAAqB;oBAC/B7B,KAAK,EAAEvC,6BAA6B,CAACU,OAAQ;oBAC7C0E,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBACzBI,QAAQ,EAAE1B,mBAAoB;oBAC9BoC,KAAK,EAAEC,OAAO,CAACtG,YAAY,CAAE,CAAC;oBAAA;oBAC9BuG,UAAU,EACTvG,YAAY,gBACXvC,OAAA;sBAAM6H,KAAK,EAAE;wBAAEO,OAAO,EAAE,MAAM;wBAAEW,QAAQ,EAAE,MAAM;wBAAEV,UAAU,EAAE;sBAAS,CAAE;sBAAAvB,QAAA,gBACxE9G,OAAA;wBACC6H,KAAK,EAAE;0BAAEmB,WAAW,EAAE;wBAAM,CAAE;wBAC9BC,uBAAuB,EAAE;0BAAEC,MAAM,EAAE1J;wBAAQ;sBAAE;wBAAAwH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7C,CAAC,EACD5E,YAAY;oBAAA;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,GACJ,IACJ;oBACDgC,UAAU,EAAE;sBACXC,YAAY,EAAE,EAAE;sBAChB/B,EAAE,EAAE;wBACH,0CAA0C,EAAE;0BAAEgC,MAAM,EAAE;wBAAO,CAAC;wBAC9D,gDAAgD,EAAE;0BAAEA,MAAM,EAAE;wBAAO,CAAC;wBACpE,YAAY,EAAE;0BAAEA,MAAM,EAAE;wBAAO,CAAC;wBAChC,SAAS,EAAE;0BAAEZ,SAAS,EAAE,iBAAiB;0BAAEa,WAAW,EAAE;wBAAkB,CAAC;wBAC3E,qBAAqB,EAAE;0BAAE9B,MAAM,EAAE,iBAAiB;0BAAEE,YAAY,EAAE;wBAAM;sBACzE;oBACD;kBAAE;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC,gBACL,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNnH,OAAA;QAAK6G,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAClC9G,OAAA,CAACZ,MAAM;UACNsJ,OAAO,EAAC,WAAW;UACnB3B,OAAO,EAAE1B,kBAAmB;UAC5BwB,SAAS,EAAE,aAAazD,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;UACvDmG,QAAQ,EAAEnG,UAAW;UAAA0D,QAAA,EAEpBtG,SAAS,CAAC,OAAO;QAAC;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAELvG,oBAAoB,iBACpBZ,OAAA,CAAAE,SAAA;MAAA4G,QAAA,eACC9G,OAAA,CAACN,mBAAmB;QACnBkB,oBAAoB,EAAEA,oBAAqB;QAC3CT,mBAAmB,EAAEA;MAAoB;QAAA6G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IAAC,gBACD,CACF,EAEApF,mBAAmB,iBACnB/B,OAAA,CAAAE,SAAA;MAAA4G,QAAA,eACC9G,OAAA,CAACJ,kBAAkB;QAClB6C,6BAA6B,EAAEA,6BAA8B;QAC7DC,gCAAgC,EAAEA;MAAiC;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IAAC,gBACD,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAER,CAAC;AAAC9G,EAAA,CAxYID,WAAW;EAAA,QACSN,cAAc,EA8BnCR,cAAc;AAAA;AAAAkK,EAAA,GA/BbpJ,WAAW;AA0YjB,eAAeA,WAAW;AAC1B,SAAQD,mBAAmB;AAAE,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}