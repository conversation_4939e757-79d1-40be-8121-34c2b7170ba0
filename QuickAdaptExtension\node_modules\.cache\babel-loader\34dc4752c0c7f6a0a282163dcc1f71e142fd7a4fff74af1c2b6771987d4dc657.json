{"ast": null, "code": "import { adminApiService } from \"../services/APIService\";\nexport const getLanguages = async () => {\n  try {\n    const response = await adminApiService.get(\"/Translation/GetLanguages\");\n    return Array.isArray(response.data) ? response.data : [];\n  } catch (error) {\n    console.error(\"Error fetching languages:\", error);\n    return [];\n  }\n};\nexport const getLabels = async (language, isMobile = false) => {\n  try {\n    const response = await adminApiService.get(\"/Translation/GetAllLabels\", {\n      params: {\n        language,\n        isMobile\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error(\"Error fetching translation labels:\", error);\n    throw error;\n  }\n};\nexport const updateLanguage = async language => {\n  try {\n    const response = await adminApiService.get(\"/Translation/UpdateUserLanguage\", {\n      params: {\n        language\n      }\n    });\n    return response.data;\n  } catch (error) {\n    console.error(\"Error fetching translation labels:\", error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["adminApiService", "getLanguages", "response", "get", "Array", "isArray", "data", "error", "console", "<PERSON><PERSON><PERSON><PERSON>", "language", "isMobile", "params", "updateLanguage"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/multilinguial/LanguageService.ts"], "sourcesContent": ["\r\nimport { adminApiService } from \"../services/APIService\";\r\n\r\n\r\nexport type LanguageType = {\r\n  LanguageId: string;\r\n  Language: string;\r\n  LanguageCode: string;\r\n  FlagIcon: string;\r\n};\r\n\r\n\r\nexport const getLanguages = async (): Promise<LanguageType[]> => {\r\n  try {\r\n    const response = await adminApiService.get(\"/Translation/GetLanguages\");\r\n    return Array.isArray(response.data) ? response.data : [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching languages:\", error);\r\n    return [];\r\n  }\r\n};\r\n\r\n\r\nexport const getLabels = async (language: string, isMobile: boolean = false) => {\r\n  try {\r\n    const response = await adminApiService.get(\"/Translation/GetAllLabels\", {\r\n      params: { language, isMobile },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching translation labels:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const updateLanguage = async (language: string) => {\r\n  try {\r\n    const response = await adminApiService.get(\"/Translation/UpdateUserLanguage\", {\r\n      params: { language },\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching translation labels:\", error);\r\n    throw error;\r\n  }\r\n};\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,wBAAwB;AAWxD,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAqC;EAC/D,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMF,eAAe,CAACG,GAAG,CAAC,2BAA2B,CAAC;IACvE,OAAOC,KAAK,CAACC,OAAO,CAACH,QAAQ,CAACI,IAAI,CAAC,GAAGJ,QAAQ,CAACI,IAAI,GAAG,EAAE;EAC1D,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,EAAE;EACX;AACF,CAAC;AAGD,OAAO,MAAME,SAAS,GAAG,MAAAA,CAAOC,QAAgB,EAAEC,QAAiB,GAAG,KAAK,KAAK;EAC9E,IAAI;IACF,MAAMT,QAAQ,GAAG,MAAMF,eAAe,CAACG,GAAG,CAAC,2BAA2B,EAAE;MACtES,MAAM,EAAE;QAAEF,QAAQ;QAAEC;MAAS;IAC/B,CAAC,CAAC;IACF,OAAOT,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMM,cAAc,GAAG,MAAOH,QAAgB,IAAK;EACxD,IAAI;IACF,MAAMR,QAAQ,GAAG,MAAMF,eAAe,CAACG,GAAG,CAAC,iCAAiC,EAAE;MAC5ES,MAAM,EAAE;QAAEF;MAAS;IACrB,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACI,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}