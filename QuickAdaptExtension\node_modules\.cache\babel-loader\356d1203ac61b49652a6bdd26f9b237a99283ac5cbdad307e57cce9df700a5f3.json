{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\common\\\\SelectImageFromApplication.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography } from \"@mui/material\";\nimport Modal from '@mui/material/Modal';\nimport { useTranslation } from 'react-i18next';\nimport { getAllFiles } from \"../../services/FileService\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SelectImageFromApplication = ({\n  isOpen,\n  handleModelClose,\n  onImageSelect,\n  handleImageUpload,\n  setFormOfUpload,\n  formOfUpload,\n  handleReplaceImage,\n  isReplaceImage\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [files, setFiles] = useState([]);\n  const getAllFilesData = async () => {\n    try {\n      const data = await getAllFiles();\n      if (data) {\n        const uploads = data.map(file => ({\n          ImageId: file.Id,\n          FileName: file.Name || null,\n          Url: file.Url || ''\n        }));\n        setFiles(uploads);\n      } else {}\n    } catch (error) {}\n  };\n  useEffect(() => {\n    getAllFilesData();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Modal, {\n      open: isOpen,\n      onClose: handleModelClose,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: \"absolute\",\n          top: \"50%\",\n          left: \"50%\",\n          transform: \"translate(-50%, -50%)\",\n          width: 450,\n          bgcolor: \"background.paper\",\n          boxShadow: 24,\n          p: 4,\n          maxHeight: \"400px\",\n          overflow: \"auto\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: translate(\"Select a File\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              flexWrap: \"wrap\"\n            },\n            children: files && files.map(file => {\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: \"100px\",\n                  height: \"100px\",\n                  mx: 2\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: file === null || file === void 0 ? void 0 : file.Url,\n                  style: {\n                    width: \"inherit\"\n                  },\n                  onClick: () => {\n                    onImageSelect(file);\n                  },\n                  alt: translate(\"Uploaded file image\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 12\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 11\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 5\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 3\n    }, this)\n  }, void 0, false);\n};\n_s(SelectImageFromApplication, \"lJmm0zHDqDpxlcAk4cBZDAghXSU=\", false, function () {\n  return [useTranslation];\n});\n_c = SelectImageFromApplication;\nexport default SelectImageFromApplication;\nvar _c;\n$RefreshReg$(_c, \"SelectImageFromApplication\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "Modal", "useTranslation", "getAllFiles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SelectImageFromApplication", "isOpen", "handleModelClose", "onImageSelect", "handleImageUpload", "setFormOfUpload", "formOfUpload", "handleReplaceImage", "isReplaceImage", "_s", "t", "translate", "files", "setFiles", "getAllFilesData", "data", "uploads", "map", "file", "ImageId", "Id", "FileName", "Name", "Url", "error", "children", "open", "onClose", "sx", "position", "top", "left", "transform", "width", "bgcolor", "boxShadow", "p", "maxHeight", "overflow", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexWrap", "height", "mx", "src", "style", "onClick", "alt", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/common/SelectImageFromApplication.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, Popover, IconButton, TextField, MenuItem, Button, Tooltip } from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport Modal from '@mui/material/Modal';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport DriveFolderUploadIcon from '@mui/icons-material/DriveFolderUpload';\r\nimport BackupIcon from '@mui/icons-material/Backup';\r\nimport { getAllFiles } from \"../../services/FileService\";\r\nimport { FileUpload } from \"../../models/FileUpload\";\r\n\r\n\r\n\r\nconst SelectImageFromApplication = ({ isOpen, handleModelClose, onImageSelect, handleImageUpload, setFormOfUpload, formOfUpload,handleReplaceImage, isReplaceImage }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\r\n\tconst [files, setFiles] = useState<FileUpload[]>([]);\r\n\t\r\n\r\n\tconst getAllFilesData = async () => {\r\n\t\ttry {\r\n\t\t\tconst data = await getAllFiles();\r\n\t\t\tif (data) {\r\n\t\t\t\tconst uploads: any = data.map((file:any) => ({\r\n\t\t\t\t\tImageId: file.Id, \r\n\t\t\t\t\tFileName: file.Name || null,\r\n\t\t\t\t\tUrl: file.Url || '',\r\n\t\t\t\t}));\r\n\t\t\t\tsetFiles(uploads);\r\n\t\t\t}else{\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t}\r\n\t}\r\n\r\n\tuseEffect(() => {\r\n\t\tgetAllFilesData();\r\n\t}, []);\r\n\r\n\treturn (<>\r\n\t\t<Modal open={isOpen} onClose={handleModelClose}>\r\n\t\t\t<Box sx={{\r\n\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\ttop: \"50%\",\r\n\t\t\t\t\tleft: \"50%\",\r\n\t\t\t\t\ttransform: \"translate(-50%, -50%)\",\r\n\t\t\t\t\twidth: 450,\r\n\t\t\t\t\tbgcolor: \"background.paper\",\r\n\t\t\t\t\tboxShadow: 24,\r\n\t\t\t\t\tp: 4,\r\n\t\t\t\t\tmaxHeight: \"400px\",\r\n\t\t\t\t\toverflow: \"auto\"\r\n\t\t\t\t}}>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"h6\">{translate(\"Select a File\")}</Typography>\r\n\t\t\t\t\t<Box sx={{ display: \"flex\", flexWrap: \"wrap\" }}>\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tfiles && (\r\n\t\t\t\t\t\t\t\tfiles.map((file) => {\r\n\t\t\t\t\t\t\t\t\treturn (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={{ width: \"100px\", height: \"100px\", mx: 2 }} >\r\n\t\t\t\t\t\t\t\t\t\t\t<img src={file?.Url} style={{ width: \"inherit\" }} onClick={() => { onImageSelect(file) }} alt={translate(\"Uploaded file image\")} />\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t</Box>\r\n\t\t\t</Box>\r\n\t\t</Modal>\r\n\t</>);\r\n}\r\n\r\n\r\nexport default SelectImageFromApplication;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,QAAmE,eAAe;AAG1G,OAAOC,KAAK,MAAM,qBAAqB;AACvC,SAASC,cAAc,QAAQ,eAAe;AAI9C,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAKzD,MAAMC,0BAA0B,GAAGA,CAAC;EAAEC,MAAM;EAAEC,gBAAgB;EAAEC,aAAa;EAAEC,iBAAiB;EAAEC,eAAe;EAAEC,YAAY;EAACC,kBAAkB;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EAC7K,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGjB,cAAc,CAAC,CAAC;EAEzC,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAe,EAAE,CAAC;EAGpD,MAAMwB,eAAe,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACH,MAAMC,IAAI,GAAG,MAAMpB,WAAW,CAAC,CAAC;MAChC,IAAIoB,IAAI,EAAE;QACT,MAAMC,OAAY,GAAGD,IAAI,CAACE,GAAG,CAAEC,IAAQ,KAAM;UAC5CC,OAAO,EAAED,IAAI,CAACE,EAAE;UAChBC,QAAQ,EAAEH,IAAI,CAACI,IAAI,IAAI,IAAI;UAC3BC,GAAG,EAAEL,IAAI,CAACK,GAAG,IAAI;QAClB,CAAC,CAAC,CAAC;QACHV,QAAQ,CAACG,OAAO,CAAC;MAClB,CAAC,MAAI,CACL;IACD,CAAC,CAAC,OAAOQ,KAAK,EAAE,CAChB;EACD,CAAC;EAEDnC,SAAS,CAAC,MAAM;IACfyB,eAAe,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN,oBAAQjB,OAAA,CAAAE,SAAA;IAAA0B,QAAA,eACP5B,OAAA,CAACJ,KAAK;MAACiC,IAAI,EAAEzB,MAAO;MAAC0B,OAAO,EAAEzB,gBAAiB;MAAAuB,QAAA,eAC9C5B,OAAA,CAACN,GAAG;QAACqC,EAAE,EAAE;UACPC,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,KAAK;UACXC,SAAS,EAAE,uBAAuB;UAClCC,KAAK,EAAE,GAAG;UACVC,OAAO,EAAE,kBAAkB;UAC3BC,SAAS,EAAE,EAAE;UACbC,CAAC,EAAE,CAAC;UACJC,SAAS,EAAE,OAAO;UAClBC,QAAQ,EAAE;QACX,CAAE;QAAAb,QAAA,eACF5B,OAAA,CAACN,GAAG;UAAAkC,QAAA,gBACH5B,OAAA,CAACL,UAAU;YAAC+C,OAAO,EAAC,IAAI;YAAAd,QAAA,EAAEd,SAAS,CAAC,eAAe;UAAC;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClE9C,OAAA,CAACN,GAAG;YAACqC,EAAE,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAApB,QAAA,EAE7Cb,KAAK,IACJA,KAAK,CAACK,GAAG,CAAEC,IAAI,IAAK;cACnB,oBACCrB,OAAA,CAACN,GAAG;gBAACqC,EAAE,EAAE;kBAAEK,KAAK,EAAE,OAAO;kBAAEa,MAAM,EAAE,OAAO;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAtB,QAAA,eACnD5B,OAAA;kBAAKmD,GAAG,EAAE9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,GAAI;kBAAC0B,KAAK,EAAE;oBAAEhB,KAAK,EAAE;kBAAU,CAAE;kBAACiB,OAAO,EAAEA,CAAA,KAAM;oBAAE/C,aAAa,CAACe,IAAI,CAAC;kBAAC,CAAE;kBAACiC,GAAG,EAAExC,SAAS,CAAC,qBAAqB;gBAAE;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/H,CAAC;YAER,CAAC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC,gBACP,CAAC;AACJ,CAAC;AAAAlC,EAAA,CA3DKT,0BAA0B;EAAA,QACNN,cAAc;AAAA;AAAA0D,EAAA,GADlCpD,0BAA0B;AA8DhC,eAAeA,0BAA0B;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}