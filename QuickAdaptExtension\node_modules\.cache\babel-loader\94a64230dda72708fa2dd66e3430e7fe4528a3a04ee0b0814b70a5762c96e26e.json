{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\AI\\\\StopScrapingButton.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './EnableAIButton.css';\nimport { useTranslation } from 'react-i18next';\nimport useDrawerStore from '../../store/drawerStore';\nimport AgentAdditionalContextPopup from './AgentAdditionalContextPopup';\nimport { cancelTraining } from '../../services/ScrapingService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StopScrapingButton = ({\n  onClick\n}) => {\n  _s();\n  const {\n    setIsAgentTraining,\n    isAgentTraining\n  } = useDrawerStore(state => state);\n  const {\n    t: translate\n  } = useTranslation();\n  const [showAdditionalContext, setShowAdditionalContext] = useState(false);\n  const handleClick = () => {\n    if (isAgentTraining) {\n      setShowAdditionalContext(true);\n    } else {\n      onClick();\n    }\n  };\n  const handleCancel = async () => {\n    try {\n      // Cancel the training process (stops scraping and clears data)\n      await cancelTraining();\n\n      // Update the training state\n      setIsAgentTraining(false);\n\n      // Close the popup\n      setShowAdditionalContext(false);\n    } catch (error) {\n      console.error('Error canceling training:', error);\n      // Still close the popup even if there's an error\n      setShowAdditionalContext(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"stop-scraping-button-container\",\n    id: \"stop-scraping-button\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"enable-ai-button stop-scraping-button\",\n      onClick: handleClick,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"enable-ai-text\",\n        children: translate(\"Stop Training\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), showAdditionalContext && isAgentTraining && /*#__PURE__*/_jsxDEV(AgentAdditionalContextPopup, {\n      open: showAdditionalContext,\n      onClose: () => setShowAdditionalContext(false),\n      onSaved: () => {\n        setShowAdditionalContext(false);\n        onClick();\n      },\n      onCancel: handleCancel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(StopScrapingButton, \"kvAp0V0T8mBf8sE7oMSDg1mBHS4=\", false, function () {\n  return [useDrawerStore, useTranslation];\n});\n_c = StopScrapingButton;\nexport default StopScrapingButton;\nvar _c;\n$RefreshReg$(_c, \"StopScrapingButton\");", "map": {"version": 3, "names": ["React", "useState", "useTranslation", "useDrawerStore", "AgentAdditionalContextPopup", "cancelTraining", "jsxDEV", "_jsxDEV", "StopScrapingButton", "onClick", "_s", "setIsAgentTraining", "isAgentTraining", "state", "t", "translate", "showAdditionalContext", "setShowAdditionalContext", "handleClick", "handleCancel", "error", "console", "className", "id", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "open", "onClose", "onSaved", "onCancel", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/AI/StopScrapingButton.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport './EnableAIButton.css';\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport AgentAdditionalContextPopup from './AgentAdditionalContextPopup';\r\nimport { cancelTraining } from '../../services/ScrapingService';\r\n\r\ninterface StopScrapingButtonProps {\r\n  onClick: () => void;\r\n}\r\n\r\nconst StopScrapingButton: React.FC<StopScrapingButtonProps> = ({ onClick }) => {\r\n  const {\r\n    setIsAgentTraining,\r\n    isAgentTraining\r\n  } = useDrawerStore((state: DrawerState) => state);\r\n  const { t: translate } = useTranslation();\r\n  const [showAdditionalContext, setShowAdditionalContext] = useState(false);\r\n\r\n  const handleClick = () => {\r\n    if(isAgentTraining){\r\n    setShowAdditionalContext(true);\r\n    }\r\n    else\r\n    {\r\n      onClick();\r\n    }\r\n  };\r\n\r\n  const handleCancel = async () => {\r\n    try {\r\n      // Cancel the training process (stops scraping and clears data)\r\n      await cancelTraining();\r\n\r\n      // Update the training state\r\n      setIsAgentTraining(false);\r\n\r\n      // Close the popup\r\n      setShowAdditionalContext(false);\r\n    } catch (error) {\r\n      console.error('Error canceling training:', error);\r\n      // Still close the popup even if there's an error\r\n      setShowAdditionalContext(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className='stop-scraping-button-container' id='stop-scraping-button'>\r\n      <button className=\"enable-ai-button stop-scraping-button\" onClick={handleClick}>\r\n        <span className=\"enable-ai-text\">{translate(\"Stop Training\")}</span>\r\n      </button>\r\n      {showAdditionalContext &&  isAgentTraining &&  (\r\n        <AgentAdditionalContextPopup\r\n          open={showAdditionalContext}\r\n          onClose={() => setShowAdditionalContext(false)}\r\n          onSaved={() => {\r\n            setShowAdditionalContext(false);\r\n            onClick();\r\n          }}\r\n          onCancel={handleCancel}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StopScrapingButton;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,sBAAsB;AAC7B,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,cAAc,MAAuB,yBAAyB;AACrE,OAAOC,2BAA2B,MAAM,+BAA+B;AACvE,SAASC,cAAc,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMhE,MAAMC,kBAAqD,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7E,MAAM;IACJC,kBAAkB;IAClBC;EACF,CAAC,GAAGT,cAAc,CAAEU,KAAkB,IAAKA,KAAK,CAAC;EACjD,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGb,cAAc,CAAC,CAAC;EACzC,MAAM,CAACc,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAEzE,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAGN,eAAe,EAAC;MACnBK,wBAAwB,CAAC,IAAI,CAAC;IAC9B,CAAC,MAED;MACER,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMU,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,MAAMd,cAAc,CAAC,CAAC;;MAEtB;MACAM,kBAAkB,CAAC,KAAK,CAAC;;MAEzB;MACAM,wBAAwB,CAAC,KAAK,CAAC;IACjC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;MACAH,wBAAwB,CAAC,KAAK,CAAC;IACjC;EACF,CAAC;EAED,oBACEV,OAAA;IAAKe,SAAS,EAAC,gCAAgC;IAACC,EAAE,EAAC,sBAAsB;IAAAC,QAAA,gBACvEjB,OAAA;MAAQe,SAAS,EAAC,uCAAuC;MAACb,OAAO,EAAES,WAAY;MAAAM,QAAA,eAC7EjB,OAAA;QAAMe,SAAS,EAAC,gBAAgB;QAAAE,QAAA,EAAET,SAAS,CAAC,eAAe;MAAC;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D,CAAC,EACRZ,qBAAqB,IAAKJ,eAAe,iBACxCL,OAAA,CAACH,2BAA2B;MAC1ByB,IAAI,EAAEb,qBAAsB;MAC5Bc,OAAO,EAAEA,CAAA,KAAMb,wBAAwB,CAAC,KAAK,CAAE;MAC/Cc,OAAO,EAAEA,CAAA,KAAM;QACbd,wBAAwB,CAAC,KAAK,CAAC;QAC/BR,OAAO,CAAC,CAAC;MACX,CAAE;MACFuB,QAAQ,EAAEb;IAAa;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClB,EAAA,CArDIF,kBAAqD;EAAA,QAIrDL,cAAc,EACOD,cAAc;AAAA;AAAA+B,EAAA,GALnCzB,kBAAqD;AAuD3D,eAAeA,kBAAkB;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}