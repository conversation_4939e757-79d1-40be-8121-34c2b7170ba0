{"ast": null, "code": "import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\nimport { getLabels, getLanguages } from './LanguageService';\n\n// Constants\nconst DEFAULT_LANGUAGE = 'en';\nconst FALLBACK_LANGUAGE = 'en';\nconst LANGUAGE_STORAGE_KEY_PREFIX = 'quickadapt_language_';\n\n// Internal state\nlet availableLanguages = [];\nlet initialized = false;\nlet isLoggedIn = false;\nlet currentOrgId = undefined;\n\n// Get organization-specific language key\nconst getOrgLanguageKey = orgId => {\n  return `${LANGUAGE_STORAGE_KEY_PREFIX}${orgId || 'default'}`;\n};\n\n// Language persistence utilities\nconst getSavedLanguage = orgId => {\n  try {\n    const key = getOrgLanguageKey(orgId || currentOrgId);\n    return localStorage.getItem(key) || DEFAULT_LANGUAGE;\n  } catch {\n    return DEFAULT_LANGUAGE;\n  }\n};\nconst saveLanguage = (language, orgId) => {\n  try {\n    const key = getOrgLanguageKey(orgId || currentOrgId);\n    localStorage.setItem(key, language);\n    console.log(`💾 Saved language ${language} for org ${orgId || currentOrgId}`);\n  } catch (err) {\n    console.error(\"Failed to save language preference:\", err);\n  }\n};\nconst clearSavedLanguage = orgId => {\n  try {\n    const key = getOrgLanguageKey(orgId || currentOrgId);\n    localStorage.removeItem(key);\n    console.log(`🗑️ Cleared saved language for org ${orgId || currentOrgId}`);\n  } catch (err) {\n    console.error(\"Failed to clear language preference:\", err);\n  }\n};\n\n// Custom backend to load translations from API\nclass CustomBackend {\n  init() {}\n  async read(language, namespace, callback) {\n    console.log(`🔍 CustomBackend.read() called with language: ${language}, namespace: ${namespace}`);\n    try {\n      const langCode = language.toLowerCase();\n\n      // For English, return empty object (keys will be used as values)\n      if (langCode === 'en') {\n        console.log('🚀 English detected - returning empty translations');\n        callback(null, {});\n        return;\n      }\n\n      // For non-English, check if user is logged in\n      if (!isLoggedIn) {\n        console.log('⚠️ User not logged in - returning empty translations');\n        callback(null, {});\n        return;\n      }\n\n      // Load available languages if not already loaded\n      if (availableLanguages.length === 0) {\n        console.log('🔄 Loading available languages...');\n        try {\n          availableLanguages = await getLanguages();\n          console.log(`✅ Loaded ${availableLanguages.length} available languages`);\n        } catch (error) {\n          console.error('❌ Failed to load available languages:', error);\n          callback(null, {});\n          return;\n        }\n      }\n\n      // Find the language metadata\n      const langMeta = availableLanguages.find(l => l.LanguageCode.toLowerCase() === langCode);\n      if (!langMeta) {\n        console.warn(`⚠️ Language code ${langCode} not found in available languages`);\n        callback(null, {});\n        return;\n      }\n\n      // Load translations from API\n      console.log(`🌐 Loading translations for ${langMeta.Language} (${langCode})`);\n      const response = await getLabels(langMeta.Language);\n      const translations = response[langCode] || response;\n      console.log(`✅ Loaded ${Object.keys(translations || {}).length} translations for ${langCode}`);\n      callback(null, translations || {});\n    } catch (err) {\n      console.error(`❌ Error loading translations for ${language}:`, err);\n      callback(null, {}); // Return empty object on error to prevent crashes\n    }\n  }\n}\n\n// Initialize i18n (call this once from App.tsx)\nCustomBackend.type = 'backend';\nexport const initializeI18n = async () => {\n  if (initialized) return;\n  await i18n.use(CustomBackend).use(initReactI18next).init({\n    lng: DEFAULT_LANGUAGE,\n    // Start with default language, will be set from user profile\n    fallbackLng: FALLBACK_LANGUAGE,\n    interpolation: {\n      escapeValue: false\n    },\n    react: {\n      useSuspense: false\n    },\n    backend: {\n      crossDomain: true\n    }\n  });\n  initialized = true;\n  console.log('✅ i18n initialized successfully');\n};\n\n// Load saved language translations after login\nexport const loadSavedLanguageTranslations = async orgId => {\n  if (!isLoggedIn) {\n    console.log('⏳ User not logged in - skipping language load');\n    return;\n  }\n\n  // Note: Language is now set from user profile instead of localStorage\n  console.log('ℹ️ Language will be set from user profile preference');\n};\n\n// Set login status and organization (call this when user logs in/out)\nexport const setLoginStatus = (loggedIn, orgId) => {\n  if (!loggedIn) {\n    // Reset to default language when logging out\n    i18n.changeLanguage(DEFAULT_LANGUAGE);\n  }\n  isLoggedIn = loggedIn;\n  currentOrgId = orgId;\n  console.log(`🔐 Login status set to: ${loggedIn}, org: ${orgId}`);\n};\n\n// Language management functions\nexport const loadAvailableLanguages = async () => {\n  try {\n    availableLanguages = await getLanguages();\n    return availableLanguages;\n  } catch (err) {\n    console.error(\"Failed to load available languages:\", err);\n    return [];\n  }\n};\nexport const getAvailableLanguages = () => availableLanguages;\nexport const getCurrentLanguage = () => i18n.language || DEFAULT_LANGUAGE;\nexport const changeLanguage = async languageCode => {\n  try {\n    console.log(`🔄 Changing language to: ${languageCode}`);\n\n    // For non-English languages, force reload resources to trigger backend\n    if (languageCode.toLowerCase() !== 'en') {\n      console.log('🔄 Reloading resources for:', languageCode);\n      await i18n.reloadResources(languageCode);\n    }\n\n    // Change the language\n    await i18n.changeLanguage(languageCode);\n\n    // Note: No need to save to localStorage since language preference is managed via API\n\n    console.log(`✅ Language changed to: ${languageCode}`);\n  } catch (err) {\n    console.error(\"Failed to change language:\", err);\n    throw err;\n  }\n};\nexport const getCurrentLanguageInfo = () => {\n  const currentCode = getCurrentLanguage().toLowerCase();\n  return availableLanguages.find(l => l.LanguageCode.toLowerCase() === currentCode);\n};\nexport const isI18nInitialized = () => initialized;\nexport default i18n;", "map": {"version": 3, "names": ["i18n", "initReactI18next", "<PERSON><PERSON><PERSON><PERSON>", "getLanguages", "DEFAULT_LANGUAGE", "FALLBACK_LANGUAGE", "LANGUAGE_STORAGE_KEY_PREFIX", "availableLanguages", "initialized", "isLoggedIn", "currentOrgId", "undefined", "getOrgLanguageKey", "orgId", "getSavedLanguage", "key", "localStorage", "getItem", "saveLanguage", "language", "setItem", "console", "log", "err", "error", "clearSavedLanguage", "removeItem", "CustomBackend", "init", "read", "namespace", "callback", "langCode", "toLowerCase", "length", "langMeta", "find", "l", "LanguageCode", "warn", "Language", "response", "translations", "Object", "keys", "type", "initializeI18n", "use", "lng", "fallbackLng", "interpolation", "escapeValue", "react", "useSuspense", "backend", "crossDomain", "loadSavedLanguageTranslations", "setLoginStatus", "loggedIn", "changeLanguage", "loadAvailableLanguages", "getAvailableLanguages", "getCurrentLanguage", "languageCode", "reloadResources", "getCurrentLanguageInfo", "currentCode", "isI18nInitialized"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/multilinguial/i18n.ts"], "sourcesContent": ["import i18n from 'i18next';\r\nimport { initReactI18next } from 'react-i18next';\r\nimport { getLabels, getLanguages, LanguageType } from './LanguageService';\r\n\r\n// Constants\r\nconst DEFAULT_LANGUAGE = 'en';\r\nconst FALLBACK_LANGUAGE = 'en';\r\nconst LANGUAGE_STORAGE_KEY_PREFIX = 'quickadapt_language_';\r\n\r\n// Internal state\r\nlet availableLanguages: LanguageType[] = [];\r\nlet initialized = false;\r\nlet isLoggedIn = false;\r\nlet currentOrgId: string | undefined = undefined;\r\n\r\n// Get organization-specific language key\r\nconst getOrgLanguageKey = (orgId: string | undefined): string => {\r\n  return `${LANGUAGE_STORAGE_KEY_PREFIX}${orgId || 'default'}`;\r\n};\r\n\r\n// Language persistence utilities\r\nconst getSavedLanguage = (orgId?: string): string => {\r\n  try {\r\n    const key = getOrgLanguageKey(orgId || currentOrgId);\r\n    return localStorage.getItem(key) || DEFAULT_LANGUAGE;\r\n  } catch {\r\n    return DEFAULT_LANGUAGE;\r\n  }\r\n};\r\n\r\nconst saveLanguage = (language: string, orgId?: string): void => {\r\n  try {\r\n    const key = getOrgLanguageKey(orgId || currentOrgId);\r\n    localStorage.setItem(key, language);\r\n    console.log(`💾 Saved language ${language} for org ${orgId || currentOrgId}`);\r\n  } catch (err) {\r\n    console.error(\"Failed to save language preference:\", err);\r\n  }\r\n};\r\nconst clearSavedLanguage = (orgId?: string): void => {\r\n  try {\r\n    const key = getOrgLanguageKey(orgId || currentOrgId);\r\n    localStorage.removeItem(key);\r\n    console.log(`🗑️ Cleared saved language for org ${orgId || currentOrgId}`);\r\n  } catch (err) {\r\n    console.error(\"Failed to clear language preference:\", err);\r\n  }\r\n};\r\n\r\n// Custom backend to load translations from API\r\nclass CustomBackend {\r\n  static type = 'backend' as const;\r\n\r\n  init() {}\r\n\r\n  async read(language: string, namespace: string, callback: (error: any, data?: any) => void) {\r\n    console.log(`🔍 CustomBackend.read() called with language: ${language}, namespace: ${namespace}`);\r\n    \r\n    try {\r\n      const langCode = language.toLowerCase();\r\n      \r\n      // For English, return empty object (keys will be used as values)\r\n      if (langCode === 'en') {\r\n        console.log('🚀 English detected - returning empty translations');\r\n        callback(null, {});\r\n        return;\r\n      }\r\n\r\n      // For non-English, check if user is logged in\r\n      if (!isLoggedIn) {\r\n        console.log('⚠️ User not logged in - returning empty translations');\r\n        callback(null, {});\r\n        return;\r\n      }\r\n\r\n      // Load available languages if not already loaded\r\n      if (availableLanguages.length === 0) {\r\n        console.log('🔄 Loading available languages...');\r\n        try {\r\n          availableLanguages = await getLanguages();\r\n          console.log(`✅ Loaded ${availableLanguages.length} available languages`);\r\n        } catch (error) {\r\n          console.error('❌ Failed to load available languages:', error);\r\n          callback(null, {});\r\n          return;\r\n        }\r\n      }\r\n\r\n      // Find the language metadata\r\n      const langMeta = availableLanguages.find(l => l.LanguageCode.toLowerCase() === langCode);\r\n      if (!langMeta) {\r\n        console.warn(`⚠️ Language code ${langCode} not found in available languages`);\r\n        callback(null, {});\r\n        return;\r\n      }\r\n\r\n      // Load translations from API\r\n      console.log(`🌐 Loading translations for ${langMeta.Language} (${langCode})`);\r\n      const response = await getLabels(langMeta.Language);\r\n      const translations = response[langCode] || response;\r\n      \r\n      console.log(`✅ Loaded ${Object.keys(translations || {}).length} translations for ${langCode}`);\r\n      callback(null, translations || {});\r\n      \r\n    } catch (err) {\r\n      console.error(`❌ Error loading translations for ${language}:`, err);\r\n      callback(null, {}); // Return empty object on error to prevent crashes\r\n    }\r\n  }\r\n}\r\n\r\n// Initialize i18n (call this once from App.tsx)\r\nexport const initializeI18n = async (): Promise<void> => {\r\n  if (initialized) return;\r\n\r\n  await i18n\r\n    .use(CustomBackend as any)\r\n    .use(initReactI18next)\r\n    .init({\r\n      lng: DEFAULT_LANGUAGE, // Start with default language, will be set from user profile\r\n      fallbackLng: FALLBACK_LANGUAGE,\r\n      interpolation: {\r\n        escapeValue: false,\r\n      },\r\n      react: {\r\n        useSuspense: false,\r\n      },\r\n      backend: {\r\n        crossDomain: true,\r\n      },\r\n    });\r\n\r\n  initialized = true;\r\n  console.log('✅ i18n initialized successfully');\r\n};\r\n\r\n// Load saved language translations after login\r\nexport const loadSavedLanguageTranslations = async (orgId?: string): Promise<void> => {\r\n  if (!isLoggedIn) {\r\n    console.log('⏳ User not logged in - skipping language load');\r\n    return;\r\n  }\r\n\r\n  // Note: Language is now set from user profile instead of localStorage\r\n  console.log('ℹ️ Language will be set from user profile preference');\r\n};\r\n\r\n// Set login status and organization (call this when user logs in/out)\r\nexport const setLoginStatus = (loggedIn: boolean, orgId?: string): void => {\r\n  if (!loggedIn) {\r\n    // Reset to default language when logging out\r\n    i18n.changeLanguage(DEFAULT_LANGUAGE);\r\n  }\r\n  isLoggedIn = loggedIn;\r\n  currentOrgId = orgId;\r\n  console.log(`🔐 Login status set to: ${loggedIn}, org: ${orgId}`);\r\n};\r\n\r\n// Language management functions\r\nexport const loadAvailableLanguages = async (): Promise<LanguageType[]> => {\r\n  try {\r\n    availableLanguages = await getLanguages();\r\n    return availableLanguages;\r\n  } catch (err) {\r\n    console.error(\"Failed to load available languages:\", err);\r\n    return [];\r\n  }\r\n};\r\n\r\nexport const getAvailableLanguages = (): LanguageType[] => availableLanguages;\r\n\r\nexport const getCurrentLanguage = (): string => i18n.language || DEFAULT_LANGUAGE;\r\n\r\nexport const changeLanguage = async (languageCode: string): Promise<void> => {\r\n  try {\r\n    console.log(`🔄 Changing language to: ${languageCode}`);\r\n    \r\n    // For non-English languages, force reload resources to trigger backend\r\n    if (languageCode.toLowerCase() !== 'en') {\r\n      console.log('🔄 Reloading resources for:', languageCode);\r\n      await i18n.reloadResources(languageCode);\r\n    }\r\n    \r\n    // Change the language\r\n    await i18n.changeLanguage(languageCode);\r\n    \r\n    // Note: No need to save to localStorage since language preference is managed via API\r\n    \r\n    console.log(`✅ Language changed to: ${languageCode}`);\r\n  } catch (err) {\r\n    console.error(\"Failed to change language:\", err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const getCurrentLanguageInfo = (): LanguageType | undefined => {\r\n  const currentCode = getCurrentLanguage().toLowerCase();\r\n  return availableLanguages.find(l => l.LanguageCode.toLowerCase() === currentCode);\r\n};\r\n\r\nexport const isI18nInitialized = (): boolean => initialized;\r\n\r\nexport default i18n;\r\nexport type { LanguageType };\r\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,SAAS;AAC1B,SAASC,gBAAgB,QAAQ,eAAe;AAChD,SAASC,SAAS,EAAEC,YAAY,QAAsB,mBAAmB;;AAEzE;AACA,MAAMC,gBAAgB,GAAG,IAAI;AAC7B,MAAMC,iBAAiB,GAAG,IAAI;AAC9B,MAAMC,2BAA2B,GAAG,sBAAsB;;AAE1D;AACA,IAAIC,kBAAkC,GAAG,EAAE;AAC3C,IAAIC,WAAW,GAAG,KAAK;AACvB,IAAIC,UAAU,GAAG,KAAK;AACtB,IAAIC,YAAgC,GAAGC,SAAS;;AAEhD;AACA,MAAMC,iBAAiB,GAAIC,KAAyB,IAAa;EAC/D,OAAO,GAAGP,2BAA2B,GAAGO,KAAK,IAAI,SAAS,EAAE;AAC9D,CAAC;;AAED;AACA,MAAMC,gBAAgB,GAAID,KAAc,IAAa;EACnD,IAAI;IACF,MAAME,GAAG,GAAGH,iBAAiB,CAACC,KAAK,IAAIH,YAAY,CAAC;IACpD,OAAOM,YAAY,CAACC,OAAO,CAACF,GAAG,CAAC,IAAIX,gBAAgB;EACtD,CAAC,CAAC,MAAM;IACN,OAAOA,gBAAgB;EACzB;AACF,CAAC;AAED,MAAMc,YAAY,GAAGA,CAACC,QAAgB,EAAEN,KAAc,KAAW;EAC/D,IAAI;IACF,MAAME,GAAG,GAAGH,iBAAiB,CAACC,KAAK,IAAIH,YAAY,CAAC;IACpDM,YAAY,CAACI,OAAO,CAACL,GAAG,EAAEI,QAAQ,CAAC;IACnCE,OAAO,CAACC,GAAG,CAAC,qBAAqBH,QAAQ,YAAYN,KAAK,IAAIH,YAAY,EAAE,CAAC;EAC/E,CAAC,CAAC,OAAOa,GAAG,EAAE;IACZF,OAAO,CAACG,KAAK,CAAC,qCAAqC,EAAED,GAAG,CAAC;EAC3D;AACF,CAAC;AACD,MAAME,kBAAkB,GAAIZ,KAAc,IAAW;EACnD,IAAI;IACF,MAAME,GAAG,GAAGH,iBAAiB,CAACC,KAAK,IAAIH,YAAY,CAAC;IACpDM,YAAY,CAACU,UAAU,CAACX,GAAG,CAAC;IAC5BM,OAAO,CAACC,GAAG,CAAC,sCAAsCT,KAAK,IAAIH,YAAY,EAAE,CAAC;EAC5E,CAAC,CAAC,OAAOa,GAAG,EAAE;IACZF,OAAO,CAACG,KAAK,CAAC,sCAAsC,EAAED,GAAG,CAAC;EAC5D;AACF,CAAC;;AAED;AACA,MAAMI,aAAa,CAAC;EAGlBC,IAAIA,CAAA,EAAG,CAAC;EAER,MAAMC,IAAIA,CAACV,QAAgB,EAAEW,SAAiB,EAAEC,QAA0C,EAAE;IAC1FV,OAAO,CAACC,GAAG,CAAC,iDAAiDH,QAAQ,gBAAgBW,SAAS,EAAE,CAAC;IAEjG,IAAI;MACF,MAAME,QAAQ,GAAGb,QAAQ,CAACc,WAAW,CAAC,CAAC;;MAEvC;MACA,IAAID,QAAQ,KAAK,IAAI,EAAE;QACrBX,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjES,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAClB;MACF;;MAEA;MACA,IAAI,CAACtB,UAAU,EAAE;QACfY,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnES,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAClB;MACF;;MAEA;MACA,IAAIxB,kBAAkB,CAAC2B,MAAM,KAAK,CAAC,EAAE;QACnCb,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,IAAI;UACFf,kBAAkB,GAAG,MAAMJ,YAAY,CAAC,CAAC;UACzCkB,OAAO,CAACC,GAAG,CAAC,YAAYf,kBAAkB,CAAC2B,MAAM,sBAAsB,CAAC;QAC1E,CAAC,CAAC,OAAOV,KAAK,EAAE;UACdH,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7DO,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;UAClB;QACF;MACF;;MAEA;MACA,MAAMI,QAAQ,GAAG5B,kBAAkB,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,CAACL,WAAW,CAAC,CAAC,KAAKD,QAAQ,CAAC;MACxF,IAAI,CAACG,QAAQ,EAAE;QACbd,OAAO,CAACkB,IAAI,CAAC,oBAAoBP,QAAQ,mCAAmC,CAAC;QAC7ED,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAClB;MACF;;MAEA;MACAV,OAAO,CAACC,GAAG,CAAC,+BAA+Ba,QAAQ,CAACK,QAAQ,KAAKR,QAAQ,GAAG,CAAC;MAC7E,MAAMS,QAAQ,GAAG,MAAMvC,SAAS,CAACiC,QAAQ,CAACK,QAAQ,CAAC;MACnD,MAAME,YAAY,GAAGD,QAAQ,CAACT,QAAQ,CAAC,IAAIS,QAAQ;MAEnDpB,OAAO,CAACC,GAAG,CAAC,YAAYqB,MAAM,CAACC,IAAI,CAACF,YAAY,IAAI,CAAC,CAAC,CAAC,CAACR,MAAM,qBAAqBF,QAAQ,EAAE,CAAC;MAC9FD,QAAQ,CAAC,IAAI,EAAEW,YAAY,IAAI,CAAC,CAAC,CAAC;IAEpC,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZF,OAAO,CAACG,KAAK,CAAC,oCAAoCL,QAAQ,GAAG,EAAEI,GAAG,CAAC;MACnEQ,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB;EACF;AACF;;AAEA;AA7DMJ,aAAa,CACVkB,IAAI,GAAG,SAAS;AA6DzB,OAAO,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAA2B;EACvD,IAAItC,WAAW,EAAE;EAEjB,MAAMR,IAAI,CACP+C,GAAG,CAACpB,aAAoB,CAAC,CACzBoB,GAAG,CAAC9C,gBAAgB,CAAC,CACrB2B,IAAI,CAAC;IACJoB,GAAG,EAAE5C,gBAAgB;IAAE;IACvB6C,WAAW,EAAE5C,iBAAiB;IAC9B6C,aAAa,EAAE;MACbC,WAAW,EAAE;IACf,CAAC;IACDC,KAAK,EAAE;MACLC,WAAW,EAAE;IACf,CAAC;IACDC,OAAO,EAAE;MACPC,WAAW,EAAE;IACf;EACF,CAAC,CAAC;EAEJ/C,WAAW,GAAG,IAAI;EAClBa,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;AAChD,CAAC;;AAED;AACA,OAAO,MAAMkC,6BAA6B,GAAG,MAAO3C,KAAc,IAAoB;EACpF,IAAI,CAACJ,UAAU,EAAE;IACfY,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5D;EACF;;EAEA;EACAD,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;AACrE,CAAC;;AAED;AACA,OAAO,MAAMmC,cAAc,GAAGA,CAACC,QAAiB,EAAE7C,KAAc,KAAW;EACzE,IAAI,CAAC6C,QAAQ,EAAE;IACb;IACA1D,IAAI,CAAC2D,cAAc,CAACvD,gBAAgB,CAAC;EACvC;EACAK,UAAU,GAAGiD,QAAQ;EACrBhD,YAAY,GAAGG,KAAK;EACpBQ,OAAO,CAACC,GAAG,CAAC,2BAA2BoC,QAAQ,UAAU7C,KAAK,EAAE,CAAC;AACnE,CAAC;;AAED;AACA,OAAO,MAAM+C,sBAAsB,GAAG,MAAAA,CAAA,KAAqC;EACzE,IAAI;IACFrD,kBAAkB,GAAG,MAAMJ,YAAY,CAAC,CAAC;IACzC,OAAOI,kBAAkB;EAC3B,CAAC,CAAC,OAAOgB,GAAG,EAAE;IACZF,OAAO,CAACG,KAAK,CAAC,qCAAqC,EAAED,GAAG,CAAC;IACzD,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMsC,qBAAqB,GAAGA,CAAA,KAAsBtD,kBAAkB;AAE7E,OAAO,MAAMuD,kBAAkB,GAAGA,CAAA,KAAc9D,IAAI,CAACmB,QAAQ,IAAIf,gBAAgB;AAEjF,OAAO,MAAMuD,cAAc,GAAG,MAAOI,YAAoB,IAAoB;EAC3E,IAAI;IACF1C,OAAO,CAACC,GAAG,CAAC,4BAA4ByC,YAAY,EAAE,CAAC;;IAEvD;IACA,IAAIA,YAAY,CAAC9B,WAAW,CAAC,CAAC,KAAK,IAAI,EAAE;MACvCZ,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEyC,YAAY,CAAC;MACxD,MAAM/D,IAAI,CAACgE,eAAe,CAACD,YAAY,CAAC;IAC1C;;IAEA;IACA,MAAM/D,IAAI,CAAC2D,cAAc,CAACI,YAAY,CAAC;;IAEvC;;IAEA1C,OAAO,CAACC,GAAG,CAAC,0BAA0ByC,YAAY,EAAE,CAAC;EACvD,CAAC,CAAC,OAAOxC,GAAG,EAAE;IACZF,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAED,GAAG,CAAC;IAChD,MAAMA,GAAG;EACX;AACF,CAAC;AAED,OAAO,MAAM0C,sBAAsB,GAAGA,CAAA,KAAgC;EACpE,MAAMC,WAAW,GAAGJ,kBAAkB,CAAC,CAAC,CAAC7B,WAAW,CAAC,CAAC;EACtD,OAAO1B,kBAAkB,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,YAAY,CAACL,WAAW,CAAC,CAAC,KAAKiC,WAAW,CAAC;AACnF,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAAe3D,WAAW;AAE3D,eAAeR,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}