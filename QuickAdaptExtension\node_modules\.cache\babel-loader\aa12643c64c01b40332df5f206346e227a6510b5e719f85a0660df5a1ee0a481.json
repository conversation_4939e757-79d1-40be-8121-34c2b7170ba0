{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\AI\\\\AgentAdditionalContextPopup.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Dialog from '@mui/material/Dialog';\nimport DialogTitle from '@mui/material/DialogTitle';\nimport DialogContent from '@mui/material/DialogContent';\nimport DialogActions from '@mui/material/DialogActions';\nimport Button from '@mui/material/Button';\nimport TextareaAutosize from '@mui/material/TextareaAutosize';\nimport useDrawerStore from '../../store/drawerStore';\nimport { cancelTraining } from '../../services/ScrapingService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentAdditionalContextPopup = ({\n  open,\n  onClose,\n  onSaved,\n  onCancel\n}) => {\n  _s();\n  const {\n    agentAdditionalContext,\n    setAgentAdditionalContext,\n    setIsAgentTraining\n  } = useDrawerStore(state => state);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState(null);\n  const handleSave = async () => {\n    setSaving(true);\n    setError(null);\n    try {\n      // Process the save - the actual agent training is handled in the parent component\n      onSaved();\n    } catch (e) {\n      setError('Failed to save agent.');\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleCancel = async () => {\n    setSaving(true);\n    setError(null);\n    try {\n      // Cancel the training process (stops scraping and clears data)\n      await cancelTraining();\n\n      // Update the training state\n      setIsAgentTraining(false);\n\n      // Call the onCancel callback if provided, otherwise use onClose\n      if (onCancel) {\n        onCancel();\n      } else {\n        onClose();\n      }\n    } catch (e) {\n      setError('Failed to cancel training.');\n      console.error('Error canceling training:', e);\n    } finally {\n      setSaving(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"AgentAdditionalContextPopup\",\n    id: \"AgentAdditionalContextPopup\",\n    children: /*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: onClose,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Agent Additional Context\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(TextareaAutosize, {\n          id: \"textareaadditionalcontextpopup\",\n          minRows: 5,\n          style: {\n            width: '100%',\n            marginTop: 16\n          },\n          placeholder: \"Enter additional context for the agent...\",\n          value: agentAdditionalContext,\n          onChange: e => setAgentAdditionalContext(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 9\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: 'red',\n            marginTop: 8\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 19\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          id: \"textareaadditionalcontextpopup\",\n          onClick: handleCancel,\n          disabled: saving,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          id: \"textareaadditionalcontextpopup\",\n          onClick: handleSave,\n          disabled: saving,\n          variant: \"contained\",\n          color: \"primary\",\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentAdditionalContextPopup, \"kT13klg8p8khgWC2L3Qlg9M8u8k=\", false, function () {\n  return [useDrawerStore];\n});\n_c = AgentAdditionalContextPopup;\nexport default AgentAdditionalContextPopup;\nvar _c;\n$RefreshReg$(_c, \"AgentAdditionalContextPopup\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextareaAutosize", "useDrawerStore", "cancelTraining", "jsxDEV", "_jsxDEV", "AgentAdditionalContextPopup", "open", "onClose", "onSaved", "onCancel", "_s", "agentAdditionalContext", "setAgentAdditionalContext", "setIsAgentTraining", "state", "saving", "setSaving", "error", "setError", "handleSave", "e", "handleCancel", "console", "className", "id", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "minRows", "style", "width", "marginTop", "placeholder", "value", "onChange", "target", "color", "onClick", "disabled", "variant", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/AI/AgentAdditionalContextPopup.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport Dialog from '@mui/material/Dialog';\r\nimport DialogTitle from '@mui/material/DialogTitle';\r\nimport DialogContent from '@mui/material/DialogContent';\r\nimport DialogActions from '@mui/material/DialogActions';\r\nimport Button from '@mui/material/Button';\r\nimport TextareaAutosize from '@mui/material/TextareaAutosize';\r\nimport useDrawerStore, { DrawerState } from '../../store/drawerStore';\r\nimport { cancelTraining } from '../../services/ScrapingService';\r\n\r\ninterface AgentAdditionalContextPopupProps {\r\n  open: boolean;\r\n  onClose: () => void;\r\n  onSaved: () => void;\r\n  onCancel?: () => void;\r\n}\r\n\r\nconst AgentAdditionalContextPopup: React.FC<AgentAdditionalContextPopupProps> = ({ open, onClose, onSaved, onCancel }) => {\r\n  const {\r\n    agentAdditionalContext,\r\n    setAgentAdditionalContext,\r\n    setIsAgentTraining\r\n  } = useDrawerStore((state: DrawerState) => state);\r\n  const [saving, setSaving] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const handleSave = async () => {\r\n    setSaving(true);\r\n    setError(null);\r\n    try {\r\n      // Process the save - the actual agent training is handled in the parent component\r\n      onSaved();\r\n    } catch (e) {\r\n      setError('Failed to save agent.');\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = async () => {\r\n    setSaving(true);\r\n    setError(null);\r\n    try {\r\n      // Cancel the training process (stops scraping and clears data)\r\n      await cancelTraining();\r\n\r\n      // Update the training state\r\n      setIsAgentTraining(false);\r\n\r\n      // Call the onCancel callback if provided, otherwise use onClose\r\n      if (onCancel) {\r\n        onCancel();\r\n      } else {\r\n        onClose();\r\n      }\r\n    } catch (e) {\r\n      setError('Failed to cancel training.');\r\n      console.error('Error canceling training:', e);\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className='AgentAdditionalContextPopup' id='AgentAdditionalContextPopup'>\r\n    <Dialog open={open} onClose={onClose} maxWidth=\"sm\" fullWidth>\r\n      <DialogTitle>Agent Additional Context</DialogTitle>\r\n      <DialogContent>\r\n        <TextareaAutosize\r\n        id=\"textareaadditionalcontextpopup\"\r\n          minRows={5}\r\n          style={{ width: '100%', marginTop: 16 }}\r\n          placeholder=\"Enter additional context for the agent...\"\r\n          value={agentAdditionalContext}\r\n          onChange={e => setAgentAdditionalContext(e.target.value)}\r\n        />\r\n        {error && <div style={{ color: 'red', marginTop: 8 }}>{error}</div>}\r\n      </DialogContent>\r\n      <DialogActions>\r\n        <Button id=\"textareaadditionalcontextpopup\" onClick={handleCancel} disabled={saving}>Cancel</Button>\r\n        <Button id=\"textareaadditionalcontextpopup\" onClick={handleSave} disabled={saving} variant=\"contained\" color=\"primary\">Save</Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AgentAdditionalContextPopup; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7D,OAAOC,cAAc,MAAuB,yBAAyB;AACrE,SAASC,cAAc,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAShE,MAAMC,2BAAuE,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACxH,MAAM;IACJC,sBAAsB;IACtBC,yBAAyB;IACzBC;EACF,CAAC,GAAGZ,cAAc,CAAEa,KAAkB,IAAKA,KAAK,CAAC;EACjD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAMyB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BH,SAAS,CAAC,IAAI,CAAC;IACfE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF;MACAV,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOY,CAAC,EAAE;MACVF,QAAQ,CAAC,uBAAuB,CAAC;IACnC,CAAC,SAAS;MACRF,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMK,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BL,SAAS,CAAC,IAAI,CAAC;IACfE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF;MACA,MAAMhB,cAAc,CAAC,CAAC;;MAEtB;MACAW,kBAAkB,CAAC,KAAK,CAAC;;MAEzB;MACA,IAAIJ,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ,CAAC,MAAM;QACLF,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC,OAAOa,CAAC,EAAE;MACVF,QAAQ,CAAC,4BAA4B,CAAC;MACtCI,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEG,CAAC,CAAC;IAC/C,CAAC,SAAS;MACRJ,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,oBACEZ,OAAA;IAAKmB,SAAS,EAAC,6BAA6B;IAACC,EAAE,EAAC,6BAA6B;IAAAC,QAAA,eAC7ErB,OAAA,CAACT,MAAM;MAACW,IAAI,EAAEA,IAAK;MAACC,OAAO,EAAEA,OAAQ;MAACmB,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAF,QAAA,gBAC3DrB,OAAA,CAACR,WAAW;QAAA6B,QAAA,EAAC;MAAwB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACnD3B,OAAA,CAACP,aAAa;QAAA4B,QAAA,gBACZrB,OAAA,CAACJ,gBAAgB;UACjBwB,EAAE,EAAC,gCAAgC;UACjCQ,OAAO,EAAE,CAAE;UACXC,KAAK,EAAE;YAAEC,KAAK,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAG,CAAE;UACxCC,WAAW,EAAC,2CAA2C;UACvDC,KAAK,EAAE1B,sBAAuB;UAC9B2B,QAAQ,EAAElB,CAAC,IAAIR,yBAAyB,CAACQ,CAAC,CAACmB,MAAM,CAACF,KAAK;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,EACDd,KAAK,iBAAIb,OAAA;UAAK6B,KAAK,EAAE;YAAEO,KAAK,EAAE,KAAK;YAAEL,SAAS,EAAE;UAAE,CAAE;UAAAV,QAAA,EAAER;QAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC,eAChB3B,OAAA,CAACN,aAAa;QAAA2B,QAAA,gBACZrB,OAAA,CAACL,MAAM;UAACyB,EAAE,EAAC,gCAAgC;UAACiB,OAAO,EAAEpB,YAAa;UAACqB,QAAQ,EAAE3B,MAAO;UAAAU,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpG3B,OAAA,CAACL,MAAM;UAACyB,EAAE,EAAC,gCAAgC;UAACiB,OAAO,EAAEtB,UAAW;UAACuB,QAAQ,EAAE3B,MAAO;UAAC4B,OAAO,EAAC,WAAW;UAACH,KAAK,EAAC,SAAS;UAAAf,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrB,EAAA,CApEIL,2BAAuE;EAAA,QAKvEJ,cAAc;AAAA;AAAA2C,EAAA,GALdvC,2BAAuE;AAsE7E,eAAeA,2BAA2B;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}