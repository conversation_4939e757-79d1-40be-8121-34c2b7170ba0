{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\CheckpointAddPopup.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { Box, Typography, TextField, IconButton, Button, InputAdornment, FormControl, Select, MenuItem, Tooltip, CircularProgress } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport { useTranslation } from 'react-i18next';\nimport useDrawerStore from \"../../store/drawerStore\";\nimport SearchIcon from \"@mui/icons-material/Search\";\nimport { deletestep, chkicn1, chkicn2, chkicn3, chkicn4, chkicn5, chkicn6, redirect, warning } from \"../../assets/icons/icons\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';\nimport { getAllGuides } from \"../../services/GuideListServices\";\nimport { AccountContext } from \"../login/AccountContext\";\nimport '../../styles/rtl_styles.scss';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CheckPointAddPopup = ({\n  checklistCheckpointProperties,\n  setChecklistCheckpointProperties\n}) => {\n  _s();\n  var _checklistCheckpointL;\n  const {\n    t: translate\n  } = useTranslation();\n  const [duplicateError, setDuplicateError] = useState(null);\n  const {\n    checklistGuideMetaData,\n    setCheckPointsEditPopup,\n    checkpointsEditPopup,\n    titlePopup,\n    setTitlePopup,\n    setDesignPopup,\n    titleColor,\n    setTitleColor,\n    checkpointsPopup,\n    setCheckPointsPopup,\n    checkpointTitleColor,\n    setCheckpointTitleColor,\n    checkpointTitleDescription,\n    setCheckpointTitleDescription,\n    checkpointIconColor,\n    setCheckpointIconColor,\n    setUnlockCheckPointInOrder,\n    unlockCheckPointInOrder,\n    checkPointMessage,\n    setCheckPointMessage,\n    setCheckPointsAddPopup,\n    updateChecklistCheckPoints,\n    updateChecklistCheckPointItem,\n    setIsUnSavedChanges,\n    isUnSavedChanges\n  } = useDrawerStore(state => state);\n  const [selectedInteraction, setSelectedInteraction] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [checklistCheckpointListProperties, setChecklistCheckpointListProperties] = useState({\n    id: '',\n    interaction: '',\n    title: '',\n    description: '',\n    redirectURL: '',\n    icon: '',\n    supportingMedia: '',\n    mediaTitle: '',\n    mediaDescription: ''\n  });\n  const handleCheckPointIconColorChange = e => setCheckpointIconColor(e.target.value);\n  const handleCheckPointTitleColorChange = e => setCheckpointTitleColor(e.target.value);\n  const handleCheckPointDescriptionColorChange = e => setCheckpointTitleColor(e.target.value);\n  const [interactions, setInteractions] = useState([]);\n  useEffect(() => {\n    if (selectedInteraction) {\n      const selectedInteractionData = filteredInteractions.find(interaction => interaction.Name === selectedInteraction);\n      if (selectedInteractionData) {\n        setChecklistCheckpointListProperties({\n          interaction: selectedInteraction,\n          title: selectedInteractionData.Name || '',\n          description: selectedInteractionData.Description || '',\n          redirectURL: selectedInteractionData.TargetUrl || '',\n          icon: selectedInteractionData.targetUrl,\n          supportingMedia: '',\n          mediaTitle: selectedInteractionData.Name || '',\n          mediaDescription: selectedInteractionData.description || '',\n          id: selectedInteractionData.GuideId || ''\n        });\n      }\n    }\n  }, [selectedInteraction, interactions, searchTerm]);\n  const handleClose = () => {\n    setCheckPointsAddPopup(false);\n  };\n  const handledesignclose = () => {\n    setDesignPopup(false);\n  };\n  const handleSizeChange = value => {\n    const sizeInPx = 16 + (value - 1) * 4;\n    onPropertyChange(\"Size\", sizeInPx);\n  };\n  const onReselectElement = () => {};\n  const onPropertyChange = (key, value) => {\n    setChecklistCheckpointListProperties(prevState => ({\n      ...prevState,\n      [key]: value\n    }));\n  };\n  const [applyclicked, setapplyClisked] = useState(false);\n  const handleApplyChanges = () => {\n    var _checklistCheckpointP;\n    setFileError(null);\n    setDuplicateError(null);\n    checklistCheckpointListProperties.icon = icon;\n\n    // Check if interaction is selected\n    if (!checklistCheckpointListProperties.interaction || checklistCheckpointListProperties.interaction.trim() === \"\") {\n      setDuplicateError(translate(\"Please select an interaction\"));\n      return;\n    }\n\n    // Check for duplicate interaction\n    const isDuplicate = (_checklistCheckpointP = checklistCheckpointProperties.checkpointsList) === null || _checklistCheckpointP === void 0 ? void 0 : _checklistCheckpointP.some(cp => cp.interaction === checklistCheckpointListProperties.interaction);\n    if (isDuplicate) {\n      setDuplicateError(translate(\"Interaction already used\"));\n      return;\n    }\n    const updatedCheckpoint = {\n      ...checklistCheckpointListProperties\n    };\n    updateChecklistCheckPointItem(updatedCheckpoint);\n    setapplyClisked(true);\n    setIsUnSavedChanges(true);\n    setChecklistCheckpointProperties(prev => ({\n      ...prev,\n      checkpointsList: [...prev.checkpointsList,\n      // Keep existing checkpoints\n      updatedCheckpoint // Add the new one at the end\n      ]\n    }));\n    handleClose();\n  };\n  const handleEditClick = () => {\n    setCheckPointsEditPopup(true);\n  };\n  const [skip, setSkip] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [hasMore, setHasMore] = useState(true);\n  const [filteredInteractions, setFilteredInteractions] = useState([]);\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const [isSearching, setIsSearching] = useState(false);\n  const top = 20;\n  useEffect(() => {\n    fetchData(0); // Fetch initial data to enable scrolling\n  }, []);\n  const fetchData = async (newSkip, newsearchTerm = \"\") => {\n    if (newsearchTerm == \"\" && loading) return; // Prevent duplicate calls\n    if (newsearchTerm == \"\" && searchTerm != \"\") newsearchTerm = searchTerm;\n    setLoading(true);\n    const filters = [{\n      FieldName: \"AccountId\",\n      ElementType: \"string\",\n      Condition: \"contains\",\n      Value: accountId,\n      IsCustomField: false\n    }, {\n      FieldName: \"GuideType\",\n      ElementType: \"string\",\n      Condition: \"not equal\",\n      Value: \"Checklist\",\n      IsCustomField: false\n    }];\n    if (newsearchTerm != \"\") {\n      filters.push({\n        FieldName: \"Name\",\n        ElementType: \"string\",\n        Condition: \"contains\",\n        Value: newsearchTerm,\n        IsCustomField: false\n      });\n    }\n    try {\n      const data = await getAllGuides(newSkip, top, filters, \"\");\n      const newInteractions = data === null || data === void 0 ? void 0 : data.results;\n      if (newsearchTerm == \"\") {\n        if (newSkip === 0) {\n          setInteractions(newInteractions);\n          setFilteredInteractions(newInteractions);\n        } else {\n          setInteractions(prev => [...prev, ...newInteractions]);\n          setFilteredInteractions(prev => [...prev, ...newInteractions]);\n        }\n      } else {\n        if (newSkip === 0) setFilteredInteractions(newInteractions);else setFilteredInteractions(prev => [...prev, ...newInteractions]);\n      }\n      setSkip(newSkip + top);\n      setHasMore(newInteractions.length > 0);\n    } catch (error) {\n      console.error(\"Error fetching guides:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleMenuScroll = event => {\n    const target = event.currentTarget;\n    const {\n      scrollTop,\n      scrollHeight,\n      clientHeight\n    } = target;\n    if (scrollHeight - scrollTop - clientHeight < 50 && !loading && hasMore) {\n      fetchData(skip); // no need to pass searchTerm\n    }\n  };\n  const [isInteractionDropdownOpen, setIsInteractionDropdownOpen] = useState(false);\n  const handleInteractionChange = newValue => {\n    setSelectedInteraction(newValue);\n    onPropertyChange(\"interaction\", newValue);\n    setIsInteractionDropdownOpen(false);\n    setSearchTerm('');\n    setDuplicateError(null); // Clear duplicate error when selecting a new interaction\n  };\n  const handleInteractionDropdownOpen = () => {\n    setIsInteractionDropdownOpen(true);\n    if (searchTerm.trim()) {\n      fetchData(0, searchTerm); // Or your logic to update `filteredInteractions`\n    } else {\n      fetchData(0, \"\"); // Optional: load default or all interactions if search is empty\n    }\n  };\n  const handleInteractionDropdownClose = () => {\n    setIsInteractionDropdownOpen(false);\n    setSearchTerm('');\n  };\n  const handleSearch = event => {\n    const term = event.target.value;\n    setSearchTerm(term);\n    if (!term.trim()) {\n      // If search is cleared\n      setFilteredInteractions(interactions);\n      setIsSearching(false);\n    } else {\n      //   const filtered = interactions.filter((interaction) =>\n      // \tinteraction.Name.toLowerCase().includes(term.toLowerCase())\n      //   );\n      //   setFilteredInteractions(filtered);\n      setIsSearching(true);\n      setFilteredInteractions([]);\n      fetchData(0, term);\n    }\n  };\n  const [icons, setIcons] = useState([{\n    id: 1,\n    component: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: chkicn1\n      },\n      style: {\n        zoom: 1,\n        display: \"flex\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 22\n    }, this),\n    selected: true\n  }, {\n    id: 2,\n    component: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: chkicn2\n      },\n      style: {\n        zoom: 1,\n        display: \"flex\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 23\n    }, this),\n    selected: false\n  }, {\n    id: 3,\n    component: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: chkicn3\n      },\n      style: {\n        zoom: 1,\n        display: \"flex\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 23\n    }, this),\n    selected: false\n  }, {\n    id: 4,\n    component: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: chkicn4\n      },\n      style: {\n        zoom: 1,\n        display: \"flex\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 25\n    }, this),\n    selected: false\n  }, {\n    id: 5,\n    component: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: chkicn5\n      },\n      style: {\n        zoom: 1,\n        display: \"flex\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 25\n    }, this),\n    selected: false\n  }, {\n    id: 6,\n    component: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: chkicn6\n      },\n      style: {\n        zoom: 1,\n        display: \"flex\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 25\n    }, this),\n    selected: false\n  }]);\n  const [error, setError] = useState(null);\n  const handleIconClick = async id => {\n    setIcons(prevIcons => prevIcons.map(icon => ({\n      ...icon,\n      selected: icon.id === id\n    })));\n    const selectedIcon = icons.find(icon => icon.id === id);\n    if (selectedIcon) {\n      var _selectedIcon$compone;\n      const svgElement = (_selectedIcon$compone = selectedIcon.component.props.dangerouslySetInnerHTML) === null || _selectedIcon$compone === void 0 ? void 0 : _selectedIcon$compone.__html;\n      if (svgElement) {\n        const base64Icon = svgToBase64(svgElement);\n        setIcon(base64Icon);\n        checklistCheckpointListProperties.icon = base64Icon;\n      }\n    }\n  };\n\n  // Helper function to convert SVG to Base64\n  const svgToBase64 = svgString => {\n    return `data:image/svg+xml;base64,${btoa(svgString)}`;\n  };\n  const handleFileUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (!file) return;\n    const isIco = file.name.endsWith(\".ico\");\n\n    // Validate the file type and size\n    const img = new Image();\n    img.src = URL.createObjectURL(file);\n    img.onload = () => {\n      if (!isIco || img.width > 64 || img.height > 64) {\n        setError(translate(\"Please upload an .ico file less than 64x64px\"));\n      } else {\n        setError(null);\n        setIcons(prevIcons => [...prevIcons, {\n          id: prevIcons.length + 1,\n          component: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: img.src,\n            alt: \"Custom Icon\",\n            width: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 44\n          }, this),\n          selected: false\n        }]);\n      }\n    };\n  };\n  const [icon, setIcon] = useState();\n  useEffect(() => {\n    const initialSelectedIcon = icons.find(icon => icon.selected);\n    if (initialSelectedIcon && !checklistCheckpointListProperties.icon) {\n      var _initialSelectedIcon$;\n      const svgElement = (_initialSelectedIcon$ = initialSelectedIcon.component.props.dangerouslySetInnerHTML) === null || _initialSelectedIcon$ === void 0 ? void 0 : _initialSelectedIcon$.__html;\n      if (svgElement) {\n        const base64Icon = svgToBase64(svgElement);\n        setIcon(base64Icon);\n        checklistCheckpointListProperties.icon = base64Icon;\n      }\n    }\n  }, []);\n  const [files, setFiles] = useState([]);\n  const [gifFile, setGifFile] = useState(null);\n  const [videoFile, setVideoFile] = useState(null);\n  const [imageType, setImageType] = useState(null); // Track locked image type\n  const [fileError, setFileError] = useState(null);\n  const handleFileChange = async event => {\n    setFileError(null);\n    if (!event.target.files) return;\n    const newFiles = Array.from(event.target.files);\n    const isAllGif = newFiles.every(file => file.type === \"image/gif\");\n    const isAllMp4 = newFiles.every(file => file.type === \"video/mp4\");\n    const isAllImages = newFiles.every(file => [\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type));\n    const isMixedType = !(isAllGif || isAllMp4 || isAllImages);\n    if (isMixedType) {\n      setFileError(translate(\"Mixed file formats are not allowed.\"));\n      return;\n    }\n    if (gifFile) {\n      if (isAllGif) {\n        setFileError(translate(\"Only one GIF is allowed.\"));\n        return;\n      } else {\n        setFileError(translate(\"Mixed file formats are not allowed.\"));\n        return;\n      }\n    }\n    if (videoFile) {\n      if (isAllMp4) {\n        setFileError(translate(\"Only one Video is allowed.\"));\n        return;\n      } else {\n        setFileError(translate(\"Mixed file formats are not allowed.\"));\n        return;\n      }\n    }\n    if (files.length > 0) {\n      if (!isAllImages) {\n        setFileError(translate(\"Mixed file formats are not allowed.\"));\n        return;\n      }\n      if (imageType && !newFiles.every(file => file.type === imageType)) {\n        setFileError(translate(\"Mixed file formats are not allowed.\"));\n        return;\n      }\n    }\n    if (isAllGif) {\n      if (newFiles.length > 1) {\n        setFileError(translate(\"Only one GIF is allowed.\"));\n        return;\n      }\n      setGifFile(newFiles[0]);\n    } else if (isAllMp4) {\n      if (newFiles.length > 1) {\n        setFileError(translate(\"Only one Video is allowed.\"));\n        return;\n      }\n      setVideoFile(newFiles[0]);\n    } else if (isAllImages) {\n      const newImageType = newFiles[0].type;\n      if (!imageType) {\n        setImageType(newImageType); // Lock the image type\n      }\n      setFiles(prevFiles => {\n        const updatedFiles = [...prevFiles, ...newFiles];\n        updatedFiles.sort((a, b) => {\n          const nameA = a.name.match(/\\d+/) ? parseInt(a.name.match(/\\d+/)[0], 10) : 0;\n          const nameB = b.name.match(/\\d+/) ? parseInt(b.name.match(/\\d+/)[0], 10) : 0;\n          return nameA - nameB;\n        });\n        return updatedFiles;\n      });\n    }\n    const base64Files = await Promise.all(newFiles.map(async file => ({\n      Name: file.name,\n      Type: file.type,\n      Base64: await fileToBase64(file)\n    })));\n    setChecklistCheckpointListProperties(prevState => {\n      const updatedMedia = [...(prevState.supportingMedia || []), ...base64Files];\n      updatedMedia.sort((a, b) => {\n        const nameA = a.Name.match(/\\d+/) ? parseInt(a.Name.match(/\\d+/)[0], 10) : 0;\n        const nameB = b.Name.match(/\\d+/) ? parseInt(b.Name.match(/\\d+/)[0], 10) : 0;\n        return nameA - nameB;\n      });\n      return {\n        ...prevState,\n        supportingMedia: updatedMedia\n      };\n    });\n  };\n  const fileToBase64 = file => {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.readAsDataURL(file);\n      reader.onload = () => resolve(reader.result);\n      reader.onerror = error => reject(error);\n    });\n  };\n  const handleDeleteFile = index => {\n    setFileError(null);\n    setFiles(prevFiles => {\n      const newFiles = prevFiles.filter((_, i) => i !== index);\n      if (newFiles.length === 0) setImageType(null); // Reset image lock\n\n      setChecklistCheckpointListProperties(prevProperties => ({\n        ...prevProperties,\n        supportingMedia: prevProperties.supportingMedia.filter((_, i) => i !== index)\n      }));\n      return newFiles;\n    });\n  };\n  const handleDeleteGif = () => {\n    setGifFile(null);\n    setChecklistCheckpointListProperties(prevProperties => ({\n      ...prevProperties,\n      supportingMedia: prevProperties.supportingMedia.filter(file => {\n        var _file$Name;\n        return !((_file$Name = file.Name) !== null && _file$Name !== void 0 && _file$Name.toLowerCase().endsWith(\".gif\"));\n      })\n    }));\n  };\n  const handleDeleteVideo = () => {\n    setVideoFile(null);\n    setChecklistCheckpointListProperties(prevProperties => ({\n      ...prevProperties,\n      supportingMedia: prevProperties.supportingMedia.filter(file => {\n        var _file$Name2;\n        return !((_file$Name2 = file.Name) !== null && _file$Name2 !== void 0 && _file$Name2.toLowerCase().endsWith(\".mp4\"));\n      })\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"back\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: [translate(\"Step\"), \" \", checklistGuideMetaData[0].checkpoints.checkpointsList.length + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-canblock\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-controls\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box qadpt-chkcontrol-box\"\n              // sx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0px !important\" }}\n              ,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  padding: \"0 0 8px 0 !important\"\n                },\n                children: translate(\"Interaction\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 5\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                variant: \"outlined\",\n                fullWidth: true,\n                className: \"qadpt-control-input\",\n                sx: {\n                  width: \"100% !important\",\n                  borderRadius: \"12px\",\n                  padding: \"0\",\n                  margin: \"0 !important\"\n                },\n                children: /*#__PURE__*/_jsxDEV(Select, {\n                  open: isInteractionDropdownOpen,\n                  onOpen: handleInteractionDropdownOpen,\n                  onClose: handleInteractionDropdownClose,\n                  value: selectedInteraction,\n                  onChange: e => {\n                    const newValue = e.target.value;\n                    setSelectedInteraction(newValue);\n                    onPropertyChange(\"interaction\", newValue);\n                  },\n                  name: \"ShowUpon\",\n                  displayEmpty: true,\n                  sx: {\n                    width: \"100% !important\",\n                    textAlign: \"left\",\n                    \"& .MuiOutlinedInput-root\": {\n                      \"&:hover\": {\n                        borderColor: \"none !important\"\n                      },\n                      \"&.Mui-focused\": {\n                        borderColor: \"none !important\"\n                      }\n                    },\n                    \"& .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"35px !important\"\n                    }\n                  },\n                  MenuProps: {\n                    PaperProps: {\n                      sx: {\n                        maxHeight: 430,\n                        overflowY: \"auto\",\n                        maxWidth: \"220px\",\n                        \"& ul li\": {\n                          whiteSpace: \"normal\",\n                          wordBreak: \"break-word\",\n                          overflowWrap: \"break-word\",\n                          maxWidth: \"100%\"\n                        },\n                        \"& .MuiFormControl-root .MuiInputBase-root\": {\n                          borderRadius: \"12px\",\n                          padding: \"1px !important\"\n                        },\n                        \"& .MuiOutlinedInput-root\": {\n                          \"&:hover\": {\n                            borderColor: \"#ccc !important\"\n                          },\n                          \"&.Mui-focused\": {\n                            borderColor: \"#ccc !important\"\n                          }\n                        },\n                        \"& .MuiOutlinedInput-notchedOutline\": {\n                          borderColor: \"#ccc !important\",\n                          borderWidth: \"1px !important\"\n                        }\n                      }\n                    },\n                    onClose: handleInteractionDropdownClose\n                  },\n                  renderValue: selected => selected || translate(\"Select Interaction\"),\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      position: \"sticky\",\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      zIndex: 1,\n                      backgroundColor: \"white\",\n                      padding: \"8px\",\n                      borderBottom: \"1px solid #eee\"\n                    }\n                    // onClick={(e) => e.stopPropagation()} // Prevent click from closing dropdown\n                    ,\n                    onKeyDown: e => e.stopPropagation() // Prevent dropdown from closing on typing\n                    ,\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      fullWidth: true,\n                      placeholder: translate(\"Search interactions...\"),\n                      variant: \"outlined\",\n                      size: \"small\",\n                      value: searchTerm,\n                      onClick: e => {\n                        e.stopPropagation(); // Prevent TextField click from closing dropdown\n                        setIsInteractionDropdownOpen(true);\n                      },\n                      onKeyDown: e => {\n                        e.stopPropagation(); // Prevent keypress from closing dropdown\n                        if (e.key === \"Escape\") {\n                          handleInteractionDropdownClose(); // Allow Escape to close dropdown\n                        }\n                        if (e.key === \"Enter\") {\n                          searchTerm.trim() ? fetchData(0, searchTerm) : fetchData(0, \"\"); // 🔁 Same logic as icon button\n                        }\n                      },\n                      onChange: e => setSearchTerm(e.target.value) // Call search handler directly\n                      ,\n                      autoFocus: true,\n                      InputProps: {\n                        startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                          position: \"start\",\n                          children: /*#__PURE__*/_jsxDEV(IconButton, {\n                            \"aria-label\": \"search\",\n                            onClick: e => {\n                              e.stopPropagation();\n                              if (searchTerm.trim()) {\n                                // Only search if there's a non-empty term\n                                fetchData(0, searchTerm);\n                              } else {\n                                fetchData(0, \"\");\n                              }\n                            },\n                            children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 695,\n                              columnNumber: 17\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 683,\n                            columnNumber: 15\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 682,\n                          columnNumber: 16\n                        }, this)\n                      },\n                      sx: {\n                        \"& .MuiOutlinedInput-root\": {\n                          borderRadius: \"4px\"\n                        },\n                        \"& .MuiInputAdornment-root\": {\n                          margin: \"0 !important\"\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 11\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 9\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      maxHeight: \"352px\",\n                      overflowY: \"auto\"\n                    },\n                    onScroll: handleMenuScroll // Attach the event directly here\n                    ,\n                    children: [filteredInteractions && filteredInteractions.length === 0 && !loading && /*#__PURE__*/_jsxDEV(MenuItem, {\n                      disabled: true,\n                      children: translate(\"No interactions found\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 718,\n                      columnNumber: 13\n                    }, this), filteredInteractions && filteredInteractions.map(interaction => /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: interaction.Name,\n                      onClick: () => {\n                        handleInteractionChange(interaction.Name);\n                        // onPropertyChange(\"interaction\", interaction.Name);\n                      },\n                      children: interaction.Name\n                    }, interaction.GuideId, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 721,\n                      columnNumber: 13\n                    }, this)), loading && /*#__PURE__*/_jsxDEV(MenuItem, {\n                      disabled: true,\n                      sx: {\n                        display: \"flex\",\n                        justifyContent: \"center\"\n                      },\n                      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 734,\n                        columnNumber: 15\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 13\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 9\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 5\n              }, this), duplicateError && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#e53935',\n                  padding: 10,\n                  marginBottom: 8,\n                  fontSize: 13,\n                  textAlign: 'left'\n                },\n                children: duplicateError\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 741,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box qadpt-chkcontrol-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  padding: \"0 !important\",\n                  marginBottom: \"8px !important\"\n                },\n                children: translate(\"Title\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                size: \"small\",\n                placeholder: translate(\"Step Title\"),\n                className: \"qadpt-control-input\",\n                style: {\n                  width: \"100%\"\n                },\n                value: checklistCheckpointListProperties.title,\n                onChange: e => onPropertyChange(\"title\", e.target.value),\n                InputProps: {\n                  endAdornment: \"\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    },\n                    \"& input\": {\n                      textAlign: \"left !important\",\n                      paddingLeft: \"10px !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"auto !important\"\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box qadpt-chkcontrol-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  padding: \"0 !important\",\n                  marginBottom: \"8px !important\"\n                },\n                children: translate(\"Description\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 779,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                size: \"small\",\n                placeholder: translate(\"Step Desc\"),\n                className: \"qadpt-control-input\",\n                multiline: true,\n                minRows: 3,\n                value: checklistCheckpointListProperties.description,\n                onChange: e => onPropertyChange(\"description\", e.target.value),\n                style: {\n                  width: \"100%\"\n                },\n                InputProps: {\n                  endAdornment: \"\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    },\n                    \"& input\": {\n                      textAlign: \"left !important\",\n                      paddingLeft: \"10px !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"auto !important\"\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box qadpt-chkcontrol-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                style: {\n                  display: \"flex\",\n                  flexDirection: \"row\",\n                  alignItems: \"center\",\n                  gap: \"5px\",\n                  padding: \"0\",\n                  marginBottom: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  sx: {\n                    color: \"#444444\",\n                    fontWeight: \"600\"\n                  },\n                  children: translate(\"Redirect URL\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: redirect\n                  },\n                  style: {\n                    display: \"flex\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 814,\n                  columnNumber: 10\n                }, this), \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 812,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                style: {\n                  fontSize: \"11px\",\n                  color: \"#8d8d8d\",\n                  textAlign: \"left\",\n                  padding: \"0\",\n                  marginBottom: \"10px\"\n                },\n                children: [\"         \", translate(\"User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips\")]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                size: \"small\",\n                placeholder: translate(\"Redirection URL\"),\n                className: \"qadpt-control-input\",\n                style: {\n                  width: \"100%\"\n                },\n                value: checklistCheckpointListProperties.redirectURL,\n                onChange: e => onPropertyChange(\"redirectURL\", e.target.value),\n                InputProps: {\n                  endAdornment: \"\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    },\n                    \"& input\": {\n                      textAlign: \"left !important\",\n                      paddingLeft: \"10px !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"auto !important\"\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box qadpt-chkcontrol-box\"\n              // sx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0 !important\" }}\n              ,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  padding: \"0 0 8px 0 !important\"\n                },\n                children: translate(\"Icon\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  gap: 1,\n                  alignItems: \"center\",\n                  width: \"-webkit-fill-available\",\n                  flexWrap: \"wrap\"\n                },\n                children: icons.map(icon => /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Select Icon\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleIconClick(icon.id),\n                    sx: {\n                      border: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\n                      borderRadius: \"8px\",\n                      padding: \"8px\",\n                      background: \"#F1ECEC\"\n                    },\n                    children: icon.component\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 849,\n                    columnNumber: 25\n                  }, this)\n                }, icon.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 846,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box qadpt-chkcontrol-box\"\n              //   sx={{ flexDirection: \"column\", height: \"auto !important\",padding:\"0 !important\"}}\n              ,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  padding: \"0 0 8px 0 !important\"\n                },\n                children: translate(\"Supporting Media\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: \"165px\",\n                  height: \"auto\",\n                  margin: \"0 8px 8px 8px\",\n                  display: \"flex\",\n                  flexDirection: \"column\",\n                  alignItems: \"center\",\n                  justifyContent: \"center\",\n                  border: \"1px dashed var(--primarycolor)\",\n                  borderRadius: \"12px\",\n                  padding: \"8px\",\n                  background: \"#F1ECEC\",\n                  textAlign: \"center\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  className: \"qadpt-upload-button\",\n                  style: {\n                    height: \"auto\",\n                    padding: \"0\",\n                    width: \"100%\",\n                    display: \"flex\",\n                    flexDirection: \"row\",\n                    // Ensures icon & text are in one line\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    gap: \"6px\",\n                    color: \"#000\",\n                    backgroundColor: \"#F1ECEC\",\n                    textTransform: \"capitalize\",\n                    boxShadow: \"none\"\n                  },\n                  component: \"label\",\n                  children: [/*#__PURE__*/_jsxDEV(CloudUploadOutlinedIcon, {\n                    sx: {\n                      zoom: \"1.6\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 5\n                  }, this), \"Upload file\", /*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"file-input\",\n                    type: \"file\",\n                    multiple: true,\n                    accept: \".jpeg, .jpg, .png, .gif, .mp4\",\n                    onChange: handleFileChange,\n                    style: {\n                      display: \"none\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 913,\n                    columnNumber: 5\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 3\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  style: {\n                    fontSize: \"12px\",\n                    color: \"#A3A3A3\"\n                  },\n                  children: \".png, .jpg, .gif, .mp4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 3\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 4\n              }, this), fileError && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  color: \"#e6a957\",\n                  padding: \"0 8px\",\n                  textAlign: \"left\",\n                  width: \"-webkit-fill-available\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    marginRight: \"4px\",\n                    display: \"flex\"\n                  },\n                  dangerouslySetInnerHTML: {\n                    __html: warning\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: \"12px\"\n                  },\n                  children: fileError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 935,\n                  columnNumber: 5\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 3\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: \"-webkit-fill-available\"\n                },\n                children: [files.map((file, index) => /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  justifyContent: \"space-between\",\n                  sx: {\n                    borderRadius: \"12px\",\n                    padding: \"8px\",\n                    margin: \"8px\",\n                    backgroundColor: \"#e5dada\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: URL.createObjectURL(file),\n                    alt: `uploaded-${index}`,\n                    style: {\n                      width: \"20px\",\n                      height: \"20px\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 957,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      flex: 1,\n                      fontSize: \"14px\",\n                      wordBreak: \"break-word\"\n                    },\n                    children: file.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 963,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleDeleteFile(index),\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: deletestep\n                      },\n                      style: {\n                        zoom: \"1\",\n                        display: \"flex\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 969,\n                      columnNumber: 4\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 968,\n                    columnNumber: 13\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 11\n                }, this)), gifFile && /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  justifyContent: \"space-between\",\n                  sx: {\n                    borderRadius: \"12px\",\n                    padding: \"8px\",\n                    margin: \"5px\",\n                    backgroundColor: \"#e5dada\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: URL.createObjectURL(gifFile),\n                    alt: \"uploaded-gif\",\n                    style: {\n                      width: \"20px\",\n                      height: \"20px\",\n                      borderRadius: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      flex: 1,\n                      fontSize: \"14px\",\n                      wordBreak: \"break-word\"\n                    },\n                    children: gifFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 992,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: handleDeleteGif,\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: deletestep\n                      },\n                      style: {\n                        zoom: \"1\",\n                        display: \"flex\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 998,\n                      columnNumber: 4\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 997,\n                    columnNumber: 13\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 976,\n                  columnNumber: 11\n                }, this), videoFile && /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  justifyContent: \"space-between\",\n                  sx: {\n                    border: \"1px solid #0a6\",\n                    borderRadius: \"5px\",\n                    padding: \"8px\",\n                    marginBottom: \"5px\",\n                    width: \"196px\",\n                    backgroundColor: \"#e6ffe6\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"video\", {\n                    width: \"40\",\n                    height: \"40\",\n                    controls: true,\n                    children: [/*#__PURE__*/_jsxDEV(\"source\", {\n                      src: URL.createObjectURL(videoFile),\n                      type: \"video/mp4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1014,\n                      columnNumber: 17\n                    }, this), \"Your browser does not support the video tag.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      flex: 1,\n                      ml: 2,\n                      fontSize: \"14px\",\n                      wordBreak: \"break-word\"\n                    },\n                    children: videoFile.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1017,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: handleDeleteVideo,\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: deletestep\n                      },\n                      style: {\n                        zoom: 0.7\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1021,\n                      columnNumber: 17\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 13\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 9\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 942,\n                columnNumber: 7\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 3\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box qadpt-chkcontrol-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  paddingBottom: \"8 !important\"\n                },\n                children: translate(\"Media Title\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                variant: \"outlined\",\n                size: \"small\",\n                placeholder: translate(\"Media Title\"),\n                className: \"qadpt-control-input\",\n                style: {\n                  width: \"100%\"\n                },\n                value: checklistCheckpointListProperties.mediaTitle,\n                onChange: e => onPropertyChange(\"mediaTitle\", e.target.value),\n                InputProps: {\n                  endAdornment: \"\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    },\n                    \"& input\": {\n                      textAlign: \"left !important\",\n                      paddingLeft: \"10px !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"auto !important\"\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-box qadpt-chkcontrol-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  paddingBottom: \"8px !important\"\n                },\n                children: translate(\"Media Description\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1064,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                value: checklistCheckpointListProperties.mediaDescription,\n                onChange: e => {\n                  let value = e.target.value;\n                  if (value.length > 200) {\n                    value = value.slice(0, 200);\n                  }\n                  onPropertyChange(\"mediaDescription\", value);\n                },\n                variant: \"outlined\",\n                size: \"small\",\n                placeholder: translate(\"Media Desc\"),\n                className: \"qadpt-control-input\",\n                multiline: true,\n                minRows: 3,\n                style: {\n                  width: \"100%\"\n                },\n                helperText: `${((_checklistCheckpointL = checklistCheckpointListProperties.mediaDescription) === null || _checklistCheckpointL === void 0 ? void 0 : _checklistCheckpointL.length) || 0}/200`,\n                InputProps: {\n                  endAdornment: \"\",\n                  sx: {\n                    \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                      border: \"none\"\n                    },\n                    \"& fieldset\": {\n                      border: \"none\"\n                    },\n                    \"& input\": {\n                      textAlign: \"left !important\",\n                      paddingLeft: \"10px !important\"\n                    },\n                    \"&.MuiInputBase-root\": {\n                      height: \"auto !important\"\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-drawerFooter\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleApplyChanges,\n            className: \"qadpt-btn\",\n            children: \"Apply\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1102,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 4\n    }, this)\n  }, void 0, false);\n};\n_s(CheckPointAddPopup, \"wj0jNNbqoO4yMtbmwqiSCoAW5ZE=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = CheckPointAddPopup;\nexport default CheckPointAddPopup;\nvar _c;\n$RefreshReg$(_c, \"CheckPointAddPopup\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "InputAdornment", "FormControl", "Select", "MenuItem", "<PERSON><PERSON><PERSON>", "CircularProgress", "CloseIcon", "useTranslation", "useDrawerStore", "SearchIcon", "deletestep", "chkicn1", "chkicn2", "chkicn3", "chkicn4", "chkicn5", "chkicn6", "redirect", "warning", "ArrowBackIosNewOutlinedIcon", "CloudUploadOutlinedIcon", "getAllGuides", "AccountContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CheckPointAddPopup", "checklistCheckpointProperties", "setChecklistCheckpointProperties", "_s", "_checklistCheckpointL", "t", "translate", "duplicateError", "setDuplicateError", "checklistGuideMetaData", "setCheckPointsEditPopup", "checkpointsEditPopup", "titlePopup", "setTitlePopup", "setDesignPopup", "titleColor", "setTitleColor", "checkpointsPopup", "setCheckPointsPopup", "checkpointTitleColor", "setCheckpointTitleColor", "checkpointTitleDescription", "setCheckpointTitleDescription", "checkpointIconColor", "setCheckpointIconColor", "setUnlockCheckPointInOrder", "unlockCheckPointInOrder", "checkPointMessage", "setCheckPointMessage", "setCheckPointsAddPopup", "updateChecklistCheckPoints", "updateChecklistCheckPointItem", "setIsUnSavedChanges", "isUnSavedChanges", "state", "selectedInteraction", "setSelectedInteraction", "searchTerm", "setSearchTerm", "checklistCheckpointListProperties", "setChecklistCheckpointListProperties", "id", "interaction", "title", "description", "redirectURL", "icon", "supportingMedia", "mediaTitle", "mediaDescription", "handleCheckPointIconColorChange", "e", "target", "value", "handleCheckPointTitleColorChange", "handleCheckPointDescriptionColorChange", "interactions", "setInteractions", "selectedInteractionData", "filteredInteractions", "find", "Name", "Description", "TargetUrl", "targetUrl", "GuideId", "handleClose", "handledesignclose", "handleSizeChange", "sizeInPx", "onPropertyChange", "onReselectElement", "key", "prevState", "applyclicked", "setapplyClisked", "handleApplyChanges", "_checklistCheckpointP", "setFileError", "trim", "isDuplicate", "checkpointsList", "some", "cp", "updatedCheckpoint", "prev", "handleEditClick", "skip", "setSkip", "loading", "setLoading", "hasMore", "setHasMore", "setFilteredInteractions", "accountId", "isSearching", "setIsSearching", "top", "fetchData", "newSkip", "newsearchTerm", "filters", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "push", "data", "newInteractions", "results", "length", "error", "console", "handleMenuScroll", "event", "currentTarget", "scrollTop", "scrollHeight", "clientHeight", "isInteractionDropdownOpen", "setIsInteractionDropdownOpen", "handleInteractionChange", "newValue", "handleInteractionDropdownOpen", "handleInteractionDropdownClose", "handleSearch", "term", "icons", "setIcons", "component", "dangerouslySetInnerHTML", "__html", "style", "zoom", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selected", "setError", "handleIconClick", "prevIcons", "map", "selectedIcon", "_selectedIcon$compone", "svgElement", "props", "base64Icon", "svgToBase64", "setIcon", "svgString", "btoa", "handleFileUpload", "_event$target$files", "file", "files", "isIco", "name", "endsWith", "img", "Image", "src", "URL", "createObjectURL", "onload", "width", "height", "alt", "initialSelectedIcon", "_initialSelectedIcon$", "setFiles", "gifFile", "setGifFile", "videoFile", "setVideoFile", "imageType", "setImageType", "fileError", "handleFileChange", "newFiles", "Array", "from", "isAllGif", "every", "type", "isAllMp4", "isAllImages", "includes", "isMixedType", "newImageType", "prevFiles", "updatedFiles", "sort", "a", "b", "nameA", "match", "parseInt", "nameB", "base64Files", "Promise", "all", "Type", "Base64", "fileToBase64", "updatedMedia", "resolve", "reject", "reader", "FileReader", "readAsDataURL", "result", "onerror", "handleDeleteFile", "index", "filter", "_", "i", "prevProperties", "handleDeleteGif", "_file$Name", "toLowerCase", "handleDeleteVideo", "_file$Name2", "children", "className", "onClick", "checkpoints", "size", "sx", "padding", "variant", "fullWidth", "borderRadius", "margin", "open", "onOpen", "onClose", "onChange", "displayEmpty", "textAlign", "borderColor", "border", "MenuProps", "PaperProps", "maxHeight", "overflowY", "max<PERSON><PERSON><PERSON>", "whiteSpace", "wordBreak", "overflowWrap", "borderWidth", "renderValue", "position", "left", "right", "zIndex", "backgroundColor", "borderBottom", "onKeyDown", "stopPropagation", "placeholder", "autoFocus", "InputProps", "startAdornment", "onScroll", "disabled", "justifyContent", "color", "marginBottom", "fontSize", "endAdornment", "paddingLeft", "multiline", "minRows", "flexDirection", "alignItems", "gap", "fontWeight", "flexWrap", "background", "textTransform", "boxShadow", "multiple", "accept", "marginRight", "flex", "controls", "ml", "paddingBottom", "slice", "helperText", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/checklist/CheckpointAddPopup.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect, useRef, useContext, useCallback } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch, Tooltip, CircularProgress, ClickAwayListener } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport SearchIcon from \"@mui/icons-material/Search\";\r\nimport AddCircleOutlineIcon from \"@mui/icons-material/AddCircleOutline\";\r\nimport InsertPhotoIcon from \"@mui/icons-material/InsertPhoto\";\r\nimport PersonIcon from \"@mui/icons-material/Person\";\r\nimport FavoriteIcon from \"@mui/icons-material/Favorite\";\r\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\r\nimport ErrorOutlineIcon from \"@mui/icons-material/ErrorOutline\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n    Solid,\r\n    editicon,\r\n    deletestep,\r\n\tchkicn1,\r\n\tchkicn2,\r\n\tchkicn3,\r\n\tchkicn4,\r\n\tchkicn5,\r\n\tchkicn6,\r\n\tredirect,\r\n\twarning,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';\r\nimport { getAllGuides } from \"../../services/GuideListServices\";\r\nimport { AccountContext } from \"../login/AccountContext\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport '../../styles/rtl_styles.scss';\r\n\r\n\r\nconst CheckPointAddPopup = ({ checklistCheckpointProperties, setChecklistCheckpointProperties }: { checklistCheckpointProperties: any; setChecklistCheckpointProperties:any}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [duplicateError, setDuplicateError] = useState<string | null>(null);\r\n\tconst {\r\n\t\tchecklistGuideMetaData,\r\n        setCheckPointsEditPopup,\r\n        checkpointsEditPopup,\r\n        titlePopup,\r\n        setTitlePopup,\r\n        setDesignPopup,\r\n        titleColor,\r\n        setTitleColor,\r\n        checkpointsPopup,\r\n        setCheckPointsPopup,\r\n        checkpointTitleColor,\r\n        setCheckpointTitleColor,\r\n        checkpointTitleDescription,\r\n        setCheckpointTitleDescription,\r\n        checkpointIconColor,\r\n        setCheckpointIconColor,\r\n        setUnlockCheckPointInOrder,\r\n\t    unlockCheckPointInOrder,\r\n        checkPointMessage,\r\n        setCheckPointMessage,\r\n\t\tsetCheckPointsAddPopup,\r\n\t\tupdateChecklistCheckPoints,\r\n\t\tupdateChecklistCheckPointItem,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tisUnSavedChanges\r\n\t\r\n      \r\n\t\r\n    } = useDrawerStore((state: any) => state);\r\n    \r\n\r\n\tconst [selectedInteraction, setSelectedInteraction] = useState<string>('');\r\n\tconst [searchTerm, setSearchTerm] = useState('');\r\n\r\n\tconst [checklistCheckpointListProperties, setChecklistCheckpointListProperties] = useState<any>({\r\n\t\tid:'',\r\n\t\tinteraction: '',\r\n\t\ttitle: '',\r\n\t\tdescription: '',\r\n\t\tredirectURL: '',\r\n\t\ticon: '',\r\n\t\tsupportingMedia: '',\r\n\t\tmediaTitle: '',\r\n\t\tmediaDescription: '',\r\n\t});\r\n\t\r\n\r\n    const handleCheckPointIconColorChange = (e: any) => setCheckpointIconColor(e.target.value);\r\n    const handleCheckPointTitleColorChange = (e: any) => setCheckpointTitleColor(e.target.value);\r\n\tconst handleCheckPointDescriptionColorChange = (e: any) => setCheckpointTitleColor(e.target.value);\r\n\tconst [interactions, setInteractions] = useState<any[]>([]);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (selectedInteraction) {\r\n\t\t\tconst selectedInteractionData = filteredInteractions.find(interaction => interaction.Name === selectedInteraction);\r\n\t\t\t\r\n\t\t\tif (selectedInteractionData) {\r\n\t\t\t\tsetChecklistCheckpointListProperties({\r\n\t\t\t\t\tinteraction: selectedInteraction,\r\n\t\t\t\t\ttitle: selectedInteractionData.Name || '',\r\n\t\t\t\t\tdescription: selectedInteractionData.Description || '',\r\n\t\t\t\t\tredirectURL: selectedInteractionData.TargetUrl || '',\r\n\t\t\t\t\ticon: selectedInteractionData.targetUrl,\r\n\t\t\t\t\tsupportingMedia: '',\r\n\t\t\t\t\tmediaTitle: selectedInteractionData.Name || '',\r\n\t\t\t\t\tmediaDescription: selectedInteractionData.description || '',\r\n\t\t\t\t\tid:selectedInteractionData.GuideId||'',\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}, [selectedInteraction, interactions, searchTerm]);\r\n\t\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetCheckPointsAddPopup(false);\r\n\t\t};\r\n\t\tconst handledesignclose = () => {\r\n\t\t\tsetDesignPopup(false);\r\n\t\t};\r\n\t\tconst handleSizeChange = (value: number) => {\r\n\t\t\tconst sizeInPx = 16 + (value - 1) * 4;\r\n\t\t\tonPropertyChange(\"Size\", sizeInPx);\r\n\t\t};\r\n\r\n\t\tconst onReselectElement = () => {\r\n\t\t\t\r\n\t\t};\r\n\r\n\t\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t\tsetChecklistCheckpointListProperties((prevState: any) => ({\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t}));\r\n\t};\r\n\tconst [applyclicked, setapplyClisked] = useState(false);\r\n\tconst handleApplyChanges = () => {\r\n\t\tsetFileError(null);\r\n\t\tsetDuplicateError(null);\r\n\t\t\tchecklistCheckpointListProperties.icon = icon;\r\n\r\n\t\t// Check if interaction is selected\r\n\t\tif (!checklistCheckpointListProperties.interaction || checklistCheckpointListProperties.interaction.trim() === \"\") {\r\n\t\t\tsetDuplicateError(translate(\"Please select an interaction\"));\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Check for duplicate interaction\r\n\t\tconst isDuplicate = checklistCheckpointProperties.checkpointsList?.some(\r\n\t\t\t(cp: any) => cp.interaction === checklistCheckpointListProperties.interaction\r\n\t\t);\r\n\t\tif (isDuplicate) {\r\n\t\t\tsetDuplicateError(translate(\"Interaction already used\"));\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t\tconst updatedCheckpoint = {\r\n\t\t\t\t...checklistCheckpointListProperties,\r\n\t\t\t};\r\n\t\t\tupdateChecklistCheckPointItem(updatedCheckpoint);\r\n\t\t\tsetapplyClisked(true);\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\r\n\t\t\tsetChecklistCheckpointProperties((prev: any) => ({\r\n\t\t\t\t...prev,\r\n\t\t\t\tcheckpointsList: [\r\n\t\t\t\t\t...prev.checkpointsList, // Keep existing checkpoints\r\n\t\t\t\t\tupdatedCheckpoint,       // Add the new one at the end\r\n\t\t\t\t],\r\n\t\t\t}));\r\n\r\n\t\t\thandleClose();\r\n\t\t};\r\n\t\t\r\n\r\n\tconst handleEditClick = () => {\r\n        setCheckPointsEditPopup(true);\r\n    }\r\n\tconst [skip, setSkip] = useState(0);\r\nconst [loading, setLoading] = useState(false);\r\nconst [hasMore, setHasMore] = useState(true);\r\nconst [filteredInteractions, setFilteredInteractions] = useState<any[]>([]);\r\n\tconst { accountId } = useContext(AccountContext);\r\n\tconst [isSearching, setIsSearching] = useState(false);\r\nconst top = 20;\r\n\r\nuseEffect(() => {\r\n  fetchData(0);  // Fetch initial data to enable scrolling\r\n}, []);\r\n\r\n\r\n\r\nconst fetchData = async (newSkip: number,newsearchTerm:string=\"\") => {\r\n\tif (newsearchTerm==\"\" && loading) return;  // Prevent duplicate calls\r\n\tif(newsearchTerm == \"\" && searchTerm != \"\") newsearchTerm = searchTerm;\r\n\tsetLoading(true);\r\n  \r\n\tconst filters = [\r\n\t  {\r\n\t\tFieldName: \"AccountId\",\r\n\t\tElementType: \"string\",\r\n\t\tCondition: \"contains\",\r\n\t\tValue: accountId,\r\n\t\tIsCustomField: false,\r\n\t  },\r\n\t  {\r\n\t\tFieldName: \"GuideType\",\r\n\t\tElementType: \"string\",\r\n\t\tCondition: \"not equal\",\r\n\t\tValue: \"Checklist\",\r\n\t\tIsCustomField: false,\r\n\t  }\r\n\t];\r\n\tif(newsearchTerm != \"\"){\r\n\t\tfilters.push({\r\n\t\t\tFieldName:\"Name\",\r\n\t\t\tElementType:\"string\",\r\n\t\t\tCondition:\"contains\",\r\n\t\t\tValue:newsearchTerm,\r\n\t\t\tIsCustomField:false\r\n\t\t});\r\n\t}\r\n  \r\n\ttry {\r\n\t  const data = await getAllGuides(newSkip, top, filters, \"\");\r\n\t const newInteractions = data?.results;\r\n   if(newsearchTerm==\"\") {\r\n\t  if (newSkip === 0) {\r\n\t\tsetInteractions(newInteractions);  \r\n\t\tsetFilteredInteractions(newInteractions);\r\n\t  } else {\r\n\t\tsetInteractions((prev) => [...prev, ...newInteractions]); \r\n\t\tsetFilteredInteractions((prev) => [...prev, ...newInteractions]);\r\n\t  }\r\n\t}\r\n\telse {\r\n       if(newSkip === 0) setFilteredInteractions(newInteractions);\r\n\t   else setFilteredInteractions((prev) => [...prev, ...newInteractions]);\r\n\t}\r\n\t  setSkip(newSkip + top);\r\n\t  setHasMore(newInteractions.length > 0);\r\n\t} catch (error) {\r\n\t  console.error(\"Error fetching guides:\", error);\r\n\t} finally {\r\n\t  setLoading(false);\r\n\t}\r\n  };\r\n  \r\nconst handleMenuScroll = (event: React.UIEvent<HTMLDivElement>) => {\r\n\tconst target = event.currentTarget;\r\n\tconst { scrollTop, scrollHeight, clientHeight } = target;\r\n  \r\n\tif (scrollHeight - scrollTop - clientHeight < 50 && !loading && hasMore ) {\r\n\t   fetchData(skip); // no need to pass searchTerm\r\n\t}\r\n  };\r\n  \r\n  const [isInteractionDropdownOpen, setIsInteractionDropdownOpen] = useState(false);\r\n  const handleInteractionChange = (newValue: string) => {\r\n\t  setSelectedInteraction(newValue);\r\n\t  onPropertyChange(\"interaction\", newValue);\r\n\t  setIsInteractionDropdownOpen(false);\r\n\t  setSearchTerm('');\r\n\t\tsetDuplicateError(null); // Clear duplicate error when selecting a new interaction\r\n  };\r\n\r\n  const handleInteractionDropdownOpen = () => {\r\n\t  setIsInteractionDropdownOpen(true);\r\n\t  if (searchTerm.trim()) {\r\n\t\tfetchData(0, searchTerm); // Or your logic to update `filteredInteractions`\r\n\t  } else {\r\n\t\tfetchData(0, \"\"); // Optional: load default or all interactions if search is empty\r\n\t  }\r\n  };\r\n\r\n  const handleInteractionDropdownClose = () => {\r\n\t  setIsInteractionDropdownOpen(false);\r\n\t  setSearchTerm('');\r\n  };\r\nconst handleSearch =  (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\tconst term = event.target.value;\r\n\tsetSearchTerm(term);\r\n  \r\n\tif (!term.trim()) {\r\n\t  // If search is cleared\r\n\t  setFilteredInteractions(interactions);\r\n\t  setIsSearching(false);\r\n\t} else {\r\n\t//   const filtered = interactions.filter((interaction) =>\r\n\t// \tinteraction.Name.toLowerCase().includes(term.toLowerCase())\r\n\t//   );\r\n\t//   setFilteredInteractions(filtered);\r\n\tsetIsSearching(true);\r\n\tsetFilteredInteractions([]);\r\n\t fetchData(0,term);\r\n\t}\r\n  };\r\n  \r\n\t  \r\n  const [icons, setIcons] = useState<any[]>([\r\n\t{ id: 1, component: <span dangerouslySetInnerHTML={{ __html: chkicn1 }} style={{ zoom: 1 ,display:\"flex\"}} />, selected: true },\r\n\t{ id: 2, component:  <span dangerouslySetInnerHTML={{ __html: chkicn2 }} style={{ zoom: 1 ,display:\"flex\"}} />, selected: false },\r\n\t{ id: 3, component:  <span dangerouslySetInnerHTML={{ __html: chkicn3 }} style={{ zoom: 1,display:\"flex\" }} />, selected: false },\r\n\t  { id: 4, component:  <span dangerouslySetInnerHTML={{ __html: chkicn4 }} style={{ zoom: 1,display:\"flex\" }} /> , selected: false },\r\n\t  { id: 5, component:  <span dangerouslySetInnerHTML={{ __html: chkicn5 }} style={{ zoom: 1 ,display:\"flex\"}} />, selected: false },\r\n\t  { id: 6, component:  <span dangerouslySetInnerHTML={{ __html: chkicn6 }} style={{ zoom: 1 ,display:\"flex\"}} />, selected: false },\r\n\t]);\r\nconst [error, setError] = useState<string | null>(null);\r\n\r\nconst handleIconClick = async (id: number) => {\r\n    setIcons(prevIcons =>\r\n        prevIcons.map(icon => ({\r\n            ...icon,\r\n            selected: icon.id === id,\r\n        }))\r\n    );\r\n\r\n    const selectedIcon = icons.find(icon => icon.id === id);\r\n    if (selectedIcon) {\r\n        const svgElement = selectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n        if (svgElement) {\r\n            const base64Icon = svgToBase64(svgElement);\r\n            setIcon(base64Icon);\r\n            checklistCheckpointListProperties.icon=base64Icon\r\n        }\r\n    }\r\n};\r\n\r\n// Helper function to convert SVG to Base64\r\nconst svgToBase64 = (svgString: string): string => {\r\n    return `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n};\r\n\r\n\r\nconst handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\tconst file = event.target.files?.[0];\r\n\tif (!file) return;\r\n\r\n\tconst isIco = file.name.endsWith(\".ico\");\r\n\r\n\t// Validate the file type and size\r\n\tconst img = new Image();\r\n\timg.src = URL.createObjectURL(file);\r\n\timg.onload = () => {\r\n\t\tif (!isIco || img.width > 64 || img.height > 64) {\r\n\t\t\tsetError(translate(\"Please upload an .ico file less than 64x64px\"));\r\n\t\t} else {\r\n\t\t\tsetError(null);\r\n\t\t\tsetIcons(prevIcons => [\r\n\t\t\t\t...prevIcons,\r\n\t\t\t\t{ id: prevIcons.length + 1, component: <img src={img.src} alt=\"Custom Icon\" width={24} />, selected: false },\r\n\t\t\t]);\r\n\t\t}\r\n\t};\r\n\t};\r\n\t\r\n\r\n\t\r\n\tconst [icon, setIcon] = useState<any>();\r\n\r\nuseEffect(() => {\r\n  const initialSelectedIcon = icons.find(icon => icon.selected);\r\n  if (initialSelectedIcon && !checklistCheckpointListProperties.icon) {\r\n        const svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n        if (svgElement) {\r\n            const base64Icon = svgToBase64(svgElement);\r\n            setIcon(base64Icon);\r\n\t\t\tchecklistCheckpointListProperties.icon=base64Icon\r\n\r\n    }\r\n  }\r\n}, []); \r\n\r\n\tconst [files, setFiles] = useState<File[]>([]);\r\n\tconst [gifFile, setGifFile] = useState<File | null>(null);\r\n\tconst [videoFile, setVideoFile] = useState<File | null>(null);\r\n\tconst [imageType, setImageType] = useState<string | null>(null); // Track locked image type\r\n\tconst [fileError, setFileError] = useState<string | null>(null);\r\n\r\n\tconst handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tsetFileError(null);\r\n\t\tif (!event.target.files) return;\r\n\t\r\n\t\tconst newFiles = Array.from(event.target.files);\r\n\t\r\n\t\tconst isAllGif = newFiles.every(file => file.type === \"image/gif\");\r\n\t\tconst isAllMp4 = newFiles.every(file => file.type === \"video/mp4\");\r\n\t\tconst isAllImages = newFiles.every(file =>\r\n\t\t\t[\"image/jpeg\", \"image/png\", \"image/jpg\"].includes(file.type)\r\n\t\t);\r\n\t\tconst isMixedType = !(isAllGif || isAllMp4 || isAllImages);\r\n\t\r\n\t\tif (isMixedType) {\r\n\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\treturn;\r\n\t\t}\r\n\t\r\n\t\tif (gifFile) {\r\n\t\t\tif (isAllGif) {\r\n\t\t\t\tsetFileError(translate(\"Only one GIF is allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t} else {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\tif (videoFile) {\r\n\t\t\tif (isAllMp4) {\r\n\t\t\t\tsetFileError(translate(\"Only one Video is allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t} else {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\tif (files.length > 0) {\r\n\t\t\tif (!isAllImages) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (imageType && !newFiles.every(file => file.type === imageType)) {\r\n\t\t\t\tsetFileError(translate(\"Mixed file formats are not allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\t\r\n\t\tif (isAllGif) {\r\n\t\t\tif (newFiles.length > 1) {\r\n\t\t\t\tsetFileError(translate(\"Only one GIF is allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tsetGifFile(newFiles[0]);\r\n\t\t} else if (isAllMp4) {\r\n\t\t\tif (newFiles.length > 1) {\r\n\t\t\t\tsetFileError(translate(\"Only one Video is allowed.\"));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tsetVideoFile(newFiles[0]);\r\n\t\t} else if (isAllImages) {\r\n\t\t\tconst newImageType = newFiles[0].type;\r\n\t\t\tif (!imageType) {\r\n\t\t\t\tsetImageType(newImageType); // Lock the image type\r\n\t\t\t}\r\n\t\t\tsetFiles(prevFiles => {\r\n\t\t\t\tconst updatedFiles = [...prevFiles, ...newFiles];\r\n\t\t\t\tupdatedFiles.sort((a, b) => {\r\n\t\t\t\t\tconst nameA = a.name.match(/\\d+/) ? parseInt(a.name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\tconst nameB = b.name.match(/\\d+/) ? parseInt(b.name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\t\treturn nameA - nameB;\r\n\t\t\t\t});\r\n\t\t\t\treturn updatedFiles;\r\n\t\t\t});\r\n\t\t}\r\n\t\r\n\t\tconst base64Files = await Promise.all(\r\n\t\t\tnewFiles.map(async (file) => ({\r\n\t\t\t\tName: file.name,\r\n\t\t\t\tType: file.type,\r\n\t\t\t\tBase64: await fileToBase64(file),\r\n\t\t\t}))\r\n\t\t);\r\n\t\r\n\t\tsetChecklistCheckpointListProperties((prevState: any) => {\r\n\t\t\tconst updatedMedia = [...(prevState.supportingMedia || []), ...base64Files];\r\n\t\t\tupdatedMedia.sort((a, b) => {\r\n\t\t\t\tconst nameA = a.Name.match(/\\d+/) ? parseInt(a.Name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\tconst nameB = b.Name.match(/\\d+/) ? parseInt(b.Name.match(/\\d+/)![0], 10) : 0;\r\n\t\t\t\treturn nameA - nameB;\r\n\t\t\t});\r\n\t\t\treturn {\r\n\t\t\t\t...prevState,\r\n\t\t\t\tsupportingMedia: updatedMedia,\r\n\t\t\t};\r\n\t\t});\r\n\t};\r\n\t\r\n\t\r\n\t  \r\n\tconst fileToBase64 = (file: File): Promise<string> => {\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t\treader.onload = () => resolve(reader.result as string);\r\n\t\t\treader.onerror = error => reject(error);\r\n\t\t});\r\n\t};\r\n\t\r\n\t\r\n\t\r\n\tconst handleDeleteFile = (index: number) => {\r\n\t\tsetFileError(null);\r\n\t\tsetFiles((prevFiles) => {\r\n\t\t\tconst newFiles = prevFiles.filter((_, i) => i !== index);\r\n\t\t\tif (newFiles.length === 0) setImageType(null); // Reset image lock\r\n\t\r\n\t\t\tsetChecklistCheckpointListProperties((prevProperties: any) => ({\r\n\t\t\t\t...prevProperties,\r\n\t\t\t\tsupportingMedia: prevProperties.supportingMedia.filter((_: any, i: any) => i !== index),\r\n\t\t\t}));\r\n\t\r\n\t\t\treturn newFiles;\r\n\t\t});\r\n\t};\r\n\t\r\n\tconst handleDeleteGif = () => {\r\n\t\tsetGifFile(null);\r\n\t\tsetChecklistCheckpointListProperties((prevProperties: any) => ({\r\n\t\t\t...prevProperties,\r\n\t\t\tsupportingMedia: prevProperties.supportingMedia.filter(\r\n\t\t\t\t(file: any) => !file.Name?.toLowerCase().endsWith(\".gif\")\r\n\t\t\t),\r\n\t\t}));\r\n\t};\r\n\t\r\n\tconst handleDeleteVideo = () => {\r\n\t\tsetVideoFile(null);\r\n\t\tsetChecklistCheckpointListProperties((prevProperties: any) => ({\r\n\t\t\t...prevProperties,\r\n\t\t\tsupportingMedia: prevProperties.supportingMedia.filter(\r\n\t\t\t\t(file: any) => !file.Name?.toLowerCase().endsWith(\".mp4\")\r\n\t\t\t),\r\n\t\t}));\r\n\t};\r\n\t\r\n\t\r\n\t\t\t\t  \r\n\t  \r\n\t\r\n\treturn (\r\n\t\t\t\r\n\t\t<>\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\taria-label=\"back\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Step\")} {checklistGuideMetaData[0].checkpoints.checkpointsList.length + 1}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\r\n\r\n\t\t\t\t\t\r\n\t\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t<Box\r\n    id=\"qadpt-designpopup\"\r\n    className=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n    // sx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0px !important\" }}\r\n  >\r\n    <Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Interaction\")}\r\n    </Typography>\r\n\r\n    <FormControl\r\n      variant=\"outlined\"\r\n      fullWidth\r\n      className=\"qadpt-control-input\"\r\n      sx={{\r\n        width: \"100% !important\",\r\n        borderRadius: \"12px\",\r\n        padding: \"0\",\r\n        margin: \"0 !important\",\r\n      }}\r\n    >\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\topen={isInteractionDropdownOpen}\r\n\t\t\t\t\t\t\t\t\t\tonOpen={handleInteractionDropdownOpen}\r\n\t\t\t\t\t\t\t\t\t\tonClose={handleInteractionDropdownClose}\r\n        value={selectedInteraction}\r\n        onChange={(e) => {\r\n          const newValue = e.target.value as string;\r\n          setSelectedInteraction(newValue);\r\n          onPropertyChange(\"interaction\", newValue);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\r\n        name=\"ShowUpon\"\r\n        displayEmpty\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\t\ttextAlign: \"left\", \r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderColor: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-notchedOutline\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder : \"none !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t  \"&.MuiInputBase-root\":{height:\"35px !important\"}\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\r\n        MenuProps={{\r\n          PaperProps: {\r\n            sx: {\r\n              maxHeight: 430,\r\n\t\t\t\t  overflowY: \"auto\",\r\n\t\t\t\t  maxWidth: \"220px\",\r\n\t\t\t\t  \"& ul li\": {\r\n\t\t\t\t\twhiteSpace: \"normal\",\r\n\t\t\t\t\twordBreak: \"break-word\", \r\n\t\t\t\t\toverflowWrap: \"break-word\", \r\n\t\t\t\t\tmaxWidth: \"100%\",\r\n\t\t\t\t  },\r\n\t\t\t\t  \"& .MuiFormControl-root .MuiInputBase-root\": {\r\n\t\t\t\t\t  borderRadius: \"12px\",\r\n\t\t\t\t\t  padding:\"1px !important\"\r\n\t\t\t\t  },\r\n\t\t\t\t  \"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t  borderColor: \"#ccc !important\", \r\n\t\t\t\t\t},\r\n\t\t\t\t\t\"&.Mui-focused\": {\r\n\t\t\t\t\t  borderColor: \"#ccc !important\", \r\n\t\t\t\t\t},\r\n\t\t\t\t  },\r\n\t\t\t\t  \"& .MuiOutlinedInput-notchedOutline\": {\r\n\t\t\t\t\t  borderColor: \"#ccc !important\",\r\n\t\t\t\t\tborderWidth:\"1px !important\"  \r\n\t\t\t\t  },\r\n            },\r\n\t\t\t},\r\n\t\t\tonClose: handleInteractionDropdownClose,\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\trenderValue={(selected) => selected || translate(\"Select Interaction\")}\r\n      >\r\n\r\n        <Box\r\n          sx={{\r\n            position: \"sticky\",\r\n            top: 0,\r\n            left: 0,\r\n            right: 0,\r\n            zIndex: 1,\r\n            backgroundColor: \"white\",\r\n            padding: \"8px\",\r\n            borderBottom: \"1px solid #eee\",\r\n          }}\r\n         // onClick={(e) => e.stopPropagation()} // Prevent click from closing dropdown\r\n          onKeyDown={(e) => e.stopPropagation()} // Prevent dropdown from closing on typing\r\n        >\r\n          <TextField\r\n            fullWidth\r\n\t\t\t\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Search interactions...\")}\r\n            variant=\"outlined\"\r\n            size=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={searchTerm}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\te.stopPropagation(); // Prevent TextField click from closing dropdown\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetIsInteractionDropdownOpen(true);\r\n\t\t\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t  onKeyDown={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\te.stopPropagation(); // Prevent keypress from closing dropdown\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (e.key === \"Escape\") {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  handleInteractionDropdownClose(); // Allow Escape to close dropdown\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (e.key === \"Enter\" ) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsearchTerm.trim()?fetchData(0, searchTerm): fetchData(0, \"\"); // 🔁 Same logic as icon button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t  onChange={(e) => setSearchTerm(e.target.value)}// Call search handler directly\r\n\t\t\t\t\t\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstartAdornment: (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  <InputAdornment position=\"start\">\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t  aria-label=\"search\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t  onClick={(e) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\te.stopPropagation();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (searchTerm.trim()) {  // Only search if there's a non-empty term\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  fetchData(0, searchTerm);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\telse{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfetchData(0, \"\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t  <SearchIcon />\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t  </InputAdornment>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiOutlinedInput-root\": { borderRadius: \"4px\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\"& .MuiInputAdornment-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmargin:\"0 !important\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n          />\r\n        </Box>\r\n\r\n        {/* Scrollable Content */}\r\n        <Box\r\n          sx={{\r\n            maxHeight: \"352px\",\r\n            overflowY: \"auto\",\r\n          }}\r\n          onScroll={handleMenuScroll} // Attach the event directly here\r\n        >\r\n          {filteredInteractions && filteredInteractions.length === 0 && !loading && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem disabled>{translate(\"No interactions found\")}</MenuItem>\r\n          )}\r\n          {filteredInteractions && filteredInteractions.map((interaction) => (\r\n            <MenuItem\r\n              key={interaction.GuideId}\r\n              value={interaction.Name}\r\n              onClick={() => {\r\n\t\t\t\thandleInteractionChange(interaction.Name);\r\n\t\t\t\t // onPropertyChange(\"interaction\", interaction.Name);\r\n              }}\r\n            >\r\n              {interaction.Name}\r\n            </MenuItem>\r\n          ))}\r\n          {loading && (\r\n            <MenuItem disabled sx={{ display: \"flex\", justifyContent: \"center\" }}>\r\n              <CircularProgress size={20} />\r\n            </MenuItem>\r\n          )}\r\n        </Box>\r\n      </Select>\r\n    </FormControl>\r\n\t\t\t\t\t\t\t\t{duplicateError && (\r\n\t\t\t\t\t\t\t\t\t<div style={{ color: '#e53935', padding: 10, marginBottom: 8, fontSize: 13, textAlign: 'left' }}>\r\n\t\t\t\t\t\t\t\t\t\t{duplicateError}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t)}\r\n  </Box>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }}>{translate(\"Title\")}</Typography>\r\n\t\t  \t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Step Title\")}\r\n                                            className=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.title}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"title\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 !important\", marginBottom: \"8px !important\" }} >{translate(\"Description\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Step Desc\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tmultiline\r\n\t\t\t\t\t\t\t\t\tminRows={3}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.description}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"description\", e.target.value)}\r\n\r\n                                            style={{width:\"100%\"}}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div className=\"qadpt-control-label\" style={{display:\"flex\",flexDirection:\"row\",alignItems:\"center\",gap:\"5px\",padding:\"0\" ,marginBottom:\"10px\"}}>\r\n\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#444444\", fontWeight: \"600\" }}>{translate(\"Redirect URL\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: redirect }} style={{ display: \"flex\" }} /> </div>\r\n\t\t\t\t\t\t\t\t<Typography style={{ fontSize: \"11px\", color: \"#8d8d8d\", textAlign: \"left\", padding: \"0\", marginBottom: \"10px\" }}>\t\t\t\t\t\t\t\t\t{translate(\"User will be navigated to redirected URL for triggering the interactions.Helpful for Tooltips\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Redirection URL\")}\r\n                                            className=\"qadpt-control-input\"\r\n                                            style={{width: \"100%\"}}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.redirectURL}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"redirectURL\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\r\n\t\t\t\t\t\t\t<Box id=\"qadpt-designpopup\" className=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t// sx={{ flexDirection: \"column\", height: \"auto !important\", padding: \"0 !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}\r\n\t\t\t\t\t\t\t\t>{translate(\"Icon\")}</Typography>\r\n            <Box sx={{ display: \"flex\", gap: 1, alignItems: \"center\" ,width:\"-webkit-fill-available\",flexWrap:\"wrap\"}}>\r\n                {icons.map(icon => (\r\n                    <Tooltip key={icon.id} title=\"Select Icon\">\r\n                        <IconButton\r\n                            onClick={() => handleIconClick(icon.id)}\r\n                            sx={{\r\n                                border: icon.selected ? \"2px solid var(--primarycolor)\" : \"none\",\r\n                                borderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\tbackground:\"#F1ECEC\",\r\n                            }}\r\n                        >\r\n                            {icon.component}\r\n                        </IconButton>\r\n                    </Tooltip>\r\n                ))}\r\n\r\n            \r\n            </Box>\r\n\r\n          \r\n        </Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t<Box\r\n      id=\"qadpt-designpopup\"\r\n      className=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n    //   sx={{ flexDirection: \"column\", height: \"auto !important\",padding:\"0 !important\"}}\r\n    >\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 8px 0 !important\" }}>{translate(\"Supporting Media\")}</Typography>\r\n\r\n\t  <div\r\n  style={{\r\n    width: \"165px\",\r\n    height: \"auto\",\r\n    margin: \"0 8px 8px 8px\",\r\n    display: \"flex\",\r\n    flexDirection: \"column\", \r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n    border: \"1px dashed var(--primarycolor)\",\r\n    borderRadius: \"12px\",\r\n    padding: \"8px\",\r\n    background: \"#F1ECEC\",\r\n    textAlign: \"center\",\r\n  }}\r\n>\r\n  <Button\r\n    className=\"qadpt-upload-button\"\r\n    style={{\r\n\t\theight: \"auto\",\r\n\t\tpadding: \"0\",\r\n      width: \"100%\",\r\n      display: \"flex\",\r\n      flexDirection: \"row\", // Ensures icon & text are in one line\r\n      alignItems: \"center\",\r\n      justifyContent: \"center\",\r\n      gap: \"6px\",\r\n      color: \"#000\",\r\n      backgroundColor: \"#F1ECEC\",\r\n      textTransform: \"capitalize\",\r\n      boxShadow: \"none\",\r\n    }}\r\n    component=\"label\"\r\n  >\r\n    <CloudUploadOutlinedIcon sx={{zoom:\"1.6\"}} />\r\n    Upload file\r\n    <input\r\n      id=\"file-input\"\r\n      type=\"file\"\r\n      multiple\r\n      accept=\".jpeg, .jpg, .png, .gif, .mp4\"\r\n      onChange={handleFileChange}\r\n      style={{ display: \"none\" }}\r\n    />\r\n  </Button>\r\n  \r\n  {/* File format text on the second line */}\r\n  <Typography style={{ fontSize: \"12px\", color: \"#A3A3A3\" }}>\r\n    .png, .jpg, .gif, .mp4\r\n  </Typography>\r\n</div>\r\n{fileError && (\r\n \t<div style={{ display: \"flex\", alignItems: \"center\" , color: \"#e6a957\",padding:\"0 8px\",textAlign:\"left\", width: \"-webkit-fill-available\"}}>\r\n\r\n    <span\r\n      style={{ marginRight: \"4px\",display:\"flex\" }}\r\n      dangerouslySetInnerHTML={{ __html: warning }}\r\n    />\r\n    <div style={{fontSize: \"12px\"}}>{fileError}</div>\r\n  </div>\r\n)}\r\n\r\n\r\n\r\n      {/* Display uploaded images */}\r\n      <Box sx={{width:\"-webkit-fill-available\"}}>\r\n        {files.map((file, index) => (\r\n          <Box\r\n            key={index}\r\n            display=\"flex\"\r\n            alignItems=\"center\"\r\n            justifyContent=\"space-between\"\r\n\t\t\tsx={{\r\n\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\tpadding: \"8px\",\r\n\t\t\t\tmargin: \"8px\",\r\n\t\t\t\tbackgroundColor: \"#e5dada\",\r\n\t\t\t  }}\r\n\t\t\t  \r\n          >\r\n            <img\r\n              src={URL.createObjectURL(file)}\r\n              alt={`uploaded-${index}`}\r\n              style={{ width: \"20px\", height: \"20px\", borderRadius: \"5px\" }}\r\n            />\r\n\t\t\t\t{/* //<span dangerouslySetInnerHTML={{ __html: imageIcon }} style={{ zoom: 0.7 }} /> */}\r\n            <Typography\r\n              sx={{ flex: 1, fontSize: \"14px\", wordBreak: \"break-word\" }}\r\n            >\r\n              {file.name}\r\n            </Typography>\r\n            <IconButton onClick={() => handleDeleteFile(index)} size=\"small\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: deletestep }} style={{ zoom: \"1\" ,display:\"flex\" }} />\r\n            </IconButton>\r\n          </Box>\r\n        ))}\r\n\r\n        {/* Display uploaded GIF separately */}\r\n        {gifFile && (\r\n          <Box\r\n            display=\"flex\"\r\n            alignItems=\"center\"\r\n            justifyContent=\"space-between\"\r\n            sx={{\r\n\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\tpadding: \"8px\",\r\n\t\t\t\tmargin: \"5px\",\r\n\t\t\t\tbackgroundColor: \"#e5dada\",\r\n\t\t\t  }}\r\n          >\r\n            <img\r\n              src={URL.createObjectURL(gifFile)}\r\n              alt=\"uploaded-gif\"\r\n              style={{ width: \"20px\", height: \"20px\", borderRadius: \"5px\" }}\r\n            />\r\n            <Typography\r\n              sx={{ flex: 1, fontSize: \"14px\", wordBreak: \"break-word\" }}\r\n\t\t\t  >\r\n              {gifFile.name}\r\n            </Typography>\r\n            <IconButton onClick={handleDeleteGif} size=\"small\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: deletestep }} style={{ zoom: \"1\" ,display:\"flex\" }} />\r\n            </IconButton>\r\n        </Box>\r\n    )}\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t{videoFile && (\r\n        <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\"\r\n            sx={{\r\n                border: \"1px solid #0a6\",\r\n                borderRadius: \"5px\",\r\n                padding: \"8px\",\r\n                marginBottom: \"5px\",\r\n                width: \"196px\",\r\n                backgroundColor: \"#e6ffe6\",\r\n            }}>\r\n            <video width=\"40\" height=\"40\" controls>\r\n                <source src={URL.createObjectURL(videoFile)} type=\"video/mp4\" />\r\n                Your browser does not support the video tag.\r\n            </video>\r\n            <Typography sx={{ flex: 1, ml: 2, fontSize: \"14px\", wordBreak: \"break-word\" }}>\r\n                {videoFile.name}\r\n            </Typography>\r\n            <IconButton onClick={handleDeleteVideo} size=\"small\">\r\n                <span dangerouslySetInnerHTML={{ __html: deletestep }} style={{ zoom: 0.7 }} />\r\n            </IconButton>\r\n      </Box>\r\n    )}\r\n    </Box>\r\n    </Box>\r\n\r\n\t\t\t\t\r\n\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ paddingBottom: \"8 !important\" }}>{translate(\"Media Title\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Media Title\")}\r\n                                            className=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{width: \"100%\"}}\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.mediaTitle}\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"mediaTitle\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box qadpt-chkcontrol-box\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ paddingBottom: \"8px !important\" }}>{translate(\"Media Description\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\tvalue={checklistCheckpointListProperties.mediaDescription}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\t\tlet value = e.target.value;\r\n\t\t\t\t\t\t\t\t\t\tif (value.length > 200) {\r\n\t\t\t\t\t\t\t\t\t\t\tvalue = value.slice(0, 200);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\tonPropertyChange(\"mediaDescription\", value);\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n                                            size=\"small\"\r\n\t\t\t\t\t\t\t\t\tplaceholder={translate(\"Media Desc\")}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\tmultiline\r\n\t\t\t\t\t\t\t\t\tminRows={3}\r\n\t\t\t\t\t\t\t\t\tstyle={{width: \"100%\"}}\r\n\t\t\t\t\t\t\t\t\thelperText={`${checklistCheckpointListProperties.mediaDescription?.length || 0}/200`}\r\n\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& input\": { textAlign: \"left !important\" ,paddingLeft:\"10px !important\"},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{height:\"auto !important\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\r\n\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\tApply\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n                </div>\r\n                \r\n                \r\n\t\t\t</div>\r\n\t\t\t</>\r\n\t\t);\r\n};\r\n\r\nexport default CheckPointAddPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAgBC,QAAQ,EAACC,SAAS,EAAUC,UAAU,QAAqB,OAAO;AAC9F,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,WAAW,EAAcC,MAAM,EAAEC,QAAQ,EAA+CC,OAAO,EAAEC,gBAAgB,QAA2B,eAAe;AAC1O,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,cAAc,MAA6E,yBAAyB;AAE3H,OAAOC,UAAU,MAAM,4BAA4B;AAOnD,SAMIC,UAAU,EACbC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,OAAO,EACPC,QAAQ,EACRC,OAAO,QACD,0BAA0B;AACjC,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,cAAc,QAAQ,yBAAyB;AAExD,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGtC,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,6BAA6B;EAAEC;AAA8G,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChL,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAG1B,cAAc,CAAC,CAAC;EACzC,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAgB,IAAI,CAAC;EACzE,MAAM;IACL4C,sBAAsB;IAChBC,uBAAuB;IACvBC,oBAAoB;IACpBC,UAAU;IACVC,aAAa;IACbC,cAAc;IACdC,UAAU;IACVC,aAAa;IACbC,gBAAgB;IAChBC,mBAAmB;IACnBC,oBAAoB;IACpBC,uBAAuB;IACvBC,0BAA0B;IAC1BC,6BAA6B;IAC7BC,mBAAmB;IACnBC,sBAAsB;IACtBC,0BAA0B;IAC7BC,uBAAuB;IACpBC,iBAAiB;IACjBC,oBAAoB;IAC1BC,sBAAsB;IACtBC,0BAA0B;IAC1BC,6BAA6B;IAC7BC,mBAAmB;IACnBC;EAIE,CAAC,GAAGpD,cAAc,CAAEqD,KAAU,IAAKA,KAAK,CAAC;EAG5C,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvE,QAAQ,CAAS,EAAE,CAAC;EAC1E,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC0E,iCAAiC,EAAEC,oCAAoC,CAAC,GAAG3E,QAAQ,CAAM;IAC/F4E,EAAE,EAAC,EAAE;IACLC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,EAAE;IACRC,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,EAAE;IACdC,gBAAgB,EAAE;EACnB,CAAC,CAAC;EAGC,MAAMC,+BAA+B,GAAIC,CAAM,IAAK3B,sBAAsB,CAAC2B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC1F,MAAMC,gCAAgC,GAAIH,CAAM,IAAK/B,uBAAuB,CAAC+B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC/F,MAAME,sCAAsC,GAAIJ,CAAM,IAAK/B,uBAAuB,CAAC+B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAClG,MAAM,CAACG,YAAY,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAQ,EAAE,CAAC;EAE3DC,SAAS,CAAC,MAAM;IACf,IAAIqE,mBAAmB,EAAE;MACxB,MAAMuB,uBAAuB,GAAGC,oBAAoB,CAACC,IAAI,CAAClB,WAAW,IAAIA,WAAW,CAACmB,IAAI,KAAK1B,mBAAmB,CAAC;MAElH,IAAIuB,uBAAuB,EAAE;QAC5BlB,oCAAoC,CAAC;UACpCE,WAAW,EAAEP,mBAAmB;UAChCQ,KAAK,EAAEe,uBAAuB,CAACG,IAAI,IAAI,EAAE;UACzCjB,WAAW,EAAEc,uBAAuB,CAACI,WAAW,IAAI,EAAE;UACtDjB,WAAW,EAAEa,uBAAuB,CAACK,SAAS,IAAI,EAAE;UACpDjB,IAAI,EAAEY,uBAAuB,CAACM,SAAS;UACvCjB,eAAe,EAAE,EAAE;UACnBC,UAAU,EAAEU,uBAAuB,CAACG,IAAI,IAAI,EAAE;UAC9CZ,gBAAgB,EAAES,uBAAuB,CAACd,WAAW,IAAI,EAAE;UAC3DH,EAAE,EAACiB,uBAAuB,CAACO,OAAO,IAAE;QACrC,CAAC,CAAC;MACH;IACD;EACD,CAAC,EAAE,CAAC9B,mBAAmB,EAAEqB,YAAY,EAAEnB,UAAU,CAAC,CAAC;EAElD,MAAM6B,WAAW,GAAGA,CAAA,KAAM;IACzBrC,sBAAsB,CAAC,KAAK,CAAC;EAC9B,CAAC;EACD,MAAMsC,iBAAiB,GAAGA,CAAA,KAAM;IAC/BrD,cAAc,CAAC,KAAK,CAAC;EACtB,CAAC;EACD,MAAMsD,gBAAgB,GAAIf,KAAa,IAAK;IAC3C,MAAMgB,QAAQ,GAAG,EAAE,GAAG,CAAChB,KAAK,GAAG,CAAC,IAAI,CAAC;IACrCiB,gBAAgB,CAAC,MAAM,EAAED,QAAQ,CAAC;EACnC,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM,CAEhC,CAAC;EAED,MAAMD,gBAAgB,GAAGA,CAACE,GAAQ,EAAEnB,KAAU,KAAK;IAClDb,oCAAoC,CAAEiC,SAAc,KAAM;MACzD,GAAGA,SAAS;MACZ,CAACD,GAAG,GAAGnB;IACR,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAG9G,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM+G,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChCC,YAAY,CAAC,IAAI,CAAC;IAClBtE,iBAAiB,CAAC,IAAI,CAAC;IACtB+B,iCAAiC,CAACO,IAAI,GAAGA,IAAI;;IAE9C;IACA,IAAI,CAACP,iCAAiC,CAACG,WAAW,IAAIH,iCAAiC,CAACG,WAAW,CAACqC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAClHvE,iBAAiB,CAACF,SAAS,CAAC,8BAA8B,CAAC,CAAC;MAC5D;IACD;;IAEA;IACA,MAAM0E,WAAW,IAAAH,qBAAA,GAAG5E,6BAA6B,CAACgF,eAAe,cAAAJ,qBAAA,uBAA7CA,qBAAA,CAA+CK,IAAI,CACrEC,EAAO,IAAKA,EAAE,CAACzC,WAAW,KAAKH,iCAAiC,CAACG,WACnE,CAAC;IACD,IAAIsC,WAAW,EAAE;MAChBxE,iBAAiB,CAACF,SAAS,CAAC,0BAA0B,CAAC,CAAC;MACxD;IACD;IAEC,MAAM8E,iBAAiB,GAAG;MACzB,GAAG7C;IACJ,CAAC;IACDR,6BAA6B,CAACqD,iBAAiB,CAAC;IAChDT,eAAe,CAAC,IAAI,CAAC;IACrB3C,mBAAmB,CAAC,IAAI,CAAC;IAEzB9B,gCAAgC,CAAEmF,IAAS,KAAM;MAChD,GAAGA,IAAI;MACPJ,eAAe,EAAE,CAChB,GAAGI,IAAI,CAACJ,eAAe;MAAE;MACzBG,iBAAiB,CAAQ;MAAA;IAE3B,CAAC,CAAC,CAAC;IAEHlB,WAAW,CAAC,CAAC;EACd,CAAC;EAGF,MAAMoB,eAAe,GAAGA,CAAA,KAAM;IACvB5E,uBAAuB,CAAC,IAAI,CAAC;EACjC,CAAC;EACJ,MAAM,CAAC6E,IAAI,EAAEC,OAAO,CAAC,GAAG3H,QAAQ,CAAC,CAAC,CAAC;EACpC,MAAM,CAAC4H,OAAO,EAAEC,UAAU,CAAC,GAAG7H,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8H,OAAO,EAAEC,UAAU,CAAC,GAAG/H,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8F,oBAAoB,EAAEkC,uBAAuB,CAAC,GAAGhI,QAAQ,CAAQ,EAAE,CAAC;EAC1E,MAAM;IAAEiI;EAAU,CAAC,GAAG/H,UAAU,CAAC4B,cAAc,CAAC;EAChD,MAAM,CAACoG,WAAW,EAAEC,cAAc,CAAC,GAAGnI,QAAQ,CAAC,KAAK,CAAC;EACtD,MAAMoI,GAAG,GAAG,EAAE;EAEdnI,SAAS,CAAC,MAAM;IACdoI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAE;EACjB,CAAC,EAAE,EAAE,CAAC;EAIN,MAAMA,SAAS,GAAG,MAAAA,CAAOC,OAAe,EAACC,aAAoB,GAAC,EAAE,KAAK;IACpE,IAAIA,aAAa,IAAE,EAAE,IAAIX,OAAO,EAAE,OAAO,CAAE;IAC3C,IAAGW,aAAa,IAAI,EAAE,IAAI/D,UAAU,IAAI,EAAE,EAAE+D,aAAa,GAAG/D,UAAU;IACtEqD,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMW,OAAO,GAAG,CACd;MACDC,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAEX,SAAS;MAChBY,aAAa,EAAE;IACd,CAAC,EACD;MACDJ,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,WAAW;MACtBC,KAAK,EAAE,WAAW;MAClBC,aAAa,EAAE;IACd,CAAC,CACF;IACD,IAAGN,aAAa,IAAI,EAAE,EAAC;MACtBC,OAAO,CAACM,IAAI,CAAC;QACZL,SAAS,EAAC,MAAM;QAChBC,WAAW,EAAC,QAAQ;QACpBC,SAAS,EAAC,UAAU;QACpBC,KAAK,EAACL,aAAa;QACnBM,aAAa,EAAC;MACf,CAAC,CAAC;IACH;IAEA,IAAI;MACF,MAAME,IAAI,GAAG,MAAMlH,YAAY,CAACyG,OAAO,EAAEF,GAAG,EAAEI,OAAO,EAAE,EAAE,CAAC;MAC3D,MAAMQ,eAAe,GAAGD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,OAAO;MACpC,IAAGV,aAAa,IAAE,EAAE,EAAE;QACtB,IAAID,OAAO,KAAK,CAAC,EAAE;UACpB1C,eAAe,CAACoD,eAAe,CAAC;UAChChB,uBAAuB,CAACgB,eAAe,CAAC;QACvC,CAAC,MAAM;UACRpD,eAAe,CAAE4B,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE,GAAGwB,eAAe,CAAC,CAAC;UACxDhB,uBAAuB,CAAER,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE,GAAGwB,eAAe,CAAC,CAAC;QAC/D;MACF,CAAC,MACI;QACC,IAAGV,OAAO,KAAK,CAAC,EAAEN,uBAAuB,CAACgB,eAAe,CAAC,CAAC,KACzDhB,uBAAuB,CAAER,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAE,GAAGwB,eAAe,CAAC,CAAC;MACxE;MACErB,OAAO,CAACW,OAAO,GAAGF,GAAG,CAAC;MACtBL,UAAU,CAACiB,eAAe,CAACE,MAAM,GAAG,CAAC,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACC,CAAC;EAEH,MAAMwB,gBAAgB,GAAIC,KAAoC,IAAK;IAClE,MAAM/D,MAAM,GAAG+D,KAAK,CAACC,aAAa;IAClC,MAAM;MAAEC,SAAS;MAAEC,YAAY;MAAEC;IAAa,CAAC,GAAGnE,MAAM;IAExD,IAAIkE,YAAY,GAAGD,SAAS,GAAGE,YAAY,GAAG,EAAE,IAAI,CAAC9B,OAAO,IAAIE,OAAO,EAAG;MACvEO,SAAS,CAACX,IAAI,CAAC,CAAC,CAAC;IACpB;EACC,CAAC;EAED,MAAM,CAACiC,yBAAyB,EAAEC,4BAA4B,CAAC,GAAG5J,QAAQ,CAAC,KAAK,CAAC;EACjF,MAAM6J,uBAAuB,GAAIC,QAAgB,IAAK;IACrDvF,sBAAsB,CAACuF,QAAQ,CAAC;IAChCrD,gBAAgB,CAAC,aAAa,EAAEqD,QAAQ,CAAC;IACzCF,4BAA4B,CAAC,KAAK,CAAC;IACnCnF,aAAa,CAAC,EAAE,CAAC;IAClB9B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAMoH,6BAA6B,GAAGA,CAAA,KAAM;IAC3CH,4BAA4B,CAAC,IAAI,CAAC;IAClC,IAAIpF,UAAU,CAAC0C,IAAI,CAAC,CAAC,EAAE;MACxBmB,SAAS,CAAC,CAAC,EAAE7D,UAAU,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM;MACR6D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACjB;EACD,CAAC;EAED,MAAM2B,8BAA8B,GAAGA,CAAA,KAAM;IAC5CJ,4BAA4B,CAAC,KAAK,CAAC;IACnCnF,aAAa,CAAC,EAAE,CAAC;EAClB,CAAC;EACH,MAAMwF,YAAY,GAAKX,KAA0C,IAAK;IACrE,MAAMY,IAAI,GAAGZ,KAAK,CAAC/D,MAAM,CAACC,KAAK;IAC/Bf,aAAa,CAACyF,IAAI,CAAC;IAEnB,IAAI,CAACA,IAAI,CAAChD,IAAI,CAAC,CAAC,EAAE;MAChB;MACAc,uBAAuB,CAACrC,YAAY,CAAC;MACrCwC,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,MAAM;MACP;MACA;MACA;MACA;MACAA,cAAc,CAAC,IAAI,CAAC;MACpBH,uBAAuB,CAAC,EAAE,CAAC;MAC1BK,SAAS,CAAC,CAAC,EAAC6B,IAAI,CAAC;IAClB;EACC,CAAC;EAGD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpK,QAAQ,CAAQ,CAC3C;IAAE4E,EAAE,EAAE,CAAC;IAAEyF,SAAS,eAAErI,OAAA;MAAMsI,uBAAuB,EAAE;QAAEC,MAAM,EAAEpJ;MAAQ,CAAE;MAACqJ,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,OAAO,EAAC;MAAM;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,QAAQ,EAAE;EAAK,CAAC,EAC/H;IAAEnG,EAAE,EAAE,CAAC;IAAEyF,SAAS,eAAGrI,OAAA;MAAMsI,uBAAuB,EAAE;QAAEC,MAAM,EAAEnJ;MAAQ,CAAE;MAACoJ,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,OAAO,EAAC;MAAM;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,QAAQ,EAAE;EAAM,CAAC,EACjI;IAAEnG,EAAE,EAAE,CAAC;IAAEyF,SAAS,eAAGrI,OAAA;MAAMsI,uBAAuB,EAAE;QAAEC,MAAM,EAAElJ;MAAQ,CAAE;MAACmJ,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAACC,OAAO,EAAC;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,QAAQ,EAAE;EAAM,CAAC,EAC/H;IAAEnG,EAAE,EAAE,CAAC;IAAEyF,SAAS,eAAGrI,OAAA;MAAMsI,uBAAuB,EAAE;QAAEC,MAAM,EAAEjJ;MAAQ,CAAE;MAACkJ,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAACC,OAAO,EAAC;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAGC,QAAQ,EAAE;EAAM,CAAC,EAClI;IAAEnG,EAAE,EAAE,CAAC;IAAEyF,SAAS,eAAGrI,OAAA;MAAMsI,uBAAuB,EAAE;QAAEC,MAAM,EAAEhJ;MAAQ,CAAE;MAACiJ,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,OAAO,EAAC;MAAM;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,QAAQ,EAAE;EAAM,CAAC,EACjI;IAAEnG,EAAE,EAAE,CAAC;IAAEyF,SAAS,eAAGrI,OAAA;MAAMsI,uBAAuB,EAAE;QAAEC,MAAM,EAAE/I;MAAQ,CAAE;MAACgJ,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,OAAO,EAAC;MAAM;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,QAAQ,EAAE;EAAM,CAAC,CAClI,CAAC;EACH,MAAM,CAAC5B,KAAK,EAAE6B,QAAQ,CAAC,GAAGhL,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAMiL,eAAe,GAAG,MAAOrG,EAAU,IAAK;IAC1CwF,QAAQ,CAACc,SAAS,IACdA,SAAS,CAACC,GAAG,CAAClG,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP8F,QAAQ,EAAE9F,IAAI,CAACL,EAAE,KAAKA;IAC1B,CAAC,CAAC,CACN,CAAC;IAED,MAAMwG,YAAY,GAAGjB,KAAK,CAACpE,IAAI,CAACd,IAAI,IAAIA,IAAI,CAACL,EAAE,KAAKA,EAAE,CAAC;IACvD,IAAIwG,YAAY,EAAE;MAAA,IAAAC,qBAAA;MACd,MAAMC,UAAU,IAAAD,qBAAA,GAAGD,YAAY,CAACf,SAAS,CAACkB,KAAK,CAACjB,uBAAuB,cAAAe,qBAAA,uBAApDA,qBAAA,CAAsDd,MAAM;MAC/E,IAAIe,UAAU,EAAE;QACZ,MAAME,UAAU,GAAGC,WAAW,CAACH,UAAU,CAAC;QAC1CI,OAAO,CAACF,UAAU,CAAC;QACnB9G,iCAAiC,CAACO,IAAI,GAACuG,UAAU;MACrD;IACJ;EACJ,CAAC;;EAED;EACA,MAAMC,WAAW,GAAIE,SAAiB,IAAa;IAC/C,OAAO,6BAA6BC,IAAI,CAACD,SAAS,CAAC,EAAE;EACzD,CAAC;EAGD,MAAME,gBAAgB,GAAIvC,KAA0C,IAAK;IAAA,IAAAwC,mBAAA;IACxE,MAAMC,IAAI,IAAAD,mBAAA,GAAGxC,KAAK,CAAC/D,MAAM,CAACyG,KAAK,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IACpC,IAAI,CAACC,IAAI,EAAE;IAEX,MAAME,KAAK,GAAGF,IAAI,CAACG,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC;;IAExC;IACA,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,GAAG,CAACE,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAC;IACnCK,GAAG,CAACK,MAAM,GAAG,MAAM;MAClB,IAAI,CAACR,KAAK,IAAIG,GAAG,CAACM,KAAK,GAAG,EAAE,IAAIN,GAAG,CAACO,MAAM,GAAG,EAAE,EAAE;QAChD3B,QAAQ,CAACvI,SAAS,CAAC,8CAA8C,CAAC,CAAC;MACpE,CAAC,MAAM;QACNuI,QAAQ,CAAC,IAAI,CAAC;QACdZ,QAAQ,CAACc,SAAS,IAAI,CACrB,GAAGA,SAAS,EACZ;UAAEtG,EAAE,EAAEsG,SAAS,CAAChC,MAAM,GAAG,CAAC;UAAEmB,SAAS,eAAErI,OAAA;YAAKsK,GAAG,EAAEF,GAAG,CAACE,GAAI;YAACM,GAAG,EAAC,aAAa;YAACF,KAAK,EAAE;UAAG;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAAEC,QAAQ,EAAE;QAAM,CAAC,CAC5G,CAAC;MACH;IACD,CAAC;EACD,CAAC;EAID,MAAM,CAAC9F,IAAI,EAAEyG,OAAO,CAAC,GAAG1L,QAAQ,CAAM,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,MAAM4M,mBAAmB,GAAG1C,KAAK,CAACpE,IAAI,CAACd,IAAI,IAAIA,IAAI,CAAC8F,QAAQ,CAAC;IAC7D,IAAI8B,mBAAmB,IAAI,CAACnI,iCAAiC,CAACO,IAAI,EAAE;MAAA,IAAA6H,qBAAA;MAC9D,MAAMxB,UAAU,IAAAwB,qBAAA,GAAGD,mBAAmB,CAACxC,SAAS,CAACkB,KAAK,CAACjB,uBAAuB,cAAAwC,qBAAA,uBAA3DA,qBAAA,CAA6DvC,MAAM;MACtF,IAAIe,UAAU,EAAE;QACZ,MAAME,UAAU,GAAGC,WAAW,CAACH,UAAU,CAAC;QAC1CI,OAAO,CAACF,UAAU,CAAC;QAC5B9G,iCAAiC,CAACO,IAAI,GAACuG,UAAU;MAEhD;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEL,MAAM,CAACQ,KAAK,EAAEe,QAAQ,CAAC,GAAG/M,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACgN,OAAO,EAAEC,UAAU,CAAC,GAAGjN,QAAQ,CAAc,IAAI,CAAC;EACzD,MAAM,CAACkN,SAAS,EAAEC,YAAY,CAAC,GAAGnN,QAAQ,CAAc,IAAI,CAAC;EAC7D,MAAM,CAACoN,SAAS,EAAEC,YAAY,CAAC,GAAGrN,QAAQ,CAAgB,IAAI,CAAC,CAAC,CAAC;EACjE,MAAM,CAACsN,SAAS,EAAErG,YAAY,CAAC,GAAGjH,QAAQ,CAAgB,IAAI,CAAC;EAE/D,MAAMuN,gBAAgB,GAAG,MAAOjE,KAA0C,IAAK;IAC9ErC,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI,CAACqC,KAAK,CAAC/D,MAAM,CAACyG,KAAK,EAAE;IAEzB,MAAMwB,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACpE,KAAK,CAAC/D,MAAM,CAACyG,KAAK,CAAC;IAE/C,MAAM2B,QAAQ,GAAGH,QAAQ,CAACI,KAAK,CAAC7B,IAAI,IAAIA,IAAI,CAAC8B,IAAI,KAAK,WAAW,CAAC;IAClE,MAAMC,QAAQ,GAAGN,QAAQ,CAACI,KAAK,CAAC7B,IAAI,IAAIA,IAAI,CAAC8B,IAAI,KAAK,WAAW,CAAC;IAClE,MAAME,WAAW,GAAGP,QAAQ,CAACI,KAAK,CAAC7B,IAAI,IACtC,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAACiC,QAAQ,CAACjC,IAAI,CAAC8B,IAAI,CAC5D,CAAC;IACD,MAAMI,WAAW,GAAG,EAAEN,QAAQ,IAAIG,QAAQ,IAAIC,WAAW,CAAC;IAE1D,IAAIE,WAAW,EAAE;MAChBhH,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC;MAC9D;IACD;IAEA,IAAIuK,OAAO,EAAE;MACZ,IAAIW,QAAQ,EAAE;QACb1G,YAAY,CAACxE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD;MACD,CAAC,MAAM;QACNwE,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC;QAC9D;MACD;IACD;IAEA,IAAIyK,SAAS,EAAE;MACd,IAAIY,QAAQ,EAAE;QACb7G,YAAY,CAACxE,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACrD;MACD,CAAC,MAAM;QACNwE,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC;QAC9D;MACD;IACD;IAEA,IAAIuJ,KAAK,CAAC9C,MAAM,GAAG,CAAC,EAAE;MACrB,IAAI,CAAC6E,WAAW,EAAE;QACjB9G,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC;QAC9D;MACD;MACA,IAAI2K,SAAS,IAAI,CAACI,QAAQ,CAACI,KAAK,CAAC7B,IAAI,IAAIA,IAAI,CAAC8B,IAAI,KAAKT,SAAS,CAAC,EAAE;QAClEnG,YAAY,CAACxE,SAAS,CAAC,qCAAqC,CAAC,CAAC;QAC9D;MACD;IACD;IAEA,IAAIkL,QAAQ,EAAE;MACb,IAAIH,QAAQ,CAACtE,MAAM,GAAG,CAAC,EAAE;QACxBjC,YAAY,CAACxE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QACnD;MACD;MACAwK,UAAU,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIM,QAAQ,EAAE;MACpB,IAAIN,QAAQ,CAACtE,MAAM,GAAG,CAAC,EAAE;QACxBjC,YAAY,CAACxE,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACrD;MACD;MACA0K,YAAY,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,MAAM,IAAIO,WAAW,EAAE;MACvB,MAAMG,YAAY,GAAGV,QAAQ,CAAC,CAAC,CAAC,CAACK,IAAI;MACrC,IAAI,CAACT,SAAS,EAAE;QACfC,YAAY,CAACa,YAAY,CAAC,CAAC,CAAC;MAC7B;MACAnB,QAAQ,CAACoB,SAAS,IAAI;QACrB,MAAMC,YAAY,GAAG,CAAC,GAAGD,SAAS,EAAE,GAAGX,QAAQ,CAAC;QAChDY,YAAY,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UAC3B,MAAMC,KAAK,GAAGF,CAAC,CAACpC,IAAI,CAACuC,KAAK,CAAC,KAAK,CAAC,GAAGC,QAAQ,CAACJ,CAAC,CAACpC,IAAI,CAACuC,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;UAC7E,MAAME,KAAK,GAAGJ,CAAC,CAACrC,IAAI,CAACuC,KAAK,CAAC,KAAK,CAAC,GAAGC,QAAQ,CAACH,CAAC,CAACrC,IAAI,CAACuC,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;UAC7E,OAAOD,KAAK,GAAGG,KAAK;QACrB,CAAC,CAAC;QACF,OAAOP,YAAY;MACpB,CAAC,CAAC;IACH;IAEA,MAAMQ,WAAW,GAAG,MAAMC,OAAO,CAACC,GAAG,CACpCtB,QAAQ,CAACrC,GAAG,CAAC,MAAOY,IAAI,KAAM;MAC7B/F,IAAI,EAAE+F,IAAI,CAACG,IAAI;MACf6C,IAAI,EAAEhD,IAAI,CAAC8B,IAAI;MACfmB,MAAM,EAAE,MAAMC,YAAY,CAAClD,IAAI;IAChC,CAAC,CAAC,CACH,CAAC;IAEDpH,oCAAoC,CAAEiC,SAAc,IAAK;MACxD,MAAMsI,YAAY,GAAG,CAAC,IAAItI,SAAS,CAAC1B,eAAe,IAAI,EAAE,CAAC,EAAE,GAAG0J,WAAW,CAAC;MAC3EM,YAAY,CAACb,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAC3B,MAAMC,KAAK,GAAGF,CAAC,CAACtI,IAAI,CAACyI,KAAK,CAAC,KAAK,CAAC,GAAGC,QAAQ,CAACJ,CAAC,CAACtI,IAAI,CAACyI,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC7E,MAAME,KAAK,GAAGJ,CAAC,CAACvI,IAAI,CAACyI,KAAK,CAAC,KAAK,CAAC,GAAGC,QAAQ,CAACH,CAAC,CAACvI,IAAI,CAACyI,KAAK,CAAC,KAAK,CAAC,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC7E,OAAOD,KAAK,GAAGG,KAAK;MACrB,CAAC,CAAC;MACF,OAAO;QACN,GAAG/H,SAAS;QACZ1B,eAAe,EAAEgK;MAClB,CAAC;IACF,CAAC,CAAC;EACH,CAAC;EAID,MAAMD,YAAY,GAAIlD,IAAU,IAAsB;IACrD,OAAO,IAAI8C,OAAO,CAAC,CAACM,OAAO,EAAEC,MAAM,KAAK;MACvC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,aAAa,CAACxD,IAAI,CAAC;MAC1BsD,MAAM,CAAC5C,MAAM,GAAG,MAAM0C,OAAO,CAACE,MAAM,CAACG,MAAgB,CAAC;MACtDH,MAAM,CAACI,OAAO,GAAGtG,KAAK,IAAIiG,MAAM,CAACjG,KAAK,CAAC;IACxC,CAAC,CAAC;EACH,CAAC;EAID,MAAMuG,gBAAgB,GAAIC,KAAa,IAAK;IAC3C1I,YAAY,CAAC,IAAI,CAAC;IAClB8F,QAAQ,CAAEoB,SAAS,IAAK;MACvB,MAAMX,QAAQ,GAAGW,SAAS,CAACyB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKH,KAAK,CAAC;MACxD,IAAInC,QAAQ,CAACtE,MAAM,KAAK,CAAC,EAAEmE,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;;MAE/C1I,oCAAoC,CAAEoL,cAAmB,KAAM;QAC9D,GAAGA,cAAc;QACjB7K,eAAe,EAAE6K,cAAc,CAAC7K,eAAe,CAAC0K,MAAM,CAAC,CAACC,CAAM,EAAEC,CAAM,KAAKA,CAAC,KAAKH,KAAK;MACvF,CAAC,CAAC,CAAC;MAEH,OAAOnC,QAAQ;IAChB,CAAC,CAAC;EACH,CAAC;EAED,MAAMwC,eAAe,GAAGA,CAAA,KAAM;IAC7B/C,UAAU,CAAC,IAAI,CAAC;IAChBtI,oCAAoC,CAAEoL,cAAmB,KAAM;MAC9D,GAAGA,cAAc;MACjB7K,eAAe,EAAE6K,cAAc,CAAC7K,eAAe,CAAC0K,MAAM,CACpD7D,IAAS;QAAA,IAAAkE,UAAA;QAAA,OAAK,GAAAA,UAAA,GAAClE,IAAI,CAAC/F,IAAI,cAAAiK,UAAA,eAATA,UAAA,CAAWC,WAAW,CAAC,CAAC,CAAC/D,QAAQ,CAAC,MAAM,CAAC;MAAA,CAC1D;IACD,CAAC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgE,iBAAiB,GAAGA,CAAA,KAAM;IAC/BhD,YAAY,CAAC,IAAI,CAAC;IAClBxI,oCAAoC,CAAEoL,cAAmB,KAAM;MAC9D,GAAGA,cAAc;MACjB7K,eAAe,EAAE6K,cAAc,CAAC7K,eAAe,CAAC0K,MAAM,CACpD7D,IAAS;QAAA,IAAAqE,WAAA;QAAA,OAAK,GAAAA,WAAA,GAACrE,IAAI,CAAC/F,IAAI,cAAAoK,WAAA,eAATA,WAAA,CAAWF,WAAW,CAAC,CAAC,CAAC/D,QAAQ,CAAC,MAAM,CAAC;MAAA,CAC1D;IACD,CAAC,CAAC,CAAC;EACJ,CAAC;EAMD,oBAECnK,OAAA,CAAAE,SAAA;IAAAmO,QAAA,eACCrO,OAAA;MACC4C,EAAE,EAAC,mBAAmB;MACtB0L,SAAS,EAAC,mBAAmB;MAAAD,QAAA,eAE7BrO,OAAA;QAAKsO,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC7BrO,OAAA;UAAKsO,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBACnCrO,OAAA,CAAC1B,UAAU;YACV,cAAW,MAAM;YACjBiQ,OAAO,EAAElK,WAAY;YAAAgK,QAAA,eAErBrO,OAAA,CAACL,2BAA2B;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACb9I,OAAA;YAAKsO,SAAS,EAAC,aAAa;YAAAD,QAAA,GAAE5N,SAAS,CAAC,MAAM,CAAC,EAAC,GAAC,EAACG,sBAAsB,CAAC,CAAC,CAAC,CAAC4N,WAAW,CAACpJ,eAAe,CAAC8B,MAAM,GAAG,CAAC;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzH9I,OAAA,CAAC1B,UAAU;YACVmQ,IAAI,EAAC,OAAO;YACZ,cAAW,OAAO;YAClBF,OAAO,EAAElK,WAAY;YAAAgK,QAAA,eAErBrO,OAAA,CAAClB,SAAS;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAIN9I,OAAA;UAAKsO,SAAS,EAAC,gBAAgB;UAAAD,QAAA,eAC9BrO,OAAA;YAAKsO,SAAS,EAAC,gBAAgB;YAAAD,QAAA,gBAC/BrO,OAAA,CAAC7B,GAAG;cACNyE,EAAE,EAAC,mBAAmB;cACtB0L,SAAS,EAAC;cACV;cAAA;cAAAD,QAAA,gBAEArO,OAAA,CAAC5B,UAAU;gBAACkQ,SAAS,EAAC,qBAAqB;gBAACI,EAAE,EAAE;kBAAEC,OAAO,EAAE;gBAAuB,CAAE;gBAAAN,QAAA,EAC9E5N,SAAS,CAAC,aAAa;cAAC;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAEb9I,OAAA,CAACvB,WAAW;gBACVmQ,OAAO,EAAC,UAAU;gBAClBC,SAAS;gBACTP,SAAS,EAAC,qBAAqB;gBAC/BI,EAAE,EAAE;kBACFhE,KAAK,EAAE,iBAAiB;kBACxBoE,YAAY,EAAE,MAAM;kBACpBH,OAAO,EAAE,GAAG;kBACZI,MAAM,EAAE;gBACV,CAAE;gBAAAV,QAAA,eAECrO,OAAA,CAACtB,MAAM;kBACNsQ,IAAI,EAAErH,yBAA0B;kBAChCsH,MAAM,EAAElH,6BAA8B;kBACtCmH,OAAO,EAAElH,8BAA+B;kBAC1CxE,KAAK,EAAElB,mBAAoB;kBAC3B6M,QAAQ,EAAG7L,CAAC,IAAK;oBACf,MAAMwE,QAAQ,GAAGxE,CAAC,CAACC,MAAM,CAACC,KAAe;oBACzCjB,sBAAsB,CAACuF,QAAQ,CAAC;oBAChCrD,gBAAgB,CAAC,aAAa,EAAEqD,QAAQ,CAAC;kBACzC,CAAE;kBAEJoC,IAAI,EAAC,UAAU;kBACfkF,YAAY;kBACVV,EAAE,EAAE;oBACHhE,KAAK,EAAE,iBAAiB;oBACxB2E,SAAS,EAAE,MAAM;oBACjB,0BAA0B,EAAE;sBAC3B,SAAS,EAAE;wBACVC,WAAW,EAAE;sBACd,CAAC;sBACD,eAAe,EAAE;wBAChBA,WAAW,EAAE;sBACd;oBACD,CAAC;oBACD,oCAAoC,EAAE;sBACrCC,MAAM,EAAG;oBACV,CAAC;oBACC,qBAAqB,EAAC;sBAAC5E,MAAM,EAAC;oBAAiB;kBAClD,CAAE;kBAEJ6E,SAAS,EAAE;oBACTC,UAAU,EAAE;sBACVf,EAAE,EAAE;wBACFgB,SAAS,EAAE,GAAG;wBACtBC,SAAS,EAAE,MAAM;wBACjBC,QAAQ,EAAE,OAAO;wBACjB,SAAS,EAAE;0BACZC,UAAU,EAAE,QAAQ;0BACpBC,SAAS,EAAE,YAAY;0BACvBC,YAAY,EAAE,YAAY;0BAC1BH,QAAQ,EAAE;wBACT,CAAC;wBACD,2CAA2C,EAAE;0BAC5Cd,YAAY,EAAE,MAAM;0BACpBH,OAAO,EAAC;wBACT,CAAC;wBACD,0BAA0B,EAAE;0BAC7B,SAAS,EAAE;4BACTW,WAAW,EAAE;0BACf,CAAC;0BACD,eAAe,EAAE;4BACfA,WAAW,EAAE;0BACf;wBACC,CAAC;wBACD,oCAAoC,EAAE;0BACrCA,WAAW,EAAE,iBAAiB;0BAChCU,WAAW,EAAC;wBACX;sBACM;oBACT,CAAC;oBACDd,OAAO,EAAElH;kBACF,CAAE;kBAEFiI,WAAW,EAAGlH,QAAQ,IAAKA,QAAQ,IAAItI,SAAS,CAAC,oBAAoB,CAAE;kBAAA4N,QAAA,gBAGzErO,OAAA,CAAC7B,GAAG;oBACFuQ,EAAE,EAAE;sBACFwB,QAAQ,EAAE,QAAQ;sBAClB9J,GAAG,EAAE,CAAC;sBACN+J,IAAI,EAAE,CAAC;sBACPC,KAAK,EAAE,CAAC;sBACRC,MAAM,EAAE,CAAC;sBACTC,eAAe,EAAE,OAAO;sBACxB3B,OAAO,EAAE,KAAK;sBACd4B,YAAY,EAAE;oBAChB;oBACD;oBAAA;oBACCC,SAAS,EAAGlN,CAAC,IAAKA,CAAC,CAACmN,eAAe,CAAC,CAAE,CAAC;oBAAA;oBAAApC,QAAA,eAEvCrO,OAAA,CAAC3B,SAAS;sBACRwQ,SAAS;sBACT6B,WAAW,EAAEjQ,SAAS,CAAC,wBAAwB,CAAE;sBACjDmO,OAAO,EAAC,UAAU;sBAClBH,IAAI,EAAC,OAAO;sBACZjL,KAAK,EAAEhB,UAAW;sBAClB+L,OAAO,EAAGjL,CAAC,IAAK;wBACfA,CAAC,CAACmN,eAAe,CAAC,CAAC,CAAC,CAAC;wBACrB7I,4BAA4B,CAAC,IAAI,CAAC;sBACjC,CAAE;sBACF4I,SAAS,EAAGlN,CAAC,IAAK;wBACnBA,CAAC,CAACmN,eAAe,CAAC,CAAC,CAAC,CAAC;wBACrB,IAAInN,CAAC,CAACqB,GAAG,KAAK,QAAQ,EAAE;0BACtBqD,8BAA8B,CAAC,CAAC,CAAC,CAAC;wBACpC;wBACA,IAAI1E,CAAC,CAACqB,GAAG,KAAK,OAAO,EAAG;0BACvBnC,UAAU,CAAC0C,IAAI,CAAC,CAAC,GAACmB,SAAS,CAAC,CAAC,EAAE7D,UAAU,CAAC,GAAE6D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;wBAC7D;sBACD,CAAE;sBACF8I,QAAQ,EAAG7L,CAAC,IAAKb,aAAa,CAACa,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;sBAAA;sBACjDmN,SAAS;sBACTC,UAAU,EAAE;wBACXC,cAAc,eACZ7Q,OAAA,CAACxB,cAAc;0BAAC0R,QAAQ,EAAC,OAAO;0BAAA7B,QAAA,eACjCrO,OAAA,CAAC1B,UAAU;4BACT,cAAW,QAAQ;4BACnBiQ,OAAO,EAAGjL,CAAC,IAAK;8BACjBA,CAAC,CAACmN,eAAe,CAAC,CAAC;8BACnB,IAAIjO,UAAU,CAAC0C,IAAI,CAAC,CAAC,EAAE;gCAAG;gCACxBmB,SAAS,CAAC,CAAC,EAAE7D,UAAU,CAAC;8BAC1B,CAAC,MACG;gCACH6D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;8BACjB;4BACC,CAAE;4BAAAgI,QAAA,eAEFrO,OAAA,CAACf,UAAU;8BAAA0J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI;sBAEjB,CAAE;sBACJ4F,EAAE,EAAE;wBACH,0BAA0B,EAAE;0BAAEI,YAAY,EAAE;wBAAM,CAAC;wBACpD,2BAA2B,EAAE;0BAC5BC,MAAM,EAAC;wBACP;sBACD;oBAAE;sBAAApG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAGN9I,OAAA,CAAC7B,GAAG;oBACFuQ,EAAE,EAAE;sBACFgB,SAAS,EAAE,OAAO;sBAClBC,SAAS,EAAE;oBACb,CAAE;oBACFmB,QAAQ,EAAEzJ,gBAAiB,CAAC;oBAAA;oBAAAgH,QAAA,GAE3BvK,oBAAoB,IAAIA,oBAAoB,CAACoD,MAAM,KAAK,CAAC,IAAI,CAACtB,OAAO,iBACpE5F,OAAA,CAACrB,QAAQ;sBAACoS,QAAQ;sBAAA1C,QAAA,EAAE5N,SAAS,CAAC,uBAAuB;oBAAC;sBAAAkI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAClE,EACAhF,oBAAoB,IAAIA,oBAAoB,CAACqF,GAAG,CAAEtG,WAAW,iBAC5D7C,OAAA,CAACrB,QAAQ;sBAEP6E,KAAK,EAAEX,WAAW,CAACmB,IAAK;sBACxBuK,OAAO,EAAEA,CAAA,KAAM;wBACzB1G,uBAAuB,CAAChF,WAAW,CAACmB,IAAI,CAAC;wBACxC;sBACS,CAAE;sBAAAqK,QAAA,EAEDxL,WAAW,CAACmB;oBAAI,GAPZnB,WAAW,CAACuB,OAAO;sBAAAuE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAQhB,CACX,CAAC,EACDlD,OAAO,iBACN5F,OAAA,CAACrB,QAAQ;sBAACoS,QAAQ;sBAACrC,EAAE,EAAE;wBAAEhG,OAAO,EAAE,MAAM;wBAAEsI,cAAc,EAAE;sBAAS,CAAE;sBAAA3C,QAAA,eACnErO,OAAA,CAACnB,gBAAgB;wBAAC4P,IAAI,EAAE;sBAAG;wBAAA9F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CACX;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EACTpI,cAAc,iBACdV,OAAA;gBAAKwI,KAAK,EAAE;kBAAEyI,KAAK,EAAE,SAAS;kBAAEtC,OAAO,EAAE,EAAE;kBAAEuC,YAAY,EAAE,CAAC;kBAAEC,QAAQ,EAAE,EAAE;kBAAE9B,SAAS,EAAE;gBAAO,CAAE;gBAAAhB,QAAA,EAC9F3N;cAAc;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACD9I,OAAA,CAAC7B,GAAG;cACFyE,EAAE,EAAC,mBAAmB;cACtB0L,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBAEnDrO,OAAA,CAAC5B,UAAU;gBAACkQ,SAAS,EAAC,qBAAqB;gBAACI,EAAE,EAAE;kBAAEC,OAAO,EAAE,cAAc;kBAAEuC,YAAY,EAAE;gBAAiB,CAAE;gBAAA7C,QAAA,EAAE5N,SAAS,CAAC,OAAO;cAAC;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAE7I9I,OAAA,CAAC3B,SAAS;gBACRuQ,OAAO,EAAC,UAAU;gBACeH,IAAI,EAAC,OAAO;gBAC/CiC,WAAW,EAAEjQ,SAAS,CAAC,YAAY,CAAE;gBACF6N,SAAS,EAAC,qBAAqB;gBAClE9F,KAAK,EAAE;kBAAEkC,KAAK,EAAE;gBAAO,CAAE;gBACzBlH,KAAK,EAAEd,iCAAiC,CAACI,KAAM;gBAC7CqM,QAAQ,EAAG7L,CAAC,IAAKmB,gBAAgB,CAAC,OAAO,EAAEnB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAC3DoN,UAAU,EAAE;kBACXQ,YAAY,EAAE,EAAE;kBAChB1C,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM,CAAC;oBAC5B,SAAS,EAAE;sBAAEF,SAAS,EAAE,iBAAiB;sBAAEgC,WAAW,EAAC;oBAAiB,CAAC;oBACzE,qBAAqB,EAAC;sBAAC1G,MAAM,EAAC;oBAAiB;kBAChD;gBACD;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGN9I,OAAA,CAAC7B,GAAG;cACFyE,EAAE,EAAC,mBAAmB;cACtB0L,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBAEnDrO,OAAA,CAAC5B,UAAU;gBAACkQ,SAAS,EAAC,qBAAqB;gBAACI,EAAE,EAAE;kBAAEC,OAAO,EAAE,cAAc;kBAAEuC,YAAY,EAAE;gBAAiB,CAAE;gBAAA7C,QAAA,EAAG5N,SAAS,CAAC,aAAa;cAAC;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAEpJ9I,OAAA,CAAC3B,SAAS;gBACRuQ,OAAO,EAAC,UAAU;gBACeH,IAAI,EAAC,OAAO;gBAC/CiC,WAAW,EAAEjQ,SAAS,CAAC,WAAW,CAAE;gBACpC6N,SAAS,EAAC,qBAAqB;gBAC/BgD,SAAS;gBACTC,OAAO,EAAE,CAAE;gBACX/N,KAAK,EAAEd,iCAAiC,CAACK,WAAY;gBACrDoM,QAAQ,EAAG7L,CAAC,IAAKmB,gBAAgB,CAAC,aAAa,EAAEnB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAE9BgF,KAAK,EAAE;kBAACkC,KAAK,EAAC;gBAAM,CAAE;gBACvDkG,UAAU,EAAE;kBACXQ,YAAY,EAAE,EAAE;kBAChB1C,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM,CAAC;oBAC5B,SAAS,EAAE;sBAAEF,SAAS,EAAE,iBAAiB;sBAAEgC,WAAW,EAAC;oBAAiB,CAAC;oBACzE,qBAAqB,EAAC;sBAAC1G,MAAM,EAAC;oBAAiB;kBAChD;gBACD;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAIN9I,OAAA,CAAC7B,GAAG;cACFyE,EAAE,EAAC,mBAAmB;cACtB0L,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBAEnDrO,OAAA;gBAAKsO,SAAS,EAAC,qBAAqB;gBAAC9F,KAAK,EAAE;kBAACE,OAAO,EAAC,MAAM;kBAAC8I,aAAa,EAAC,KAAK;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAC,KAAK;kBAAC/C,OAAO,EAAC,GAAG;kBAAEuC,YAAY,EAAC;gBAAM,CAAE;gBAAA7C,QAAA,gBAC/IrO,OAAA,CAAC5B,UAAU;kBAACsQ,EAAE,EAAE;oBAAEuC,KAAK,EAAE,SAAS;oBAAEU,UAAU,EAAE;kBAAM,CAAE;kBAAAtD,QAAA,EAAE5N,SAAS,CAAC,cAAc;gBAAC;kBAAAkI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACjG9I,OAAA;kBAAMsI,uBAAuB,EAAE;oBAAEC,MAAM,EAAE9I;kBAAS,CAAE;kBAAC+I,KAAK,EAAE;oBAAEE,OAAO,EAAE;kBAAO;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3F9I,OAAA,CAAC5B,UAAU;gBAACoK,KAAK,EAAE;kBAAE2I,QAAQ,EAAE,MAAM;kBAAEF,KAAK,EAAE,SAAS;kBAAE5B,SAAS,EAAE,MAAM;kBAAEV,OAAO,EAAE,GAAG;kBAAEuC,YAAY,EAAE;gBAAO,CAAE;gBAAA7C,QAAA,GAAC,WAAS,EAAC5N,SAAS,CAAC,+FAA+F,CAAC;cAAA;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAEnP9I,OAAA,CAAC3B,SAAS;gBACRuQ,OAAO,EAAC,UAAU;gBACeH,IAAI,EAAC,OAAO;gBAC/CiC,WAAW,EAAEjQ,SAAS,CAAC,iBAAiB,CAAE;gBACP6N,SAAS,EAAC,qBAAqB;gBAC/B9F,KAAK,EAAE;kBAACkC,KAAK,EAAE;gBAAM,CAAE;gBAC1DlH,KAAK,EAAEd,iCAAiC,CAACM,WAAY;gBACnDmM,QAAQ,EAAG7L,CAAC,IAAKmB,gBAAgB,CAAC,aAAa,EAAEnB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBACjEoN,UAAU,EAAE;kBACXQ,YAAY,EAAE,EAAE;kBAChB1C,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM,CAAC;oBAC5B,SAAS,EAAE;sBAAEF,SAAS,EAAE,iBAAiB;sBAAEgC,WAAW,EAAC;oBAAiB,CAAC;oBACzE,qBAAqB,EAAC;sBAAC1G,MAAM,EAAC;oBAAiB;kBAChD;gBACD;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAIN9I,OAAA,CAAC7B,GAAG;cAACyE,EAAE,EAAC,mBAAmB;cAAC0L,SAAS,EAAC;cACrC;cAAA;cAAAD,QAAA,gBAEArO,OAAA,CAAC5B,UAAU;gBAACkQ,SAAS,EAAC,qBAAqB;gBAACI,EAAE,EAAE;kBAAEC,OAAO,EAAE;gBAAuB,CAAE;gBAAAN,QAAA,EAClF5N,SAAS,CAAC,MAAM;cAAC;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7B9I,OAAA,CAAC7B,GAAG;gBAACuQ,EAAE,EAAE;kBAAEhG,OAAO,EAAE,MAAM;kBAAEgJ,GAAG,EAAE,CAAC;kBAAED,UAAU,EAAE,QAAQ;kBAAE/G,KAAK,EAAC,wBAAwB;kBAACkH,QAAQ,EAAC;gBAAM,CAAE;gBAAAvD,QAAA,EACrGlG,KAAK,CAACgB,GAAG,CAAClG,IAAI,iBACXjD,OAAA,CAACpB,OAAO;kBAAekE,KAAK,EAAC,aAAa;kBAAAuL,QAAA,eACtCrO,OAAA,CAAC1B,UAAU;oBACPiQ,OAAO,EAAEA,CAAA,KAAMtF,eAAe,CAAChG,IAAI,CAACL,EAAE,CAAE;oBACxC8L,EAAE,EAAE;sBACAa,MAAM,EAAEtM,IAAI,CAAC8F,QAAQ,GAAG,+BAA+B,GAAG,MAAM;sBAChE+F,YAAY,EAAE,KAAK;sBAC3CH,OAAO,EAAE,KAAK;sBACdkD,UAAU,EAAC;oBACS,CAAE;oBAAAxD,QAAA,EAEDpL,IAAI,CAACoF;kBAAS;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP;gBAAC,GAXH7F,IAAI,CAACL,EAAE;kBAAA+F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYZ,CACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGL,CAAC,eAGZ9I,OAAA,CAAC7B,GAAG;cACAyE,EAAE,EAAC,mBAAmB;cACtB0L,SAAS,EAAC;cACZ;cAAA;cAAAD,QAAA,gBAEIrO,OAAA,CAAC5B,UAAU;gBAACkQ,SAAS,EAAC,qBAAqB;gBAACI,EAAE,EAAE;kBAAEC,OAAO,EAAE;gBAAuB,CAAE;gBAAAN,QAAA,EAAE5N,SAAS,CAAC,kBAAkB;cAAC;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAEtI9I,OAAA;gBACDwI,KAAK,EAAE;kBACLkC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,MAAM;kBACdoE,MAAM,EAAE,eAAe;kBACvBrG,OAAO,EAAE,MAAM;kBACf8I,aAAa,EAAE,QAAQ;kBACvBC,UAAU,EAAE,QAAQ;kBACpBT,cAAc,EAAE,QAAQ;kBACxBzB,MAAM,EAAE,gCAAgC;kBACxCT,YAAY,EAAE,MAAM;kBACpBH,OAAO,EAAE,KAAK;kBACdkD,UAAU,EAAE,SAAS;kBACrBxC,SAAS,EAAE;gBACb,CAAE;gBAAAhB,QAAA,gBAEFrO,OAAA,CAACzB,MAAM;kBACL+P,SAAS,EAAC,qBAAqB;kBAC/B9F,KAAK,EAAE;oBACTmC,MAAM,EAAE,MAAM;oBACdgE,OAAO,EAAE,GAAG;oBACRjE,KAAK,EAAE,MAAM;oBACbhC,OAAO,EAAE,MAAM;oBACf8I,aAAa,EAAE,KAAK;oBAAE;oBACtBC,UAAU,EAAE,QAAQ;oBACpBT,cAAc,EAAE,QAAQ;oBACxBU,GAAG,EAAE,KAAK;oBACVT,KAAK,EAAE,MAAM;oBACbX,eAAe,EAAE,SAAS;oBAC1BwB,aAAa,EAAE,YAAY;oBAC3BC,SAAS,EAAE;kBACb,CAAE;kBACF1J,SAAS,EAAC,OAAO;kBAAAgG,QAAA,gBAEjBrO,OAAA,CAACJ,uBAAuB;oBAAC8O,EAAE,EAAE;sBAACjG,IAAI,EAAC;oBAAK;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAE7C,eAAA9I,OAAA;oBACE4C,EAAE,EAAC,YAAY;oBACfiJ,IAAI,EAAC,MAAM;oBACXmG,QAAQ;oBACRC,MAAM,EAAC,+BAA+B;oBACtC9C,QAAQ,EAAE5D,gBAAiB;oBAC3B/C,KAAK,EAAE;sBAAEE,OAAO,EAAE;oBAAO;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAGT9I,OAAA,CAAC5B,UAAU;kBAACoK,KAAK,EAAE;oBAAE2I,QAAQ,EAAE,MAAM;oBAAEF,KAAK,EAAE;kBAAU,CAAE;kBAAA5C,QAAA,EAAC;gBAE3D;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,EACLwC,SAAS,iBACRtL,OAAA;gBAAKwI,KAAK,EAAE;kBAAEE,OAAO,EAAE,MAAM;kBAAE+I,UAAU,EAAE,QAAQ;kBAAGR,KAAK,EAAE,SAAS;kBAACtC,OAAO,EAAC,OAAO;kBAACU,SAAS,EAAC,MAAM;kBAAE3E,KAAK,EAAE;gBAAwB,CAAE;gBAAA2D,QAAA,gBAExIrO,OAAA;kBACEwI,KAAK,EAAE;oBAAE0J,WAAW,EAAE,KAAK;oBAACxJ,OAAO,EAAC;kBAAO,CAAE;kBAC7CJ,uBAAuB,EAAE;oBAAEC,MAAM,EAAE7I;kBAAQ;gBAAE;kBAAAiJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACF9I,OAAA;kBAAKwI,KAAK,EAAE;oBAAC2I,QAAQ,EAAE;kBAAM,CAAE;kBAAA9C,QAAA,EAAE/C;gBAAS;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CACN,eAKK9I,OAAA,CAAC7B,GAAG;gBAACuQ,EAAE,EAAE;kBAAChE,KAAK,EAAC;gBAAwB,CAAE;gBAAA2D,QAAA,GACvCrE,KAAK,CAACb,GAAG,CAAC,CAACY,IAAI,EAAE4D,KAAK,kBACrB3N,OAAA,CAAC7B,GAAG;kBAEFuK,OAAO,EAAC,MAAM;kBACd+I,UAAU,EAAC,QAAQ;kBACnBT,cAAc,EAAC,eAAe;kBACvCtC,EAAE,EAAE;oBACHI,YAAY,EAAE,MAAM;oBACpBH,OAAO,EAAE,KAAK;oBACdI,MAAM,EAAE,KAAK;oBACbuB,eAAe,EAAE;kBAChB,CAAE;kBAAAjC,QAAA,gBAGKrO,OAAA;oBACEsK,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACT,IAAI,CAAE;oBAC/Ba,GAAG,EAAE,YAAY+C,KAAK,EAAG;oBACzBnF,KAAK,EAAE;sBAAEkC,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,MAAM;sBAAEmE,YAAY,EAAE;oBAAM;kBAAE;oBAAAnG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eAEF9I,OAAA,CAAC5B,UAAU;oBACTsQ,EAAE,EAAE;sBAAEyD,IAAI,EAAE,CAAC;sBAAEhB,QAAQ,EAAE,MAAM;sBAAErB,SAAS,EAAE;oBAAa,CAAE;oBAAAzB,QAAA,EAE1DtE,IAAI,CAACG;kBAAI;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACb9I,OAAA,CAAC1B,UAAU;oBAACiQ,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAACC,KAAK,CAAE;oBAACc,IAAI,EAAC,OAAO;oBAAAJ,QAAA,eACzErO,OAAA;sBAAMsI,uBAAuB,EAAE;wBAAEC,MAAM,EAAErJ;sBAAW,CAAE;sBAACsJ,KAAK,EAAE;wBAAEC,IAAI,EAAE,GAAG;wBAAEC,OAAO,EAAC;sBAAO;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA,GAzBR6E,KAAK;kBAAAhF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0BP,CACN,CAAC,EAGDkC,OAAO,iBACNhL,OAAA,CAAC7B,GAAG;kBACFuK,OAAO,EAAC,MAAM;kBACd+I,UAAU,EAAC,QAAQ;kBACnBT,cAAc,EAAC,eAAe;kBAC9BtC,EAAE,EAAE;oBACZI,YAAY,EAAE,MAAM;oBACpBH,OAAO,EAAE,KAAK;oBACdI,MAAM,EAAE,KAAK;oBACbuB,eAAe,EAAE;kBAChB,CAAE;kBAAAjC,QAAA,gBAEKrO,OAAA;oBACEsK,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACQ,OAAO,CAAE;oBAClCJ,GAAG,EAAC,cAAc;oBAClBpC,KAAK,EAAE;sBAAEkC,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE,MAAM;sBAAEmE,YAAY,EAAE;oBAAM;kBAAE;oBAAAnG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACF9I,OAAA,CAAC5B,UAAU;oBACTsQ,EAAE,EAAE;sBAAEyD,IAAI,EAAE,CAAC;sBAAEhB,QAAQ,EAAE,MAAM;sBAAErB,SAAS,EAAE;oBAAa,CAAE;oBAAAzB,QAAA,EAE1DrD,OAAO,CAACd;kBAAI;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACb9I,OAAA,CAAC1B,UAAU;oBAACiQ,OAAO,EAAEP,eAAgB;oBAACS,IAAI,EAAC,OAAO;oBAAAJ,QAAA,eAC3DrO,OAAA;sBAAMsI,uBAAuB,EAAE;wBAAEC,MAAM,EAAErJ;sBAAW,CAAE;sBAACsJ,KAAK,EAAE;wBAAEC,IAAI,EAAE,GAAG;wBAAEC,OAAO,EAAC;sBAAO;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CACR,EAEKoC,SAAS,iBACXlL,OAAA,CAAC7B,GAAG;kBAACuK,OAAO,EAAC,MAAM;kBAAC+I,UAAU,EAAC,QAAQ;kBAACT,cAAc,EAAC,eAAe;kBAClEtC,EAAE,EAAE;oBACAa,MAAM,EAAE,gBAAgB;oBACxBT,YAAY,EAAE,KAAK;oBACnBH,OAAO,EAAE,KAAK;oBACduC,YAAY,EAAE,KAAK;oBACnBxG,KAAK,EAAE,OAAO;oBACd4F,eAAe,EAAE;kBACrB,CAAE;kBAAAjC,QAAA,gBACFrO,OAAA;oBAAO0K,KAAK,EAAC,IAAI;oBAACC,MAAM,EAAC,IAAI;oBAACyH,QAAQ;oBAAA/D,QAAA,gBAClCrO,OAAA;sBAAQsK,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACU,SAAS,CAAE;sBAACW,IAAI,EAAC;oBAAW;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gDAEpE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACR9I,OAAA,CAAC5B,UAAU;oBAACsQ,EAAE,EAAE;sBAAEyD,IAAI,EAAE,CAAC;sBAAEE,EAAE,EAAE,CAAC;sBAAElB,QAAQ,EAAE,MAAM;sBAAErB,SAAS,EAAE;oBAAa,CAAE;oBAAAzB,QAAA,EACzEnD,SAAS,CAAChB;kBAAI;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACb9I,OAAA,CAAC1B,UAAU;oBAACiQ,OAAO,EAAEJ,iBAAkB;oBAACM,IAAI,EAAC,OAAO;oBAAAJ,QAAA,eAChDrO,OAAA;sBAAMsI,uBAAuB,EAAE;wBAAEC,MAAM,EAAErJ;sBAAW,CAAE;sBAACsJ,KAAK,EAAE;wBAAEC,IAAI,EAAE;sBAAI;oBAAE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAKH9I,OAAA,CAAC7B,GAAG;cACFyE,EAAE,EAAC,mBAAmB;cACtB0L,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBAEnDrO,OAAA,CAAC5B,UAAU;gBAACkQ,SAAS,EAAC,qBAAqB;gBAACI,EAAE,EAAE;kBAAE4D,aAAa,EAAE;gBAAe,CAAE;gBAAAjE,QAAA,EAAE5N,SAAS,CAAC,aAAa;cAAC;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAEzH9I,OAAA,CAAC3B,SAAS;gBACRuQ,OAAO,EAAC,UAAU;gBACeH,IAAI,EAAC,OAAO;gBAC/CiC,WAAW,EAAEjQ,SAAS,CAAC,aAAa,CAAE;gBACH6N,SAAS,EAAC,qBAAqB;gBAChE9F,KAAK,EAAE;kBAACkC,KAAK,EAAE;gBAAM,CAAE;gBACzBlH,KAAK,EAAEd,iCAAiC,CAACS,UAAW;gBAClDgM,QAAQ,EAAG7L,CAAC,IAAKmB,gBAAgB,CAAC,YAAY,EAAEnB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAChEoN,UAAU,EAAE;kBACXQ,YAAY,EAAE,EAAE;kBAChB1C,EAAE,EAAE;oBAEH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM,CAAC;oBAC5B,SAAS,EAAE;sBAAEF,SAAS,EAAE,iBAAiB;sBAAEgC,WAAW,EAAC;oBAAiB,CAAC;oBACzE,qBAAqB,EAAC;sBAAC1G,MAAM,EAAC;oBAAiB;kBAChD;gBACD;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGN9I,OAAA,CAAC7B,GAAG;cACFyE,EAAE,EAAC,mBAAmB;cACtB0L,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBAEnDrO,OAAA,CAAC5B,UAAU;gBAACkQ,SAAS,EAAC,qBAAqB;gBAACI,EAAE,EAAE;kBAAE4D,aAAa,EAAE;gBAAiB,CAAE;gBAAAjE,QAAA,EAAE5N,SAAS,CAAC,mBAAmB;cAAC;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAElI9I,OAAA,CAAC3B,SAAS;gBACTmF,KAAK,EAAEd,iCAAiC,CAACU,gBAAiB;gBAC1D+L,QAAQ,EAAG7L,CAAC,IAAK;kBAChB,IAAIE,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;kBAC1B,IAAIA,KAAK,CAAC0D,MAAM,GAAG,GAAG,EAAE;oBACvB1D,KAAK,GAAGA,KAAK,CAAC+O,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC;kBAC5B;kBACA9N,gBAAgB,CAAC,kBAAkB,EAAEjB,KAAK,CAAC;gBAC5C,CAAE;gBACAoL,OAAO,EAAC,UAAU;gBACeH,IAAI,EAAC,OAAO;gBAC/CiC,WAAW,EAAEjQ,SAAS,CAAC,YAAY,CAAE;gBACrC6N,SAAS,EAAC,qBAAqB;gBAC/BgD,SAAS;gBACTC,OAAO,EAAE,CAAE;gBACX/I,KAAK,EAAE;kBAACkC,KAAK,EAAE;gBAAM,CAAE;gBACvB8H,UAAU,EAAE,GAAG,EAAAjS,qBAAA,GAAAmC,iCAAiC,CAACU,gBAAgB,cAAA7C,qBAAA,uBAAlDA,qBAAA,CAAoD2G,MAAM,KAAI,CAAC,MAAO;gBACrF0J,UAAU,EAAE;kBACTQ,YAAY,EAAE,EAAE;kBAChB1C,EAAE,EAAE;oBACH,0CAA0C,EAAE;sBAAEa,MAAM,EAAE;oBAAO,CAAC;oBAC9D,gDAAgD,EAAE;sBAAEA,MAAM,EAAE;oBAAO,CAAC;oBACpE,YAAY,EAAC;sBAACA,MAAM,EAAC;oBAAM,CAAC;oBAC5B,SAAS,EAAE;sBAAEF,SAAS,EAAE,iBAAiB;sBAAEgC,WAAW,EAAC;oBAAiB,CAAC;oBACzE,qBAAqB,EAAC;sBAAC1G,MAAM,EAAC;oBAAiB;kBAChD;gBACD;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGN9I,OAAA;UAAKsO,SAAS,EAAC,oBAAoB;UAAAD,QAAA,eAElCrO,OAAA,CAACzB,MAAM;YACNqQ,OAAO,EAAC,WAAW;YACnBL,OAAO,EAAExJ,kBAAmB;YAC5BuJ,SAAS,EAAC,WAAW;YAAAD,QAAA,EACrB;UAED;YAAA1F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGd;EAAC,gBACJ,CAAC;AAEN,CAAC;AAACxI,EAAA,CAxjCIH,kBAAkB;EAAA,QACEpB,cAAc,EA+BhCC,cAAc;AAAA;AAAAyT,EAAA,GAhChBtS,kBAAkB;AA0jCxB,eAAeA,kBAAkB;AAAC,IAAAsS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}