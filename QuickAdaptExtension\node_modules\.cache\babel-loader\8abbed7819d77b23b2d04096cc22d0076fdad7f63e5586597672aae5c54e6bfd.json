{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\login\\\\AccountContext.tsx\",\n  _s = $RefreshSig$();\nimport React, { createContext, useState, useEffect, useRef } from 'react';\n\n// Create the context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AccountContext = /*#__PURE__*/createContext({\n  accountId: '',\n  // Default value\n  setAccountId: () => {},\n  // Empty function as a placeholder\n  roles: [],\n  setRoles: () => []\n});\n\n// Provider component\nexport const AccountProvider = ({\n  children\n}) => {\n  _s();\n  const [roles, setRoles] = useState([]);\n  // Get the default accountId from localStorage\n  const getDefaultAccountId = () => localStorage.getItem(\"accountId\") || \"\";\n  const [accountId, setAccountId] = useState(getDefaultAccountId());\n  const originalAccountIdRef = useRef(null);\n  useEffect(() => {\n    // On mount, check for qa_account_id in the URL\n\n    const params = new URLSearchParams(window.location.search);\n    const overrideAccountId = params.get('qa_account_id');\n    const defaultAccountId = getDefaultAccountId();\n\n    // --- Store intended URL if special param is present and not logged in ---\n    const guideId = params.get('quickadopt_guide_id');\n    const isLoggedIn = !!localStorage.getItem('access_token');\n    if ((overrideAccountId || guideId) && !isLoggedIn) {\n      sessionStorage.setItem('postLoginRedirect', window.location.href);\n    }\n    if (overrideAccountId && overrideAccountId !== defaultAccountId) {\n      // Cache the original accountId (if not already cached)\n      if (originalAccountIdRef.current === null) {\n        originalAccountIdRef.current = defaultAccountId;\n      }\n      setAccountId(overrideAccountId);\n      // Store override in localStorage for later use\n      localStorage.setItem('qa_account_id_override', overrideAccountId);\n      // Remove qa_account_id from the URL after use\n      params.delete('qa_account_id');\n      const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}${window.location.hash}`;\n      window.history.replaceState({}, '', newUrl);\n    } else {\n      setAccountId(defaultAccountId);\n      // Remove any previous override if not using it\n      localStorage.removeItem('qa_account_id_override');\n    }\n    // On unload, restore the original accountId and clean up override\n    const handleUnload = () => {\n      if (originalAccountIdRef.current !== null) {\n        setAccountId(originalAccountIdRef.current);\n        originalAccountIdRef.current = null;\n      }\n      localStorage.removeItem('qa_account_id_override');\n    };\n    window.addEventListener('beforeunload', handleUnload);\n    return () => {\n      window.removeEventListener('beforeunload', handleUnload);\n      // Also restore on unmount\n      handleUnload();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/_jsxDEV(AccountContext.Provider, {\n    value: {\n      accountId,\n      setAccountId,\n      roles,\n      setRoles\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountProvider, \"LbE589WhA1941ikebVgeZBou9L8=\");\n_c = AccountProvider;\nvar _c;\n$RefreshReg$(_c, \"AccountProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "AccountContext", "accountId", "setAccountId", "roles", "setRoles", "Account<PERSON><PERSON><PERSON>", "children", "_s", "getDefaultAccountId", "localStorage", "getItem", "originalAccountIdRef", "params", "URLSearchParams", "window", "location", "search", "overrideAccountId", "get", "defaultAccountId", "guideId", "isLoggedIn", "sessionStorage", "setItem", "href", "current", "delete", "newUrl", "pathname", "toString", "hash", "history", "replaceState", "removeItem", "handleUnload", "addEventListener", "removeEventListener", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/login/AccountContext.tsx"], "sourcesContent": ["import React, { createContext, useState, ReactNode, useEffect, useRef } from 'react';\r\n\r\n// Create the context\r\nexport const AccountContext = createContext<{\r\n  accountId: string;\r\n  setAccountId: (id: string) => void;\r\n  roles: string[];\r\n  setRoles: (roles: [])=>void;\r\n}>({\r\n  accountId: '',      // Default value\r\n  setAccountId: () => { },  // Empty function as a placeholder\r\n  roles: [],\r\n  setRoles: () =>[],\r\n});\r\n\r\n// Provider component\r\nexport const AccountProvider = ({ children }: { children: ReactNode }) => {\r\n  const [roles, setRoles] = useState<string[]>([]); \r\n  // Get the default accountId from localStorage\r\n  const getDefaultAccountId = () => localStorage.getItem(\"accountId\") || \"\";\r\n  const [accountId, setAccountId] = useState<string>(getDefaultAccountId());\r\n  const originalAccountIdRef = useRef<string | null>(null);\r\n\r\n  useEffect(() => {\r\n\r\n    // On mount, check for qa_account_id in the URL\r\n\r\n    const params = new URLSearchParams(window.location.search);\r\n    \r\n    const overrideAccountId = params.get('qa_account_id');\r\n    const defaultAccountId = getDefaultAccountId();\r\n    \r\n    // --- Store intended URL if special param is present and not logged in ---\r\n    const guideId = params.get('quickadopt_guide_id');\r\n    const isLoggedIn = !!localStorage.getItem('access_token');\r\n    if ((overrideAccountId || guideId) && !isLoggedIn) {\r\n      sessionStorage.setItem('postLoginRedirect', window.location.href);\r\n    }\r\n    \r\n    if (overrideAccountId && overrideAccountId !== defaultAccountId) {\r\n      // Cache the original accountId (if not already cached)\r\n      if (originalAccountIdRef.current === null) {\r\n        originalAccountIdRef.current = defaultAccountId;\r\n      }\r\n      setAccountId(overrideAccountId);\r\n      // Store override in localStorage for later use\r\n      localStorage.setItem('qa_account_id_override', overrideAccountId);\r\n      // Remove qa_account_id from the URL after use\r\n      params.delete('qa_account_id');\r\n      const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}${window.location.hash}`;\r\n      window.history.replaceState({}, '', newUrl);\r\n    } else {\r\n      setAccountId(defaultAccountId);\r\n      // Remove any previous override if not using it\r\n      localStorage.removeItem('qa_account_id_override');\r\n    }\r\n    // On unload, restore the original accountId and clean up override\r\n    const handleUnload = () => {\r\n      if (originalAccountIdRef.current !== null) {\r\n        setAccountId(originalAccountIdRef.current);\r\n        originalAccountIdRef.current = null;\r\n      }\r\n      localStorage.removeItem('qa_account_id_override');\r\n    };\r\n    window.addEventListener('beforeunload', handleUnload);\r\n    return () => {\r\n      window.removeEventListener('beforeunload', handleUnload);\r\n      // Also restore on unmount\r\n      handleUnload();\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n\r\n  return (\r\n    <AccountContext.Provider value={{ accountId, setAccountId,roles,setRoles }}>\r\n      {children}\r\n    </AccountContext.Provider>\r\n  );\r\n};\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAaC,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAEpF;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,cAAc,gBAAGN,aAAa,CAKxC;EACDO,SAAS,EAAE,EAAE;EAAO;EACpBC,YAAY,EAAEA,CAAA,KAAM,CAAE,CAAC;EAAG;EAC1BC,KAAK,EAAE,EAAE;EACTC,QAAQ,EAAEA,CAAA,KAAK;AACjB,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAkC,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAM,CAACJ,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAW,EAAE,CAAC;EAChD;EACA,MAAMa,mBAAmB,GAAGA,CAAA,KAAMC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE;EACzE,MAAM,CAACT,SAAS,EAAEC,YAAY,CAAC,GAAGP,QAAQ,CAASa,mBAAmB,CAAC,CAAC,CAAC;EACzE,MAAMG,oBAAoB,GAAGd,MAAM,CAAgB,IAAI,CAAC;EAExDD,SAAS,CAAC,MAAM;IAEd;;IAEA,MAAMgB,MAAM,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAE1D,MAAMC,iBAAiB,GAAGL,MAAM,CAACM,GAAG,CAAC,eAAe,CAAC;IACrD,MAAMC,gBAAgB,GAAGX,mBAAmB,CAAC,CAAC;;IAE9C;IACA,MAAMY,OAAO,GAAGR,MAAM,CAACM,GAAG,CAAC,qBAAqB,CAAC;IACjD,MAAMG,UAAU,GAAG,CAAC,CAACZ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IACzD,IAAI,CAACO,iBAAiB,IAAIG,OAAO,KAAK,CAACC,UAAU,EAAE;MACjDC,cAAc,CAACC,OAAO,CAAC,mBAAmB,EAAET,MAAM,CAACC,QAAQ,CAACS,IAAI,CAAC;IACnE;IAEA,IAAIP,iBAAiB,IAAIA,iBAAiB,KAAKE,gBAAgB,EAAE;MAC/D;MACA,IAAIR,oBAAoB,CAACc,OAAO,KAAK,IAAI,EAAE;QACzCd,oBAAoB,CAACc,OAAO,GAAGN,gBAAgB;MACjD;MACAjB,YAAY,CAACe,iBAAiB,CAAC;MAC/B;MACAR,YAAY,CAACc,OAAO,CAAC,wBAAwB,EAAEN,iBAAiB,CAAC;MACjE;MACAL,MAAM,CAACc,MAAM,CAAC,eAAe,CAAC;MAC9B,MAAMC,MAAM,GAAG,GAAGb,MAAM,CAACC,QAAQ,CAACa,QAAQ,GAAGhB,MAAM,CAACiB,QAAQ,CAAC,CAAC,GAAG,IAAIjB,MAAM,CAACiB,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAE,GAAGf,MAAM,CAACC,QAAQ,CAACe,IAAI,EAAE;MACtHhB,MAAM,CAACiB,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,EAAEL,MAAM,CAAC;IAC7C,CAAC,MAAM;MACLzB,YAAY,CAACiB,gBAAgB,CAAC;MAC9B;MACAV,YAAY,CAACwB,UAAU,CAAC,wBAAwB,CAAC;IACnD;IACA;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIvB,oBAAoB,CAACc,OAAO,KAAK,IAAI,EAAE;QACzCvB,YAAY,CAACS,oBAAoB,CAACc,OAAO,CAAC;QAC1Cd,oBAAoB,CAACc,OAAO,GAAG,IAAI;MACrC;MACAhB,YAAY,CAACwB,UAAU,CAAC,wBAAwB,CAAC;IACnD,CAAC;IACDnB,MAAM,CAACqB,gBAAgB,CAAC,cAAc,EAAED,YAAY,CAAC;IACrD,OAAO,MAAM;MACXpB,MAAM,CAACsB,mBAAmB,CAAC,cAAc,EAAEF,YAAY,CAAC;MACxD;MACAA,YAAY,CAAC,CAAC;IAChB,CAAC;IACD;EACF,CAAC,EAAE,EAAE,CAAC;EAGN,oBACEnC,OAAA,CAACC,cAAc,CAACqC,QAAQ;IAACC,KAAK,EAAE;MAAErC,SAAS;MAAEC,YAAY;MAACC,KAAK;MAACC;IAAS,CAAE;IAAAE,QAAA,EACxEA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACc,CAAC;AAE9B,CAAC;AAACnC,EAAA,CA/DWF,eAAe;AAAAsC,EAAA,GAAftC,eAAe;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}