import React, { useEffect, useRef, useState } from "react";
import useDrawerStore from "../../store/drawerStore";
import "./Tooltip.css";
import {
	Box,
	ClickAwayListener,
	Tooltip,
	TooltipProps,
	tooltipClasses,
	LinearProgress,
	MobileStepper,
	Breadcrumbs,
	Typography,
} from "@mui/material";
import { styled } from "@mui/material/styles";

import { updateCacheWithNewRows } from "@mui/x-data-grid/hooks/features/rows/gridRowsUtils";
import TooltipBody from "./TooltipBody";
import { GetGudeDetailsByGuideId } from "../../services/GuideListServices";
import { CustomCursor } from "../../assets/icons/icons";

export const TOOLTIP_MX_WIDTH = 500;
export const TOOLTIP_MN_WIDTH = 250;
export const TOOLTIP_HEIGHT = 500;
export const EXTENSION_PART = "extension-part";

export const CustomWidthTooltip = styled(
	({
		className,
		selectedTemplate,
		selectedTemplateTour,
		...props
	}: TooltipProps & { selectedTemplate: string; selectedTemplateTour: string }) => {
		const { toolTipGuideMetaData, currentStep ,elementSelected} = useDrawerStore();
		useEffect(() => {
			if (
				(
					selectedTemplate === "Tooltip" || 
					selectedTemplate === "Hotspot" || 
					selectedTemplateTour === "Tooltip" || 
					selectedTemplateTour === "Hotspot"
				  ) && elementSelected
				  
			) {
				document.body.style.overflow = "hidden";
			} else {
				document.body.style.overflow = "";
			}
		
			// Cleanup
			return () => {
				document.body.style.overflow = "";
			};
		}, [selectedTemplate, selectedTemplateTour, elementSelected]);
		return (
			<Tooltip
				{...props}
				classes={{ popper: className }}
				id="Tooltip-unique"
				disableHoverListener
				disableTouchListener
				PopperProps={{
					className: "qadpt-tlprte",
					sx: {
						top:
							selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip"
								? "-110px !important"
								: "-160px !important",
								zIndex:'99999 !important'
					},
				}}
			/>
		);
	}
)(({ selectedTemplate }: { selectedTemplate: string }) => {
	const { toolTipGuideMetaData, currentStep } = useDrawerStore();

	return {
		[`& .${tooltipClasses.tooltip}`]: {
			// maxWidth: TOOLTIP_MX_WIDTH,
			minWidth: TOOLTIP_MN_WIDTH,
			width: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.width} !important` || "250px !important",
			maxWidth: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.width} !important` || "800px !important",
			backgroundColor: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.backgroundColor}` || "#fff",
			color: "transparent",
			fontSize: "14px",
			borderRadius: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.borderRadius} !important`,
			position: "relative !important",
			padding: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.padding}` || "2px",
			boxShadow: "9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f", // Adds a smooth shadow
		},
		[`& .${tooltipClasses.arrow}`]: {
			color: "white", // Match arrow color with tooltip background
		},
	};
	return {
		[`& .${tooltipClasses.tooltip}`]: {
			// maxWidth: TOOLTIP_MX_WIDTH,
			minWidth: TOOLTIP_MN_WIDTH,
			maxWidth: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.width} !important` || "500px !important",
			backgroundColor: "white",
			color: "transparent",
			fontSize: "14px",
			borderRadius: `${toolTipGuideMetaData[currentStep - 1]?.canvas?.borderRadius || "8px"} !important`,
			position: "relative !important",
			padding: "0px !important",
			boxShadow: "9px 0px 10px 1px #00000033, 0px 0px 0px 0px #00000024, -4px 2px 10px 1px #0000001f", // Adds a smooth shadow
		},
		[`& .${tooltipClasses.arrow}`]: {
			color: "white", // Match arrow color with tooltip background
		},
	};
});

const CreateTooltip = ({
	isTooltipNameScreenOpen,
	isUnSavedChanges,
	openWarning,
	setopenWarning,
	handleLeave,
	updatedGuideData,
}: {
	isTooltipNameScreenOpen: boolean;
	isUnSavedChanges: boolean;
	openWarning: boolean;
	setopenWarning: (params: boolean) => void;
	handleLeave: () => void;
		updatedGuideData: any;
}) => {
	const {
		selectedTemplate,
		setTooltip,
		tooltip,
		elementSelected,
		setElementSelected,
		guideName,
		borderColor,
		backgroundColor,
		borderSize,
		borderRadius,
		padding,
		width,
		setXpathToTooltipMetaData,
		// updatedCanvasSettings,
		tooltipXaxis,
		tooltipYaxis,
		tooltipWidth,
		setTooltipWidth,
		setTooltipPadding,
		setTooltipBorderradius,
		setTooltipBordersize,
		setTooltipBordercolor,
		setTooltipBackgroundcolor,
		tooltippadding,
		tooltipborderradius,
		tooltipbordersize,
		tooltipBordercolor,
		tooltipBackgroundcolor,
		tooltipPosition,
		setTooltipPosition,
		selectedOption,
		steps,
		currentStepIndex,
		openTooltip,
		setOpenTooltip,
		setCurrentStepIndex,
		HotspotSettings,
		toolTipGuideMetaData,
		hotspotGuideMetaData,
		currentStep,
		isPopoverOpen,
		setIsPopoverOpen,
		setAxisData,
		currentGuideId,
		axisData,
		isALTKeywordEnabled,
		setIsALTKeywordEnabled,
		currentHoveredElement,
		setCurrentHoveredElement,
		selectedTemplateTour,
		isGuideInfoScreen,
		isCollapsed,
		createWithAI,
		syncAITooltipContainerData
	} = useDrawerStore((state: any) => state);
	const [hoveredElement, setHoveredElement] = useState<HTMLElement | null>(null);
	const [rectData, setRectData] = useState<DOMRect | null>(null); // State to store rect object
	const [overlayPosition, setOverlayPosition] = useState<{
		top: number;
		left: number;
		width: number;
		height: number;
	} | null>(null);
	const [popupPosition, setPopupPosition] = useState<any>(null);

	const getXPath = (element: HTMLElement | null) => {
		const isUnique = (attr: string, value: string, tagName: string) => {
			const parent = element?.parentNode;
			if (!parent) return false;
			const matchingElements = [...Array.from(parent.querySelectorAll(`${tagName}[${attr}="${value}"]`))];
			return matchingElements.length === 1;
		};

		const getUniqueSelector = (el: HTMLElement | null) => {
			if (!el) return null;
			const tagName = el.tagName.toLowerCase();

			// find with event attributes
			const eventAttributes = [...Array.from(el.attributes)].filter((attr) => attr.name.startsWith("on"));
			for (const attr of eventAttributes) {
				if (isUnique(attr.name, attr.value, tagName)) {
					return `//${tagName}[@${attr.name}="${attr.value}"]`;
				}
			}
			return null;
		};

		const getSegment = (el: HTMLElement | null) => {
			if (!el) return null;
			const tagName = el.tagName.toLowerCase();
			const uniqueSelector = getUniqueSelector(el);
			const bannercontainer = document.querySelector(".quickAdopt_banner");
			if (bannercontainer) {
				if (el.closest(".quickAdopt_banner")) {
					return null;
				}
			}
			if (uniqueSelector) return uniqueSelector;

			let siblingIndex = 1;
			let sibling = el.previousElementSibling;

			while (sibling) {
				if (sibling.tagName === el.tagName) siblingIndex++;
				sibling = sibling.previousElementSibling;
			}

			return `${tagName}[${siblingIndex}]`;
		};

		const segments: string[] = [];

		let currentElement = element;

		while (currentElement && currentElement !== document.body && currentElement !== document.documentElement) {
			const segment = getSegment(currentElement);
			if (!segment) break;
			segments.unshift(segment);
			if (segment.startsWith("//*[@")) break;
			currentElement = currentElement.parentElement as HTMLElement;
		}

		if (!segments[0].startsWith("//")) {
			if (segments[0] !== "html") {
				segments.unshift("html");
			}
			if (segments[1] !== "body") {
				segments.splice(1, 0, "body");
			}
		}
		return `${segments.join("/")}`;
	};
	const getRelativeXPath = (element: HTMLElement | null) => {
		// Check if element is valid
		if (!element || !element.tagName) return null;

		const isUnique = (xpath: string) => {
			try {
				const matchingElements = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
				return matchingElements.snapshotLength === 1;
			} catch (e) {
				return false;
			}
		};

		const isHighlightStyle = (styleValue: string) => {
			// Match your specific highlighting styles
			return (
				styleValue.includes("outline: 2px solid #6c97a6") ||
				styleValue.includes("pointer-events: none") ||
				styleValue.includes("pointer-events:")
			);
		};

		const isHighlightClass = (className: string) => {
			// Check for your highlighting class
			return className === "quickAdopt-selection";
		};

		const getUniqueSelector = (el: HTMLElement) => {
			const tagName = el.tagName.toLowerCase();

			// Priority 1: ID
			if (el.id) {
				const xpath = `//${tagName}[@id="${el.id}"]`;
				if (isUnique(xpath)) return xpath;
			}

			// Priority 2: Class names (excluding highlight class)
			if (el.className) {
				const classes = el.className
					.trim()
					.split(/\s+/)
					.filter((cls) => !isHighlightClass(cls));
				for (const className of classes) {
					if (className) {
						const xpath = `//${tagName}[contains(@class, "${className}")]`;
						if (isUnique(xpath)) return xpath;
					}
				}
			}

			// Priority 3: Event attributes
			const eventAttributes = [...Array.from(el.attributes)].filter((attr) => attr.name.startsWith("on"));
			for (const attr of eventAttributes) {
				const xpath = `//${tagName}[@${attr.name}="${attr.value}"]`;
				if (isUnique(xpath)) return xpath;
			}

			// Priority 4: Other attributes (excluding highlight style and disabled)
			const attributes = [...Array.from(el.attributes)].filter(
				(attr) =>
					attr.name !== "id" &&
					attr.name !== "class" &&
					!attr.name.startsWith("on") &&
					attr.name !== "disabled" &&
					!(attr.name === "style" && isHighlightStyle(attr.value))
			);
			for (const attr of attributes) {
				const xpath = `//${tagName}[@${attr.name}="${attr.value}"]`;
				if (isUnique(xpath)) return xpath;
			}

			return null;
		};

		let currentElement = element;
		let pathSegments: string[] = [];

		while (currentElement && currentElement !== document.documentElement) {
			const uniqueSelector = getUniqueSelector(currentElement);

			if (uniqueSelector) {
				return uniqueSelector;
			}

			// Build positional path if no unique selector is found
			const tagName = currentElement.tagName.toLowerCase();
			const siblings = Array.from(currentElement.parentElement?.children || []).filter(
				(sib) => sib.tagName === currentElement.tagName
			);
			const index = siblings.indexOf(currentElement) + 1;

			pathSegments.unshift(index > 1 ? `${tagName}[${index}]` : tagName);
			currentElement = currentElement.parentElement as HTMLElement;
		}

		// Check for banner container (from your original code)
		const bannerContainer = document.querySelector(".quickAdopt_banner");
		if (bannerContainer && element.closest(".quickAdopt_banner")) {
			return null;
		}

		return `//${pathSegments.join("/")}`;
	};

	// Example usage:
	// const element = document.querySelector('some-element');
	// const relativeXPath = getRelativeXPath(element);
	// console.log(relativeXPath);

	const shouldIgnoreHighlight = (element: HTMLElement) => {
		return (
			element.classList.contains("mdc-tooltip__surface") ||
			element.classList.contains("mdc-tooltip__surface-animation") ||
			element.classList.contains("mdc-tooltip") ||
			element.classList.contains("mdc-tooltip--shown") ||
			element.classList.contains("mdc-tooltip--showing") ||
			element.classList.contains("mdc-tooltip--hiding") ||
			element.getAttribute("role") === "tooltip" ||
			element.closest("#Tooltip-unique") ||
			element.closest("#my-react-drawer") ||
			element.closest("#tooltip-section-popover") ||
			element.closest("#btn-setting-toolbar") ||
			element.closest("#button-toolbar") ||
			element.closest("#color-picker") ||
			element.closest(".qadpt-ext-banner") ||
			element.closest("#leftDrawer") ||
			element.closest("#image-popover") ||
			element.closest("#toggle-fit") ||
			element.closest("#color-popover") ||
			element.closest("#rte-popover") ||
			element.closest("#rte-alignment") ||
			element.closest("#rte-alignment-menu") ||
			element.closest("#rte-font") ||
			element.closest("#rte-bold") ||
			element.closest("#rte-italic") ||
			element.closest("#rte-underline") ||
			element.closest("#rte-strke-through") ||
			element.closest("#rte-alignment-menu-items") ||
			element.closest("#rte-more") ||
			element.closest("#rte-text-color") ||
			element.closest("#rte-text-color-popover") ||
			element.closest("#rte-text-highlight") ||
			element.closest("#rte-text-highlight-pop") ||
			element.closest("#rte-text-heading") ||
			element.closest("#rte-text-heading-menu-items") ||
			element.closest("#rte-text-format") ||
			element.closest("#rte-text-ul") ||
			element.closest("#rte-text-hyperlink") ||
			element.closest("#rte-video") ||
			element.closest("#rte-clear-formatting") ||
			element.closest("#rte-hyperlink-popover") ||
			element.closest("#rte-box") ||
			element.closest(element.id.startsWith("#rt-editor") ? `${element.id}` : "nope") ||
			element.closest("#rte-placeholder") ||
			element.closest("#qadpt-designpopup") ||
			element.closest("#image-properties") ||
			element.closest("#rte-toolbar") ||
			element.closest("#tooltipdialog") ||
			element.closest("#rte-toolbar-paper") ||
			element.closest("#rte-alignment-menu") ||
			element.closest("#rte-alignment-menu-items")
		);
	};
	const shouldIgnoreEvents = (element: HTMLElement) => {
		return (
			element.classList.contains("mdc-tooltip__surface") ||
			element.classList.contains("mdc-tooltip__surface-animation") ||
			element.classList.contains("mdc-tooltip") ||
			element.classList.contains("mdc-tooltip--shown") ||
			element.classList.contains("mdc-tooltip--showing") ||
			element.classList.contains("mdc-tooltip--hiding") ||
			element.getAttribute("role") === "tooltip" ||
			element.closest("#Tooltip-unique") ||
			element.closest("#tooltip-section-popover") ||
			element.closest("#btn-setting-toolbar") ||
			element.closest("#button-toolbar") ||
			element.closest("#color-picker") ||
			element.closest(".qadpt-ext-banner") ||
			element.closest("#leftDrawer") ||
			element.closest("#rte-popover") ||
			element.closest(element.id.startsWith("#rt-editor") ? `${element.id}` : "nope") ||
			element.closest("#rte-box") ||
			element.closest("#rte-placeholder") ||
			element.closest("#rte-alignment-menu-items") ||
			element.closest("#qadpt-designpopup")
		);
	};

	let hotspot: any;
	const altElementBorderRemove = () => {
		const previousSelectedDOM = document.querySelector(".quickAdopt-selection") as HTMLElement;
		if (previousSelectedDOM) {
			setTimeout(() => {
				previousSelectedDOM.style.outline = "none";
				//previousSelectedDOM.style.backgroundColor = "";
				previousSelectedDOM.style.boxShadow = "";
				previousSelectedDOM.style.pointerEvents = "";
				previousSelectedDOM.removeAttribute("disabled");
				previousSelectedDOM.classList.remove("quickAdopt-selection");
			}, 10);
		}
	};

	const removeAllElementHighlights = () => {
		// Remove all elements with quickAdopt-selection class
		const highlightedElements = document.querySelectorAll('.quickAdopt-selection');
		highlightedElements.forEach((element: Element) => {
			const htmlElement = element as HTMLElement;
			htmlElement.style.outline = '';
			//htmlElement.style.backgroundColor = '';
			htmlElement.style.boxShadow = '';
			htmlElement.style.pointerEvents = '';
			htmlElement.removeAttribute('disabled');
			htmlElement.classList.remove('quickAdopt-selection');
		});

		// Remove any elements with data-target-element attribute
		const targetElements = document.querySelectorAll('[data-target-element="true"]');
		targetElements.forEach((element: Element) => {
			const htmlElement = element as HTMLElement;
			htmlElement.style.outline = '';
			//htmlElement.style.backgroundColor = '';
			htmlElement.style.boxShadow = '';
			htmlElement.style.pointerEvents = '';
			htmlElement.removeAttribute('disabled');
			htmlElement.removeAttribute('data-target-element');
		});

		// Remove any elements with quickadapt highlighting attributes
		const quickAdaptElements = document.querySelectorAll('[data-quickadapt-highlighted="true"]');
		quickAdaptElements.forEach((element: Element) => {
			const htmlElement = element as HTMLElement;
			htmlElement.style.outline = '';
			htmlElement.style.outlineOffset = '';
			//htmlElement.style.backgroundColor = '';
			htmlElement.style.boxShadow = '';
			htmlElement.style.pointerEvents = '';
			htmlElement.removeAttribute('disabled');
			htmlElement.removeAttribute('data-quickadapt-highlighted');
		});
	};

	const removeAppliedStyleOfEle = (element: HTMLElement) => {
		element.removeAttribute("disabled");
		element.style.outline = "";
		element.style.pointerEvents = "unset";
	};

	// Custom cursor utility functions
	const createCustomCursorDataURL = () => {
		// Create a data URL from the SVG
		const svgString = CustomCursor;
		const encodedSvg = encodeURIComponent(svgString);
		return `data:image/svg+xml,${encodedSvg}`;
	};

	const applyCustomCursor = () => {
		const cursorDataURL = createCustomCursorDataURL();
		// Apply cursor to document body and html to ensure it covers the entire page
		// Using 16 16 as hotspot (center of 32x32 cursor)
		document.body.style.cursor = `url("${cursorDataURL}") 16 16, crosshair`;
		document.documentElement.style.cursor = `url("${cursorDataURL}") 16 16, crosshair`;

		// Also apply to all elements that might override the cursor, but exclude extension UI
		const style = document.createElement('style');
		style.id = 'quickadapt-custom-cursor-style';
		style.textContent = `
			* {
				cursor: url("${cursorDataURL}") 16 16, crosshair !important;
			}
			/* Exclude extension UI elements from custom cursor */
			#my-react-drawer,
			#my-react-drawer *,
			.MuiDrawer-root,
			.MuiDrawer-root *,
			.MuiDialog-root,
			.MuiDialog-root *,
			.MuiPopover-root,
			.MuiPopover-root *,
			.MuiTooltip-popper,
			.MuiTooltip-popper *,
			.qadpt-ext-banner,
			.qadpt-ext-banner *,
			#leftDrawer,
			#leftDrawer *,
			#rte-popover,
			#rte-popover *,
			#rte-box,
			#rte-box *,
			#qadpt-designpopup,
			#qadpt-designpopup *,
			.quickadapt-no-custom-cursor,
			.quickadapt-no-custom-cursor * {
				cursor: default !important;
				zIndex: 999999;
			}
		`;
		document.head.appendChild(style);
	};

	const removeCustomCursor = () => {
		// Remove custom cursor from body and html
		document.body.style.cursor = '';
		document.documentElement.style.cursor = '';

		// Remove the custom cursor style
		const existingStyle = document.getElementById('quickadapt-custom-cursor-style');
		if (existingStyle) {
			existingStyle.remove();
		}
	};

	useEffect(() => {
		let isElementHover = false;

		const handleMouseOver = (event: MouseEvent) => {
	if (isALTKeywordEnabled && currentHoveredElement && !isTooltipNameScreenOpen && !isCollapsed) {
		const element = event.target as HTMLElement;
		setCurrentHoveredElement(element);

		if (!shouldIgnoreHighlight(element)) {
			altElementBorderRemove();

			const rect = element.getBoundingClientRect();
			const overlay = document.createElement("div");
			overlay.className = "quickAdopt-overlay";
			Object.assign(overlay.style, {
				position: "fixed",
				top: `${rect.top + window.scrollY}px`,
				left: `${rect.left + window.scrollX}px`,
				width: `${rect.width}px`,
				height: `${rect.height}px`,
				backgroundColor: "rgba(108, 151, 166, 0.2)",
				outline: "2px dashed #6c97a6",
				boxShadow: "0 0 0 3px transparent",
				pointerEvents: "none",
				zIndex: "9999"
			});

			overlay.setAttribute("data-overlay", "true");

			document.body.appendChild(overlay);

			// Optional: Remove overlay on mouseout
			element.addEventListener("mouseout", () => {
				document.querySelectorAll("[data-overlay='true']").forEach(el => el.remove());
			}, { once: true });
		}
	}
};
		const handleMouseOut = (event: MouseEvent) => {
			const element = event.target as HTMLElement;
			setCurrentHoveredElement(element as HTMLElement);
			if (!shouldIgnoreHighlight(element)) {
				if (["input", "select", "textarea", "button", "a", "select"].includes(element.tagName.toLowerCase())) {
					element.style.pointerEvents = "";
				}
				element.removeAttribute("disabled");
				element.style.outline = "";
				//element.style.backgroundColor = "";
				element.style.boxShadow = "";
				isElementHover = false;
			}
		};
		const existingHotspot = document.getElementById("hotspotBlinkCreation");

		const handleClick = (event: MouseEvent) => {
			if (!isGuideInfoScreen && !isTooltipNameScreenOpen && !isCollapsed) {
				const element = event.target as HTMLElement;
				event.stopPropagation();
				event.stopImmediatePropagation();
				event.preventDefault();
				event.stopImmediatePropagation();
				setElementSelected(true);
				if (isALTKeywordEnabled) {
					if (!shouldIgnoreHighlight(element) && !shouldIgnoreEvents(element)) {
						if (!element.hasAttribute("data-target-element")) {
							element.setAttribute("data-target-element", "true");
						}
						const xpath = getXPath(element);
						//element.classList.add("");
						element.style.outline = "";
						const getRelativexpath = getRelativeXPath(element);
						//element.style.position = "relative";
						//element.style.backgroundColor = "rgba(108, 151, 166, 0.2)";
						element.style.outline = "2px dashed #6c97a6";
						element.style.boxShadow = "0 0 0 3px transparent";
						console.log(getRelativexpath);
						const rect = element.getBoundingClientRect();
						setPopupPosition({
							left: `${rect.left}px`,
							top: `${element.offsetHeight + window.scrollY}px`,
						});

						setRectData(rect);
						setAxisData(rect);

						if (!["BODY", "HTML"].includes(element.tagName)) {
							const tooltipPosition = { x: rect.left + 5, y: rect.top };
							setTooltip({
								visible: true,
								text: xpath,
								position: { x: rect.left + 5, y: rect.top },
							});
							if (xpath) {
								setXpathToTooltipMetaData({
									value: xpath,
									PossibleElementPath: getRelativexpath,
									position: tooltipPosition,
								});

								// For AI-created tooltips, sync the data after element selection
								if (createWithAI && selectedTemplate === "Tooltip") {
									// Use setTimeout to ensure the xpath data is set before syncing
									setTimeout(() => {
										syncAITooltipContainerData();
									}, 0);
								}
							}
							setOpenTooltip(true);
							if (!existingHotspot) {
								applyHotspotProperties(rect);
							}
						}
					}
				}
			}
		};

		const handleDocumentClick = (event: MouseEvent) => {
			const target = event.target as HTMLElement;

			if (isALTKeywordEnabled && !isGuideInfoScreen) {
				if (!shouldIgnoreHighlight(target) && !shouldIgnoreEvents(target)) {
					handleClick(event);
				}
			}
		};

		if (rectData && existingHotspot) {
			applyHotspotProperties(rectData);
		}

		if (!isALTKeywordEnabled && currentHoveredElement) {
			// Clean up all highlighted elements when element selection is disabled
			removeAllElementHighlights();
			// Remove custom cursor when switching to page interaction mode
			removeCustomCursor();
			document.removeEventListener("mouseover", handleMouseOver);
			document.removeEventListener("mouseout", handleMouseOut);
			document.removeEventListener("click", handleDocumentClick, true);
			return;
		} else if (elementSelected === false && (selectedTemplate === "Hotspot" || selectedTemplateTour === "Hotspot")) {
			// Apply custom cursor for hotspot element selection
			if (isALTKeywordEnabled) {
				applyCustomCursor();
			}
			document.addEventListener("mouseover", handleMouseOver);
			document.addEventListener("mouseout", handleMouseOut);
			document.addEventListener("click", handleDocumentClick, true);
		} else if (
			!elementSelected &&
			(selectedTemplate === "Tooltip" || selectedTemplateTour === "Tooltip") &&
			isALTKeywordEnabled
		) {
			// Apply custom cursor for tooltip element selection
			applyCustomCursor();
			document.addEventListener("mouseover", handleMouseOver);
			document.addEventListener("mouseout", handleMouseOut);
			document.addEventListener("click", handleDocumentClick, true);
			setElementSelected(false);
		}

		return () => {
			document.removeEventListener("mouseover", handleMouseOver);
			document.removeEventListener("mouseout", handleMouseOut);
			document.removeEventListener("click", handleDocumentClick, true);
			// Clean up custom cursor on component unmount
			removeCustomCursor();
		};
	}, [toolTipGuideMetaData, elementSelected, borderSize, isALTKeywordEnabled, currentHoveredElement]);

	// Separate useEffect to handle custom cursor based on ALT keyword enabled state
	useEffect(() => {
		if (isALTKeywordEnabled && !elementSelected &&
			(selectedTemplate === "Tooltip" || selectedTemplate === "Hotspot" ||
			 selectedTemplateTour === "Tooltip" || selectedTemplateTour === "Hotspot")) {
			// Apply custom cursor when in element selection mode
			applyCustomCursor();
		} else {
			// Remove custom cursor when not in element selection mode
			removeCustomCursor();
		}

		// Cleanup on unmount
		return () => {
			removeCustomCursor();
		};
	}, [isALTKeywordEnabled, elementSelected, selectedTemplate, selectedTemplateTour]);

	const generateDynamicId = () => {
		return "hotspotBlinkCreation";
	};

	const applyHotspotProperties = (rect: any) => {
		const hotspotPropData = toolTipGuideMetaData && toolTipGuideMetaData[0]?.hotspots;
		//const xOffset = parseFloat(hotspotPropData?.XPosition || "4");
		//const yOffset = parseFloat(hotspotPropData?.YPosition || "4");
		const size = hotspotPropData?.Size;
		// const left = rect.left + window.scrollX + xOffset;
		// const top = rect.top + window.scrollY + yOffset;
		const left = "375";
		const top = "160";
		if (selectedTemplate === "Hotspot" || selectedTemplateTour === "Hotspot") {
			hotspot = document.getElementById("hotspotBlinkCreation");
			if (!hotspot) {
				hotspot = document.createElement("div");
				hotspot.id = generateDynamicId();
				document.body.appendChild(hotspot);
			}

			hotspot.style.position = "absolute";
			hotspot.style.left = `${left}px`;
			hotspot.style.top = `${top}px`;

			hotspot.style.width = `${size}px`;
			hotspot.style.height = `${size}px`;
			hotspot.style.backgroundColor = hotspotPropData?.Color ? hotspotPropData.Color : "yellow";
			hotspot.style.borderRadius = "50%";
			hotspot.style.zIndex = "9999";
			hotspot.style.transition = "none";
			hotspot.innerHTML = "";
			if (hotspotPropData?.Type === "Info" || hotspotPropData?.Type === "Question") {
				const textSpan = document.createElement("span");
				textSpan.innerText = hotspotPropData.Type === "Info" ? "i" : "?";
				textSpan.style.color = "white";
				textSpan.style.fontSize = "14px";
				textSpan.style.fontWeight = "bold";
				textSpan.style.fontStyle = hotspotPropData.Type === "Info" ? "italic" : "normal";
				textSpan.style.display = "flex";
				textSpan.style.alignItems = "center";
				textSpan.style.justifyContent = "center";
				textSpan.style.width = "100%";
				textSpan.style.height = "100%";
				textSpan.style.pointerEvents = "none";
				hotspot.appendChild(textSpan);
			}

			if (hotspotPropData?.PulseAnimation) {
				hotspot.classList.add("pulse-animation");

				if (hotspotPropData?.stopAnimationUponInteraction) {
					const stopAnimation = () => {
						if (hotspot) {
							hotspot.classList.remove("pulse-animation");
							hotspot.classList.add("pulse-animation-removed");
							// Ensure the hotspot remains visible by keeping its styles
							hotspot.style.display = "flex";
							hotspot.style.opacity = "1";
							hotspot.style.transform = "scale(1)";
							// Don't remove event handlers to ensure tooltip still works
						}
					};

					// Clear existing event handlers
					hotspot.onclick = null;
					hotspot.onmouseover = null;

					// Add appropriate event handlers based on ShowUpon property
					if (hotspotPropData?.ShowUpon === "Hovering Hotspot") {
						hotspot.addEventListener("mouseover", stopAnimation);
					} else if (hotspotPropData?.ShowUpon === "Clicking Hotspot") {
						hotspot.addEventListener("click", stopAnimation);
					} else {
						// Default to click if ShowUpon is not specified
						hotspot.addEventListener("click", stopAnimation);
					}
				}
			} else {
				hotspot.classList.remove("pulse-animation");
			}
		}
	};

	// Note: "q" key handling is now done in the Drawer component for proper navigation logic

	const canvasProperties = toolTipGuideMetaData[currentStep - 1]?.canvas;
	return (
		<>
			{(selectedTemplate === "Hotspot" ||
				selectedTemplate === "Tooltip" ||
				selectedTemplateTour === "Hotspot" ||
				selectedTemplateTour === "Tooltip") &&
				elementSelected === true && (
					<div
						className="backdrop"
						style={{
							position: "fixed",
							top: 0,
							left: 0,
							right: 0,
							bottom: 0,
							backgroundColor: "rgba(0, 0, 0, 0.5)",
							zIndex: 99999,
						}}
					/>
				)}

			<CustomWidthTooltip
				selectedTemplate={selectedTemplate}
				selectedTemplateTour={selectedTemplateTour}
				title={
					<>
						<TooltipBody
							isPopoverOpen={isPopoverOpen}
							setIsPopoverOpen={setIsPopoverOpen}
							popupPosition={popupPosition}
							isUnSavedChanges={isUnSavedChanges}
							openWarning={openWarning}
							setopenWarning={setopenWarning}
							handleLeave={handleLeave}
							updatedGuideData={updatedGuideData}
							// isRtl={isRtl}	
						/>
						{/* {selectedOption === 1 ? (
							<DotsStepper
								activeStep={currentStep}
								steps={steps.length}
							/>
						) : selectedOption === 2 ? (
							<LinearProgress
								variant="determinate"
								value={(currentStep / steps.length) * 100}
							/>
						) : selectedOption === 3 ? (
							<Breadcrumbs
								aria-label="breadcrumb"
								sx={{ marginTop: "10px" }}
							>
								{steps.map((_: any, index: any) => (
									<Typography
										key={index}
										color={index === currentStepIndex ? "primary" : "text.secondary"}
									>
										Step {index + 1} of {steps.length}
									</Typography>
								))}
							</Breadcrumbs>
						) : null} */}
					</>
				}
				open={
					(selectedTemplate === "Hotspot" ||
						selectedTemplate === "Tooltip" ||
						selectedTemplateTour === "Tooltip" ||
						selectedTemplateTour === "Hotspot") &&
					elementSelected === true &&
					openTooltip
				}
				placement="bottom"
				slotProps={{
					tooltip: {
						sx: {
							borderRadius: canvasProperties?.borderRadius || tooltipborderradius,
							border: `${canvasProperties?.borderSize || "0px"} solid ${canvasProperties?.borderColor || tooltipBordercolor}`,
							backgroundColor: canvasProperties?.backgroundColor || tooltipBackgroundcolor,
							padding: canvasProperties?.padding || tooltippadding,
							Width: `${canvasProperties?.width || "300px"} !important`,
							zIndex: 1000, // Ensure tooltip is above the backdrop
						},
					},
				}}
			>
	<div></div>
			</CustomWidthTooltip>
		</>
	);
};
const normalizePx = (value:any, fallback = "8px") => {
	if (!value) return fallback;
	const val = typeof value === "string" ? value.trim() : `${value}`;
	return val.endsWith("px") ? val : `${val}px`;
};
export default CreateTooltip;

const DotsStepper = ({ steps, activeStep }: { steps: number; activeStep: number }) => {
	return (
		<MobileStepper
			variant="dots"
			steps={steps}
			position="static"
			activeStep={activeStep - 1}
			sx={{ maxWidth: 400, flexGrow: 1, display: "flex", justifyContent: "center" }}
			nextButton={<></>}
			backButton={<></>}
		/>
	);
};
