{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\login\\\\ExtensionLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { TextField, Button, IconButton, InputAdornment, FormHelperText } from '@mui/material';\nimport { GetUserDetails } from '../../services/UserService';\nimport JSEncrypt from 'jsencrypt';\nimport { LoginService } from '../../services/LoginService';\nimport { getOrganizationById } from '../../services/OrganizationService';\nimport useDrawerStore from \"../../store/drawerStore\";\nimport useInfoStore from \"../../store/UserInfoStore\";\nimport userSession from \"../../store/userSession\";\nimport { pwdeye, eyeclose } from '../../assets/icons/icons';\nimport '../login/ExtensionLogin.css';\nimport { getRolesByUser } from '../../services/UserRoleService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  setAccessToken,\n  setOidcInfo,\n  setUser,\n  setOrgDetails,\n  setUserType,\n  setUserRoles\n} = useInfoStore.getState();\nconst {\n  setHasAnnouncementOpened,\n  hasAnnouncementOpened\n} = userSession.getState();\nconst {\n  clearAll,\n  clearAccessToken\n} = useInfoStore.getState();\nconst {\n  clearGuideDetails,\n  setActiveMenu,\n  setSearchText\n} = useDrawerStore.getState();\nconst {\n  clearUserSession\n} = userSession.getState();\nlet userLocalData = {};\nlet SAinitialsData;\nlet userDetails;\nconst ExtensionLogin = ({\n  setIsLoggedIn\n}) => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState(null);\n  const [loginUserDetails, setUserDetails] = useState(null);\n  //const { setUserRoles } = useContext(AuthProvider);\n\n  // const loginStyles = {\n  //   qadptDrawerContent: {\n  //     width: '100%',\n  //     backgroundColor: 'var(--ext - background)',\n  //     marginTop: '20px',\n  //   },\n  //   qadptWelcomeMessage: {\n  //     fontWeight: \"600\",\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\n  //     padding: \"14px\",\n  //   },\n  //       // container: {\n  //   //   padding: '10px',\n  //   //   backgroundColor: '#F6EEEE',\n  //   //   borderRadius: '8px',\n  //   // },\n  //   // welcomeText: {\n  //   //   fontFamily: 'Poppins, sans-serif',\n  //   //   fontSize: '16px',\n  //   //   fontWeight: 600,\n  //   //   lineHeight: '24px',\n  //   //   textAlign: 'left',\n  //   //   color: 'rgba(34, 34, 34, 1)',\n  //   // },\n  //   // headerText: {\n  //   //   fontFamily: 'Poppins, sans-serif',\n  //   //   fontSize: '14px',\n  //   //   fontWeight: 400,\n  //   //   lineHeight: '24px',\n  //   //   textAlign: 'left',\n  //   //   color: 'rgba(68, 68, 68, 1)',\n  //   //   marginTop: '10px',\n  //   // },\n  //   // textField: {\n  //   //   width: '100%',\n  //   //   backgroundColor: 'rgba(255, 255, 255, 1)',\n  //   //   borderRadius: '6px',\n  //   //   height: '46px',\n  //   // },\n  //   // textFieldInput: {\n  //   //   fontFamily: 'Poppins, sans-serif',\n  //   //   fontSize: '16px',\n  //   //   fontWeight: 400,\n  //   //   color: 'rgba(68, 68, 68, 1)',\n  //   //   padding: '12px',\n  //   //   border: '1px solid rgba(213, 213, 213, 1)',\n  //   //   borderRadius: '6px',\n  //   //   height: '46px',\n  //   // },\n  //   // loginButton: {\n  //   //   backgroundColor: '#5F9EA0',\n  //   //   color: '#fff',\n  //   //   borderRadius: '25px',\n  //   //   width: '100%',\n  //   //   marginTop: '20px',\n  //   //   textTransform: 'none',\n  //   //   fontFamily: 'Poppins, sans-serif',\n  //   //   fontSize: '16px',\n  //   //   fontWeight: 500,\n  //   // },\n  //   // qadptTextdanger: {\n  //   //   color: '#d9534f',\n  //   //   fontSize: '0.9rem',\n  //   // },\n\n  //   qadptLoginForm: {\n  //     marginTop: '20px',\n  //     padding: '0px 10px 20px 10px', \n  //   },\n  //   qadptFormLabel: {\n  //     fontSize: \"14px\",\n  //     marginTop: \"10px\",\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\n  //   },\n  //   qadptInvalidCreds: {\n  //     fontSize: \"14px\",\n  //   },\n  //       // qadpteyeicon: {\n  //   //   \"& .MuiIconButton-root\": {\n  //   //     backgroundColor: \"transparent !important\",\n  //   //     border: \"none !important\",\n  //   //     padding: \"0 !important\", // Note: Correct \"Padding\" to \"padding\"\n  //   //   }\n  //   // },\n\n  //   qadptForgotPwd: {\n  //     color: \"var(--primarycolor)\", \n  //     cursor: \"pointer\",\n  //     fontSize: \"16px\",\n  //     fontWeight: \"400\",\n  //     lineHeight: \"24px\",\n  //     marginTop: \"10px\",\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\n  //   },\n  //   qadptBtn: {\n  //     backgroundColor: \"var(--primarycolor) !important\",  \n  //     color: \"#fff\",\n  //     border: \"none\",\n  //     padding: \"10px 12px\",\n  //     cursor: \"pointer\",\n  //     fontSize: \"16px\",\n  //     borderRadius: \"12px\",\n  //     width: \"100%\",\n  //     marginTop: \"10px\",\n  //     lineHeight: \"20px\",\n  //     textTransform:\"none\",\n\n  //   } as React.CSSProperties,\n  // };\n\n  const GetUserRoles = async () => {\n    try {\n      const rolesData = await getRolesByUser();\n      // console.log(rolesData);\n      const dist = rolesData.reduce((acc, curr) => {\n        if (!acc[curr.AccountId]) {\n          acc[curr.AccountId] = [];\n        }\n        if (!acc[curr.AccountId].includes(curr.RoleName)) {\n          acc[curr.AccountId].push(curr.RoleName);\n        }\n        return acc;\n      }, {});\n      setUserRoles(dist);\n    } catch (e) {}\n  };\n  const validateForm = () => {\n    if (!email.trim() && !password.trim()) {\n      setError(\"Email and Password are required.\");\n      return false;\n    }\n    if (!email.trim()) {\n      setError(\"Email is required.\");\n      return false;\n    }\n    if (!validateEmail(email)) {\n      setError(\"Enter a valid email address.\");\n      return false;\n    }\n    if (!password.trim()) {\n      setError(\"Password is required.\");\n      return false;\n    }\n    return true;\n  };\n  const handleLoginSuccess = async () => {\n    try {\n      if (!validateForm()) return;\n      clearAll();\n      clearGuideDetails();\n      clearUserSession();\n      setActiveMenu(null);\n      setSearchText(\"\");\n      const organizationId = \"1\";\n      const rememberLogin = true;\n      const returnUrl = \"\";\n      const authType = \"admin\";\n      const tenantId = \"web\";\n      const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\n      const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\n      const encryptor = new JSEncrypt();\n      encryptor.setPublicKey(publicKey);\n      const now = new Date().toISOString();\n      const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\n      if (!encryptedPassword) {\n        console.error(\"Encryption failed\");\n        return;\n      }\n      const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);\n      if (response.access_token) {\n        setAccessToken(response.access_token);\n        setOidcInfo(response);\n        const userResponse = await GetUserDetails();\n        if (userResponse) {\n          var _userResponse$FirstNa, _userResponse$LastNam, _userResponse$UserTyp, _userResponse$Organiz;\n          setUser(userResponse);\n          GetUserRoles();\n          const firstNameInitials = (userResponse === null || userResponse === void 0 ? void 0 : (_userResponse$FirstNa = userResponse.FirstName) === null || _userResponse$FirstNa === void 0 ? void 0 : _userResponse$FirstNa.charAt(0).toUpperCase()) || '';\n          const lastNameInitials = (userResponse === null || userResponse === void 0 ? void 0 : (_userResponse$LastNam = userResponse.LastName) === null || _userResponse$LastNam === void 0 ? void 0 : _userResponse$LastNam.charAt(0).toUpperCase()) || '';\n          SAinitialsData = firstNameInitials + lastNameInitials;\n          setUserType((_userResponse$UserTyp = userResponse === null || userResponse === void 0 ? void 0 : userResponse.UserType) !== null && _userResponse$UserTyp !== void 0 ? _userResponse$UserTyp : \"\");\n          const orgDetails = await getOrganizationById((_userResponse$Organiz = userResponse === null || userResponse === void 0 ? void 0 : userResponse.OrganizationId) !== null && _userResponse$Organiz !== void 0 ? _userResponse$Organiz : \"\");\n          setOrgDetails(orgDetails);\n          setIsLoggedIn(true);\n          if (!hasAnnouncementOpened) setHasAnnouncementOpened(true);\n          // --- Post-login redirect logic ---\n        }\n      } else {\n        setIsLoggedIn(false);\n        setError(response.error_description || \"Login failed. Please check your credentials.\");\n      }\n    } catch (err) {\n      console.error(err);\n      setError(\"An unexpected error occurred. Please try again later.\");\n    }\n  };\n  const handleClickShowPassword = () => setShowPassword(!showPassword);\n  const handlePasswordChange = event => {\n    setPassword(event.target.value);\n    setError(null);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadptDrawerContent\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadptWelcomeMessage\",\n      children: \"Welcome back\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 1\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadptLoginForm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadptFormLabel\",\n        children: \"Email\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        autoFocus: true,\n        value: email,\n        onChange: e => {\n          setEmail(e.target.value);\n          setError(null);\n        },\n        className: \"qadpt-txtfld\",\n        placeholder: \"Enter your email\"\n        // sx={{\n        //   \"& .MuiInputBase-root\":{\n        //     fontSize: \"16px !important\",\n        //     fontWeight: \"400 !important\",\n        //     padding: \"12px !important\",\n        //     border: \"1px solid var(--border-color) !important\",\n        //     borderRadius: \"6px !important\",\n        //     boxShadow: \"none !important\",\n        //     height: \"42px !important\",\n        //     backgroundColor: \"var(--white-color) !important\",\n        //     marginTop: \"10px !important\",\n        //   },\n        //   \"& .MuiInputBase-input\": {\n        //     height: \"34px !important\",\n        //     border: \"none !important\",\n        //     padding:\"0 !important\",\n\n        //   }\n        // }}\n        ,\n        InputProps: {\n          // className: \"qadpt-input-field\",\n          disableUnderline: true\n        },\n        variant: \"standard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadptFormLabel\",\n        children: \"Password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        required: true,\n        fullWidth: true,\n        type: showPassword ? \"text\" : \"password\",\n        id: \"password\",\n        name: \"password\",\n        autoComplete: \"password\",\n        value: password,\n        onChange: handlePasswordChange,\n        className: \"qadpt-txtfld\",\n        placeholder: \"Enter your password\"\n        // sx={{\n        //   \"& .MuiInputBase-root\":{\n        //     fontSize: \"16px !important\",\n        //     fontWeight: \"400 !important\",\n        //     padding: \"12px !important\",\n        //     border: \"1px solid var(--border-color) !important\",\n        //     borderRadius: \"6px !important\",\n        //     boxShadow: \"none !important\",\n        //     height: \"46px !important\",\n        //     backgroundColor: \"var(--white-color) !important\",\n        //     marginTop: \"10px !important\",\n        //   },\n        //   \"& .MuiInputBase-input\": {\n        //     height: \"34px !important\",\n        //     border: \"none !important\",\n        //     padding:\"0 !important\",\n        //   }\n        // }}\n        ,\n        InputProps: {\n          endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"end\",\n            className: \"pwdicon-blk\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              \"aria-label\": \"toggle password visibility\",\n              onClick: handleClickShowPassword,\n              edge: \"end\"\n              // sx={{\n              //   backgroundColor: \"transparent !important\",\n              //   border: \"none !important\",\n              //   padding: \"0 !important\",\n              //   margin: \"0 !important\",\n              // }}\n              ,\n              className: \"qadpt-pwdicon\"\n              //style={loginStyles.qadpteyeicon} \n              ,\n              children: showPassword ? /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: pwdeye\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 18\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                dangerouslySetInnerHTML: {\n                  __html: eyeclose\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 72\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 17\n          }, this),\n          disableUnderline: true\n        },\n        variant: \"standard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(FormHelperText, {\n        error: true,\n        className: \"qadptFormLabel\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadptForgotPwd\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: () => window.open(`${process.env.REACT_APP_WEB_API}/forgotpassword`, '_blank'),\n          children: \"Forgot password?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleLoginSuccess\n        // style={loginStyles.qadptBtn}\n        ,\n        className: \"qadptBtn\",\n        children: \"Log in\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n};\n_s(ExtensionLogin, \"p9Gm2HccrX+dVgAQzABdkLh8msM=\");\n_c = ExtensionLogin;\nexport default ExtensionLogin;\nfunction validateEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\nvar _c;\n$RefreshReg$(_c, \"ExtensionLogin\");", "map": {"version": 3, "names": ["React", "useState", "TextField", "<PERSON><PERSON>", "IconButton", "InputAdornment", "FormHelperText", "GetUserDetails", "JSEncrypt", "LoginService", "getOrganizationById", "useDrawerStore", "useInfoStore", "userSession", "pwdeye", "eyeclose", "getRolesByUser", "jsxDEV", "_jsxDEV", "setAccessToken", "setOidcInfo", "setUser", "setOrgDetails", "setUserType", "setUserRoles", "getState", "setHasAnnouncementOpened", "hasAnnouncementOpened", "clearAll", "clearAccessToken", "clearGuideDetails", "setActiveMenu", "setSearchText", "clearUserSession", "userLocalData", "SAinitialsData", "userDetails", "ExtensionLogin", "setIsLoggedIn", "_s", "email", "setEmail", "password", "setPassword", "showPassword", "setShowPassword", "error", "setError", "loginUserDetails", "setUserDetails", "GetUserRoles", "rolesData", "dist", "reduce", "acc", "curr", "AccountId", "includes", "RoleName", "push", "e", "validateForm", "trim", "validateEmail", "handleLoginSuccess", "organizationId", "<PERSON><PERSON><PERSON><PERSON>", "returnUrl", "authType", "tenantId", "isEncryptionEnabled", "process", "env", "REACT_APP_ENABLE_ENCRYPTION", "public<PERSON>ey", "REACT_APP_PUBLIC_ENCRYPT_KEY", "encryptor", "setPublicKey", "now", "Date", "toISOString", "encryptedPassword", "encrypt", "toString", "console", "response", "access_token", "userResponse", "_userResponse$FirstNa", "_userResponse$LastNam", "_userResponse$UserTyp", "_userResponse$Organiz", "firstNameInitials", "FirstName", "char<PERSON>t", "toUpperCase", "lastNameInitials", "LastName", "UserType", "orgDetails", "OrganizationId", "error_description", "err", "handleClickShowPassword", "handlePasswordChange", "event", "target", "value", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "autoFocus", "onChange", "placeholder", "InputProps", "disableUnderline", "variant", "required", "type", "id", "name", "autoComplete", "endAdornment", "position", "onClick", "edge", "dangerouslySetInnerHTML", "__html", "window", "open", "REACT_APP_WEB_API", "_c", "emailRegex", "test", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/login/ExtensionLogin.tsx"], "sourcesContent": ["import React, { useContext, useState } from 'react';\r\nimport { TextField, Button, Typography, IconButton, InputAdornment, FormHelperText } from '@mui/material';\r\nimport VisibilityOff from '@mui/icons-material/VisibilityOff';\r\nimport Visibility from '@mui/icons-material/Visibility';\r\nimport { GetUserDetails, GetUserDetailsById, UserLogin } from '../../services/UserService';\r\nimport { AuthProvider, useAuth } from '../auth/AuthProvider';\r\nimport JSEncrypt from 'jsencrypt';\r\nimport { LoginService } from '../../services/LoginService';\r\nimport { User } from '../../models/User';\r\nimport { getOrganizationById } from '../../services/OrganizationService';\r\nimport { Padding } from '@mui/icons-material';\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport useInfoStore from \"../../store/UserInfoStore\";\r\nimport userSession from \"../../store/userSession\";\r\nimport { pwdeye, eyeclose } from '../../assets/icons/icons';\r\nimport '../login/ExtensionLogin.css';\r\nimport { AccountContext } from './AccountContext';\r\nimport { getRolesByUser } from '../../services/UserRoleService';\r\nconst { setAccessToken, setOidcInfo, setUser, setOrgDetails, setUserType,setUserRoles } = useInfoStore.getState();\r\nconst { setHasAnnouncementOpened, hasAnnouncementOpened } = userSession.getState();\r\nconst { clearAll, clearAccessToken } = useInfoStore.getState();\r\nconst { clearGuideDetails, setActiveMenu, setSearchText } = useDrawerStore.getState();\r\nconst {\tclearUserSession} = userSession.getState();\r\nlet userLocalData: { [key: string]: any } = {}\r\nlet SAinitialsData: string;\r\nlet userDetails: User;\r\ninterface ExtensionLoginProps {\r\n    setIsLoggedIn: (value: boolean) => void;\r\n}\r\nconst ExtensionLogin: React.FC<ExtensionLoginProps> = ({ setIsLoggedIn }) => {\r\n  const [email, setEmail] = useState<string>('');\r\n  const [password, setPassword] = useState<string>('');\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [loginUserDetails, setUserDetails] = useState<User | null>(null);\r\n  //const { setUserRoles } = useContext(AuthProvider);\r\n\r\n  // const loginStyles = {\r\n  //   qadptDrawerContent: {\r\n  //     width: '100%',\r\n  //     backgroundColor: 'var(--ext - background)',\r\n  //     marginTop: '20px',\r\n  //   },\r\n  //   qadptWelcomeMessage: {\r\n  //     fontWeight: \"600\",\r\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\r\n  //     padding: \"14px\",\r\n  //   },\r\n  //       // container: {\r\n  //   //   padding: '10px',\r\n  //   //   backgroundColor: '#F6EEEE',\r\n  //   //   borderRadius: '8px',\r\n  //   // },\r\n  //   // welcomeText: {\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '16px',\r\n  //   //   fontWeight: 600,\r\n  //   //   lineHeight: '24px',\r\n  //   //   textAlign: 'left',\r\n  //   //   color: 'rgba(34, 34, 34, 1)',\r\n  //   // },\r\n  //   // headerText: {\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '14px',\r\n  //   //   fontWeight: 400,\r\n  //   //   lineHeight: '24px',\r\n  //   //   textAlign: 'left',\r\n  //   //   color: 'rgba(68, 68, 68, 1)',\r\n  //   //   marginTop: '10px',\r\n  //   // },\r\n  //   // textField: {\r\n  //   //   width: '100%',\r\n  //   //   backgroundColor: 'rgba(255, 255, 255, 1)',\r\n  //   //   borderRadius: '6px',\r\n  //   //   height: '46px',\r\n  //   // },\r\n  //   // textFieldInput: {\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '16px',\r\n  //   //   fontWeight: 400,\r\n  //   //   color: 'rgba(68, 68, 68, 1)',\r\n  //   //   padding: '12px',\r\n  //   //   border: '1px solid rgba(213, 213, 213, 1)',\r\n  //   //   borderRadius: '6px',\r\n  //   //   height: '46px',\r\n  //   // },\r\n  //   // loginButton: {\r\n  //   //   backgroundColor: '#5F9EA0',\r\n  //   //   color: '#fff',\r\n  //   //   borderRadius: '25px',\r\n  //   //   width: '100%',\r\n  //   //   marginTop: '20px',\r\n  //   //   textTransform: 'none',\r\n  //   //   fontFamily: 'Poppins, sans-serif',\r\n  //   //   fontSize: '16px',\r\n  //   //   fontWeight: 500,\r\n  //   // },\r\n  //   // qadptTextdanger: {\r\n  //   //   color: '#d9534f',\r\n  //   //   fontSize: '0.9rem',\r\n  //   // },\r\n\r\n  //   qadptLoginForm: {\r\n  //     marginTop: '20px',\r\n  //     padding: '0px 10px 20px 10px', \r\n  //   },\r\n  //   qadptFormLabel: {\r\n  //     fontSize: \"14px\",\r\n  //     marginTop: \"10px\",\r\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\r\n  //   },\r\n  //   qadptInvalidCreds: {\r\n  //     fontSize: \"14px\",\r\n  //   },\r\n  //       // qadpteyeicon: {\r\n  //   //   \"& .MuiIconButton-root\": {\r\n  //   //     backgroundColor: \"transparent !important\",\r\n  //   //     border: \"none !important\",\r\n  //   //     padding: \"0 !important\", // Note: Correct \"Padding\" to \"padding\"\r\n  //   //   }\r\n  //   // },\r\n    \r\n  //   qadptForgotPwd: {\r\n  //     color: \"var(--primarycolor)\", \r\n  //     cursor: \"pointer\",\r\n  //     fontSize: \"16px\",\r\n  //     fontWeight: \"400\",\r\n  //     lineHeight: \"24px\",\r\n  //     marginTop: \"10px\",\r\n  //     textAlign: \"left\" as React.CSSProperties[\"textAlign\"],\r\n  //   },\r\n  //   qadptBtn: {\r\n  //     backgroundColor: \"var(--primarycolor) !important\",  \r\n  //     color: \"#fff\",\r\n  //     border: \"none\",\r\n  //     padding: \"10px 12px\",\r\n  //     cursor: \"pointer\",\r\n  //     fontSize: \"16px\",\r\n  //     borderRadius: \"12px\",\r\n  //     width: \"100%\",\r\n  //     marginTop: \"10px\",\r\n  //     lineHeight: \"20px\",\r\n  //     textTransform:\"none\",\r\n      \r\n  //   } as React.CSSProperties,\r\n  // };\r\n  \r\n  const GetUserRoles = async () => {\r\n    try {\r\n      const rolesData = await getRolesByUser();\r\n      // console.log(rolesData);\r\n      const dist = rolesData.reduce((acc: { [x: string]: any[]; }, curr: { AccountId: string | number; RoleName: any; }) => {\r\n        if (!acc[curr.AccountId]) {\r\n          acc[curr.AccountId] = [];\r\n        }\r\n        if (!acc[curr.AccountId].includes(curr.RoleName)) {\r\n          acc[curr.AccountId].push(curr.RoleName);\r\n        }\r\n        return acc;\r\n      }, {});\r\n\r\n      setUserRoles(dist);\r\n              \r\n      \r\n    } catch (e) {\r\n      \r\n    }\r\n  }\r\n\r\n  const validateForm = (): boolean => {\r\n    if (!email.trim() && !password.trim()) {\r\n      setError(\"Email and Password are required.\");\r\n      return false;\r\n    }\r\n    if (!email.trim()) {\r\n      setError(\"Email is required.\");\r\n      return false;\r\n    }\r\n    if (!validateEmail(email)) {\r\n      setError(\"Enter a valid email address.\");\r\n      return false;\r\n    }\r\n    if (!password.trim()) {\r\n      setError(\"Password is required.\");\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n  \r\n  \r\n\r\n  const handleLoginSuccess = async () => {\r\n    try {\r\n      if (!validateForm()) return;\r\n  \r\n      clearAll();\r\n      clearGuideDetails();\r\n      clearUserSession();\r\n      setActiveMenu(null);\r\n      setSearchText(\"\");\r\n  \r\n      const organizationId = \"1\";\r\n      const rememberLogin = true;\r\n      const returnUrl = \"\";\r\n      const authType = \"admin\";\r\n      const tenantId = \"web\";\r\n\r\n      const isEncryptionEnabled = process.env.REACT_APP_ENABLE_ENCRYPTION === 'true';\r\n\r\n      const publicKey = process.env.REACT_APP_PUBLIC_ENCRYPT_KEY || '';\r\n                const encryptor = new JSEncrypt();\r\n                encryptor.setPublicKey(publicKey);\r\n                const now = new Date().toISOString();\r\n                const encryptedPassword = encryptor.encrypt(password + '|' + now.trim()).toString();\r\n                if (!encryptedPassword) {\r\n                  console.error(\"Encryption failed\");\r\n                  return; \r\n                }\r\n  \r\n      const response = await LoginService(email, isEncryptionEnabled ? encryptedPassword : password, organizationId, rememberLogin, returnUrl, authType, tenantId);\r\n      if (response.access_token) {\r\n        setAccessToken(response.access_token);\r\n        setOidcInfo(response);\r\n        const userResponse = await GetUserDetails();\r\n        if (userResponse) {\r\n          setUser(userResponse);\r\n          GetUserRoles();\r\n          const firstNameInitials = userResponse?.FirstName?.charAt(0).toUpperCase() || '';\r\n          const lastNameInitials = userResponse?.LastName?.charAt(0).toUpperCase() || '';\r\n          SAinitialsData = firstNameInitials + lastNameInitials;\r\n          setUserType(userResponse?.UserType ?? \"\");\r\n          const orgDetails = await getOrganizationById(userResponse?.OrganizationId ?? \"\");\r\n          setOrgDetails(orgDetails);\r\n          setIsLoggedIn(true);\r\n          if (!hasAnnouncementOpened) setHasAnnouncementOpened(true);\r\n                    // --- Post-login redirect logic ---\r\n        }\r\n      } else {\r\n        setIsLoggedIn(false);\r\n        setError(response.error_description || \"Login failed. Please check your credentials.\");\r\n      }\r\n    } catch (err) {\r\n      console.error(err);\r\n      setError(\"An unexpected error occurred. Please try again later.\");\r\n    }\r\n  };\r\n  \r\n\r\n  const handleClickShowPassword = () => setShowPassword(!showPassword);\r\n\r\n  const handlePasswordChange = (event: any) => {\r\n    setPassword(event.target.value);\r\n    setError(null);\r\n  };\r\n\r\n  return (\r\n    <div className='qadptDrawerContent'>\t\r\n<div className='qadptWelcomeMessage'>\r\n  Welcome back\r\n</div>\r\n    <div className='qadptLoginForm'>\r\n\r\n        <div className='qadptFormLabel'>Email</div>\r\n\r\n        <TextField\r\n            fullWidth\r\n            autoFocus            \r\n            value={email}\r\n            onChange={(e) => {\r\n                setEmail(e.target.value); \r\n                setError(null); \r\n          }}\r\n          className='qadpt-txtfld'\r\n          placeholder=\"Enter your email\"\r\n          // sx={{\r\n          //   \"& .MuiInputBase-root\":{\r\n          //     fontSize: \"16px !important\",\r\n          //     fontWeight: \"400 !important\",\r\n          //     padding: \"12px !important\",\r\n          //     border: \"1px solid var(--border-color) !important\",\r\n          //     borderRadius: \"6px !important\",\r\n          //     boxShadow: \"none !important\",\r\n          //     height: \"42px !important\",\r\n          //     backgroundColor: \"var(--white-color) !important\",\r\n          //     marginTop: \"10px !important\",\r\n          //   },\r\n          //   \"& .MuiInputBase-input\": {\r\n          //     height: \"34px !important\",\r\n          //     border: \"none !important\",\r\n          //     padding:\"0 !important\",\r\n              \r\n          //   }\r\n          // }}\r\n            InputProps={{\t\t\t\t\t\t\t\t\t\t\r\n              // className: \"qadpt-input-field\",\r\n              disableUnderline: true,\r\n          }}\r\n          variant=\"standard\"\r\n        />\r\n\r\n        <div className='qadptFormLabel'>Password</div>\r\n        <TextField\r\n            required\r\n            fullWidth\r\n            type={showPassword ? \"text\" : \"password\"}\r\n            id=\"password\"\r\n            name=\"password\"\r\n            autoComplete=\"password\"           \r\n            value={password}\r\n          onChange={handlePasswordChange}\r\n          className='qadpt-txtfld'\r\n          placeholder=\"Enter your password\"\r\n          // sx={{\r\n          //   \"& .MuiInputBase-root\":{\r\n          //     fontSize: \"16px !important\",\r\n          //     fontWeight: \"400 !important\",\r\n          //     padding: \"12px !important\",\r\n          //     border: \"1px solid var(--border-color) !important\",\r\n          //     borderRadius: \"6px !important\",\r\n          //     boxShadow: \"none !important\",\r\n          //     height: \"46px !important\",\r\n          //     backgroundColor: \"var(--white-color) !important\",\r\n          //     marginTop: \"10px !important\",\r\n          //   },\r\n          //   \"& .MuiInputBase-input\": {\r\n          //     height: \"34px !important\",\r\n          //     border: \"none !important\",\r\n          //     padding:\"0 !important\",\r\n          //   }\r\n          // }}\r\n            InputProps={{ \r\n              endAdornment: (\r\n                <InputAdornment \r\n                position=\"end\" \r\n                className='pwdicon-blk'\r\n              >\r\n                <IconButton\r\n                  aria-label=\"toggle password visibility\"\r\n                  onClick={handleClickShowPassword}\r\n                    edge=\"end\"\r\n                    // sx={{\r\n                    //   backgroundColor: \"transparent !important\",\r\n                    //   border: \"none !important\",\r\n                    //   padding: \"0 !important\",\r\n                    //   margin: \"0 !important\",\r\n                    // }}\r\n                    className='qadpt-pwdicon'\r\n                    //style={loginStyles.qadpteyeicon} \r\n                >\r\n{showPassword ?  <span dangerouslySetInnerHTML={{ __html: pwdeye }}/>: <span dangerouslySetInnerHTML={{ __html: eyeclose }}/>}\r\n</IconButton>\r\n              </InputAdornment>\r\n                    \r\n                ),\r\n               \r\n                disableUnderline: true,\r\n            }}\r\n            variant=\"standard\"\r\n           \r\n        />\t\r\n         {error && (\r\n          <FormHelperText error className='qadptFormLabel'>\r\n              {error}\r\n          </FormHelperText>\r\n        )}\r\n\r\n        <div className='qadptForgotPwd'>\r\n        <span \r\n        onClick={() => window.open(`${process.env.REACT_APP_WEB_API}/forgotpassword`, '_blank')}\r\n        >\r\n          Forgot password?\r\n        </span>\r\n        </div>\r\n\r\n        <Button\r\n        variant=\"contained\"\r\n        onClick={handleLoginSuccess}\r\n          // style={loginStyles.qadptBtn}\r\n          className='qadptBtn'\r\n    >\r\n        Log in\r\n    </Button>\r\n    </div>\r\n</div>\r\n  );\r\n};\r\n\r\nexport default ExtensionLogin;\r\nfunction validateEmail(email: string): boolean {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n}\r\n\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAgBC,QAAQ,QAAQ,OAAO;AACnD,SAASC,SAAS,EAAEC,MAAM,EAAcC,UAAU,EAAEC,cAAc,EAAEC,cAAc,QAAQ,eAAe;AAGzG,SAASC,cAAc,QAAuC,4BAA4B;AAE1F,OAAOC,SAAS,MAAM,WAAW;AACjC,SAASC,YAAY,QAAQ,6BAA6B;AAE1D,SAASC,mBAAmB,QAAQ,oCAAoC;AAExE,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,MAAM,EAAEC,QAAQ,QAAQ,0BAA0B;AAC3D,OAAO,6BAA6B;AAEpC,SAASC,cAAc,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAChE,MAAM;EAAEC,cAAc;EAAEC,WAAW;EAAEC,OAAO;EAAEC,aAAa;EAAEC,WAAW;EAACC;AAAa,CAAC,GAAGZ,YAAY,CAACa,QAAQ,CAAC,CAAC;AACjH,MAAM;EAAEC,wBAAwB;EAAEC;AAAsB,CAAC,GAAGd,WAAW,CAACY,QAAQ,CAAC,CAAC;AAClF,MAAM;EAAEG,QAAQ;EAAEC;AAAiB,CAAC,GAAGjB,YAAY,CAACa,QAAQ,CAAC,CAAC;AAC9D,MAAM;EAAEK,iBAAiB;EAAEC,aAAa;EAAEC;AAAc,CAAC,GAAGrB,cAAc,CAACc,QAAQ,CAAC,CAAC;AACrF,MAAM;EAAEQ;AAAgB,CAAC,GAAGpB,WAAW,CAACY,QAAQ,CAAC,CAAC;AAClD,IAAIS,aAAqC,GAAG,CAAC,CAAC;AAC9C,IAAIC,cAAsB;AAC1B,IAAIC,WAAiB;AAIrB,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC+C,gBAAgB,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAc,IAAI,CAAC;EACtE;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA,MAAMiD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,SAAS,GAAG,MAAMnC,cAAc,CAAC,CAAC;MACxC;MACA,MAAMoC,IAAI,GAAGD,SAAS,CAACE,MAAM,CAAC,CAACC,GAA4B,EAAEC,IAAoD,KAAK;QACpH,IAAI,CAACD,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,EAAE;UACxBF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,GAAG,EAAE;QAC1B;QACA,IAAI,CAACF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACC,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,EAAE;UAChDJ,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACG,IAAI,CAACJ,IAAI,CAACG,QAAQ,CAAC;QACzC;QACA,OAAOJ,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN9B,YAAY,CAAC4B,IAAI,CAAC;IAGpB,CAAC,CAAC,OAAOQ,CAAC,EAAE,CAEZ;EACF,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAe;IAClC,IAAI,CAACrB,KAAK,CAACsB,IAAI,CAAC,CAAC,IAAI,CAACpB,QAAQ,CAACoB,IAAI,CAAC,CAAC,EAAE;MACrCf,QAAQ,CAAC,kCAAkC,CAAC;MAC5C,OAAO,KAAK;IACd;IACA,IAAI,CAACP,KAAK,CAACsB,IAAI,CAAC,CAAC,EAAE;MACjBf,QAAQ,CAAC,oBAAoB,CAAC;MAC9B,OAAO,KAAK;IACd;IACA,IAAI,CAACgB,aAAa,CAACvB,KAAK,CAAC,EAAE;MACzBO,QAAQ,CAAC,8BAA8B,CAAC;MACxC,OAAO,KAAK;IACd;IACA,IAAI,CAACL,QAAQ,CAACoB,IAAI,CAAC,CAAC,EAAE;MACpBf,QAAQ,CAAC,uBAAuB,CAAC;MACjC,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAID,MAAMiB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,IAAI,CAACH,YAAY,CAAC,CAAC,EAAE;MAErBjC,QAAQ,CAAC,CAAC;MACVE,iBAAiB,CAAC,CAAC;MACnBG,gBAAgB,CAAC,CAAC;MAClBF,aAAa,CAAC,IAAI,CAAC;MACnBC,aAAa,CAAC,EAAE,CAAC;MAEjB,MAAMiC,cAAc,GAAG,GAAG;MAC1B,MAAMC,aAAa,GAAG,IAAI;MAC1B,MAAMC,SAAS,GAAG,EAAE;MACpB,MAAMC,QAAQ,GAAG,OAAO;MACxB,MAAMC,QAAQ,GAAG,KAAK;MAEtB,MAAMC,mBAAmB,GAAGC,OAAO,CAACC,GAAG,CAACC,2BAA2B,KAAK,MAAM;MAE9E,MAAMC,SAAS,GAAGH,OAAO,CAACC,GAAG,CAACG,4BAA4B,IAAI,EAAE;MACtD,MAAMC,SAAS,GAAG,IAAIpE,SAAS,CAAC,CAAC;MACjCoE,SAAS,CAACC,YAAY,CAACH,SAAS,CAAC;MACjC,MAAMI,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACpC,MAAMC,iBAAiB,GAAGL,SAAS,CAACM,OAAO,CAACxC,QAAQ,GAAG,GAAG,GAAGoC,GAAG,CAAChB,IAAI,CAAC,CAAC,CAAC,CAACqB,QAAQ,CAAC,CAAC;MACnF,IAAI,CAACF,iBAAiB,EAAE;QACtBG,OAAO,CAACtC,KAAK,CAAC,mBAAmB,CAAC;QAClC;MACF;MAEV,MAAMuC,QAAQ,GAAG,MAAM5E,YAAY,CAAC+B,KAAK,EAAE8B,mBAAmB,GAAGW,iBAAiB,GAAGvC,QAAQ,EAAEuB,cAAc,EAAEC,aAAa,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;MAC5J,IAAIgB,QAAQ,CAACC,YAAY,EAAE;QACzBnE,cAAc,CAACkE,QAAQ,CAACC,YAAY,CAAC;QACrClE,WAAW,CAACiE,QAAQ,CAAC;QACrB,MAAME,YAAY,GAAG,MAAMhF,cAAc,CAAC,CAAC;QAC3C,IAAIgF,YAAY,EAAE;UAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;UAChBtE,OAAO,CAACkE,YAAY,CAAC;UACrBrC,YAAY,CAAC,CAAC;UACd,MAAM0C,iBAAiB,GAAG,CAAAL,YAAY,aAAZA,YAAY,wBAAAC,qBAAA,GAAZD,YAAY,CAAEM,SAAS,cAAAL,qBAAA,uBAAvBA,qBAAA,CAAyBM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,EAAE;UAChF,MAAMC,gBAAgB,GAAG,CAAAT,YAAY,aAAZA,YAAY,wBAAAE,qBAAA,GAAZF,YAAY,CAAEU,QAAQ,cAAAR,qBAAA,uBAAtBA,qBAAA,CAAwBK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAI,EAAE;UAC9E5D,cAAc,GAAGyD,iBAAiB,GAAGI,gBAAgB;UACrDzE,WAAW,EAAAmE,qBAAA,GAACH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEW,QAAQ,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UACzC,MAAMS,UAAU,GAAG,MAAMzF,mBAAmB,EAAAiF,qBAAA,GAACJ,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEa,cAAc,cAAAT,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;UAChFrE,aAAa,CAAC6E,UAAU,CAAC;UACzB7D,aAAa,CAAC,IAAI,CAAC;UACnB,IAAI,CAACX,qBAAqB,EAAED,wBAAwB,CAAC,IAAI,CAAC;UAChD;QACZ;MACF,CAAC,MAAM;QACLY,aAAa,CAAC,KAAK,CAAC;QACpBS,QAAQ,CAACsC,QAAQ,CAACgB,iBAAiB,IAAI,8CAA8C,CAAC;MACxF;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZlB,OAAO,CAACtC,KAAK,CAACwD,GAAG,CAAC;MAClBvD,QAAQ,CAAC,uDAAuD,CAAC;IACnE;EACF,CAAC;EAGD,MAAMwD,uBAAuB,GAAGA,CAAA,KAAM1D,eAAe,CAAC,CAACD,YAAY,CAAC;EAEpE,MAAM4D,oBAAoB,GAAIC,KAAU,IAAK;IAC3C9D,WAAW,CAAC8D,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IAC/B5D,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,oBACE7B,OAAA;IAAK0F,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACvC3F,OAAA;MAAK0F,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EAAC;IAErC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACF/F,OAAA;MAAK0F,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAE3B3F,OAAA;QAAK0F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAE3C/F,OAAA,CAAChB,SAAS;QACNgH,SAAS;QACTC,SAAS;QACTR,KAAK,EAAEnE,KAAM;QACb4E,QAAQ,EAAGxD,CAAC,IAAK;UACbnB,QAAQ,CAACmB,CAAC,CAAC8C,MAAM,CAACC,KAAK,CAAC;UACxB5D,QAAQ,CAAC,IAAI,CAAC;QACpB,CAAE;QACF6D,SAAS,EAAC,cAAc;QACxBS,WAAW,EAAC;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QAAA;QACEC,UAAU,EAAE;UACV;UACAC,gBAAgB,EAAE;QACtB,CAAE;QACFC,OAAO,EAAC;MAAU;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eAEF/F,OAAA;QAAK0F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC9C/F,OAAA,CAAChB,SAAS;QACNuH,QAAQ;QACRP,SAAS;QACTQ,IAAI,EAAE9E,YAAY,GAAG,MAAM,GAAG,UAAW;QACzC+E,EAAE,EAAC,UAAU;QACbC,IAAI,EAAC,UAAU;QACfC,YAAY,EAAC,UAAU;QACvBlB,KAAK,EAAEjE,QAAS;QAClB0E,QAAQ,EAAEZ,oBAAqB;QAC/BI,SAAS,EAAC,cAAc;QACxBS,WAAW,EAAC;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAAA;QACEC,UAAU,EAAE;UACVQ,YAAY,eACV5G,OAAA,CAACb,cAAc;YACf0H,QAAQ,EAAC,KAAK;YACdnB,SAAS,EAAC,aAAa;YAAAC,QAAA,eAEvB3F,OAAA,CAACd,UAAU;cACT,cAAW,4BAA4B;cACvC4H,OAAO,EAAEzB,uBAAwB;cAC/B0B,IAAI,EAAC;cACL;cACA;cACA;cACA;cACA;cACA;cAAA;cACArB,SAAS,EAAC;cACV;cAAA;cAAAC,QAAA,EAEnBjE,YAAY,gBAAI1B,OAAA;gBAAMgH,uBAAuB,EAAE;kBAAEC,MAAM,EAAErH;gBAAO;cAAE;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,gBAAE/F,OAAA;gBAAMgH,uBAAuB,EAAE;kBAAEC,MAAM,EAAEpH;gBAAS;cAAE;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACiB,CAEb;UAEDM,gBAAgB,EAAE;QACtB,CAAE;QACFC,OAAO,EAAC;MAAU;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAErB,CAAC,EACAnE,KAAK,iBACL5B,OAAA,CAACZ,cAAc;QAACwC,KAAK;QAAC8D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC3C/D;MAAK;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB,eAED/F,OAAA;QAAK0F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC/B3F,OAAA;UACA8G,OAAO,EAAEA,CAAA,KAAMI,MAAM,CAACC,IAAI,CAAC,GAAG9D,OAAO,CAACC,GAAG,CAAC8D,iBAAiB,iBAAiB,EAAE,QAAQ,CAAE;UAAAzB,QAAA,EACvF;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN/F,OAAA,CAACf,MAAM;QACPqH,OAAO,EAAC,WAAW;QACnBQ,OAAO,EAAEhE;QACP;QAAA;QACA4C,SAAS,EAAC,UAAU;QAAAC,QAAA,EACzB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEN,CAAC;AAAC1E,EAAA,CApWIF,cAA6C;AAAAkG,EAAA,GAA7ClG,cAA6C;AAsWnD,eAAeA,cAAc;AAC7B,SAAS0B,aAAaA,CAACvB,KAAa,EAAW;EAC7C,MAAMgG,UAAU,GAAG,4BAA4B;EAC/C,OAAOA,UAAU,CAACC,IAAI,CAACjG,KAAK,CAAC;AAC/B;AAAC,IAAA+F,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}