{"ast": null, "code": "const matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nexport const unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);", "map": {"version": 3, "names": ["matchHtmlEntity", "htmlEntities", "unescapeHtmlEntity", "m", "unescape", "text", "replace"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/node_modules/react-i18next/dist/es/unescape.js"], "sourcesContent": ["const matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nexport const unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);"], "mappings": "AAAA,MAAMA,eAAe,GAAG,mGAAmG;AAC3H,MAAMC,YAAY,GAAG;EACnB,OAAO,EAAE,GAAG;EACZ,OAAO,EAAE,GAAG;EACZ,MAAM,EAAE,GAAG;EACX,OAAO,EAAE,GAAG;EACZ,MAAM,EAAE,GAAG;EACX,OAAO,EAAE,GAAG;EACZ,QAAQ,EAAE,GAAG;EACb,OAAO,EAAE,GAAG;EACZ,QAAQ,EAAE,GAAG;EACb,OAAO,EAAE,GAAG;EACZ,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,QAAQ,EAAE,GAAG;EACb,OAAO,EAAE,GAAG;EACZ,QAAQ,EAAE,GAAG;EACb,UAAU,EAAE,GAAG;EACf,SAAS,EAAE,GAAG;EACd,QAAQ,EAAE,GAAG;EACb,OAAO,EAAE;AACX,CAAC;AACD,MAAMC,kBAAkB,GAAGC,CAAC,IAAIF,YAAY,CAACE,CAAC,CAAC;AAC/C,OAAO,MAAMC,QAAQ,GAAGC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAACN,eAAe,EAAEE,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}