{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\contexts\\\\TranslationContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { loadAvailableLanguages, getCurrentLanguage, changeLanguage as changeI18nLanguage, setLoginStatus, loadSavedLanguageTranslations } from '../multilinguial/i18n';\nimport { updateLanguage } from '../multilinguial/LanguageService';\nimport useInfoStore from '../store/UserInfoStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TranslationContext = /*#__PURE__*/createContext(undefined);\nexport const useTranslationContext = () => {\n  _s();\n  const context = useContext(TranslationContext);\n  if (!context) {\n    throw new Error('useTranslationContext must be used within a TranslationProvider');\n  }\n  return context;\n};\n_s(useTranslationContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const TranslationProvider = ({\n  children\n}) => {\n  _s2();\n  const {\n    t,\n    i18n\n  } = useTranslation();\n  const [availableLanguages, setAvailableLanguages] = useState([]);\n  const [currentLanguage, setCurrentLanguage] = useState('en');\n  const [currentLanguageInfo, setCurrentLanguageInfo] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n\n  // Get organization ID and user info from store\n  const orgId = useInfoStore(state => {\n    var _state$orgDetails, _state$user;\n    return ((_state$orgDetails = state.orgDetails) === null || _state$orgDetails === void 0 ? void 0 : _state$orgDetails.OrganizationId) || ((_state$user = state.user) === null || _state$user === void 0 ? void 0 : _state$user.OrganizationId);\n  });\n  const accessToken = useInfoStore(state => state.accessToken);\n  const userInfoObj = useInfoStore(state => state.user);\n\n  // Update login status in i18n module when user logs in/out\n  useEffect(() => {\n    const hasValidLogin = !!(orgId && accessToken);\n    setLoginStatus(hasValidLogin, orgId);\n  }, [orgId, accessToken]);\n\n  // Load available languages when user logs in\n  useEffect(() => {\n    const initializeLanguages = async () => {\n      if (!orgId || !accessToken) {\n        setAvailableLanguages([]);\n        setIsInitialized(false);\n        return;\n      }\n      try {\n        setIsLoading(true);\n        console.log('🌐 Loading available languages for organization:', orgId);\n        const languages = await loadAvailableLanguages();\n        setAvailableLanguages(languages);\n        setIsInitialized(true);\n\n        // Load saved language translations if user had a saved language\n        await loadSavedLanguageTranslations(orgId);\n\n        // Set initial language from user preference only on first load\n        if (userInfoObj !== null && userInfoObj !== void 0 && userInfoObj.Language && languages.length > 0) {\n          console.log('🔄 Setting initial language from user preference:', userInfoObj.Language);\n\n          // Find the language code that matches the user's language preference\n          const userLanguage = languages.find(lang => lang.Language.toLowerCase() === userInfoObj.Language.toLowerCase());\n          if (userLanguage) {\n            await changeI18nLanguage(userLanguage.LanguageCode);\n            console.log('✅ Initial language set to:', userLanguage.LanguageCode);\n          } else {\n            console.log('⚠️ User language preference not found in available languages:', userInfoObj.Language);\n            // Set English as default if user's language is not found\n            await changeI18nLanguage('en');\n            console.log('✅ Default language (English) set');\n          }\n        } else if (userInfoObj && !userInfoObj.Language) {\n          // If user exists but has no language preference, set English as default\n          console.log('ℹ️ No language preference found for user, setting English as default');\n          await changeI18nLanguage('en');\n          console.log('✅ Default language (English) set');\n        }\n        console.log('✅ Languages loaded successfully:', languages.length);\n      } catch (error) {\n        console.error('❌ Failed to load languages:', error);\n        setAvailableLanguages([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    initializeLanguages();\n  }, [orgId, accessToken]);\n\n  // Update current language when i18n language changes\n  useEffect(() => {\n    const handleLanguageChange = () => {\n      const currentLang = getCurrentLanguage();\n      setCurrentLanguage(currentLang);\n\n      // Update current language info\n      const currentInfo = availableLanguages.find(lang => lang.LanguageCode.toLowerCase() === currentLang.toLowerCase());\n      setCurrentLanguageInfo(currentInfo || null);\n    };\n\n    // Initial setup\n    handleLanguageChange();\n\n    // Listen for language changes\n    i18n.on('languageChanged', handleLanguageChange);\n    return () => {\n      i18n.off('languageChanged', handleLanguageChange);\n    };\n  }, [i18n, availableLanguages]);\n  const changeLanguage = async languageCode => {\n    try {\n      setIsLoading(true);\n      const selectedLanguage = availableLanguages.find(lang => lang.LanguageCode.toLowerCase() === languageCode.toLowerCase());\n      if (!selectedLanguage) {\n        throw new Error(`Language not found: ${languageCode}`);\n      }\n      console.log(`🔄 Changing language to ${languageCode} (${selectedLanguage.Language})`);\n\n      // Call the updateLanguage API to update user's language preference on the server\n      await updateLanguage(selectedLanguage.Language);\n      console.log(`✅ User language preference updated on server: ${selectedLanguage.Language}`);\n\n      // Use the simplified change language function\n      await changeI18nLanguage(languageCode);\n      console.log('✅ Language changed successfully');\n    } catch (error) {\n      console.error('❌ Failed to change language:', error);\n      throw error;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const refreshLanguages = async () => {\n    try {\n      setIsLoading(true);\n      const languages = await loadAvailableLanguages();\n      setAvailableLanguages(languages);\n    } catch (error) {\n      console.error('Failed to refresh languages:', error);\n      throw error;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const contextValue = {\n    availableLanguages,\n    currentLanguage,\n    currentLanguageInfo,\n    isLoading,\n    isInitialized,\n    changeLanguage,\n    refreshLanguages,\n    t\n  };\n  return /*#__PURE__*/_jsxDEV(TranslationContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 203,\n    columnNumber: 5\n  }, this);\n};\n_s2(TranslationProvider, \"xb76ldl/L5ty7rVPGAom65sygGY=\", false, function () {\n  return [useTranslation, useInfoStore, useInfoStore, useInfoStore];\n});\n_c = TranslationProvider;\nexport default TranslationProvider;\nvar _c;\n$RefreshReg$(_c, \"TranslationProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "useTranslation", "loadAvailableLanguages", "getCurrentLanguage", "changeLanguage", "changeI18nLanguage", "setLoginStatus", "loadSavedLanguageTranslations", "updateLanguage", "useInfoStore", "jsxDEV", "_jsxDEV", "TranslationContext", "undefined", "useTranslationContext", "_s", "context", "Error", "TranslationProvider", "children", "_s2", "t", "i18n", "availableLanguages", "setAvailableLanguages", "currentLanguage", "setCurrentLanguage", "currentLanguageInfo", "setCurrentLanguageInfo", "isLoading", "setIsLoading", "isInitialized", "setIsInitialized", "orgId", "state", "_state$orgDetails", "_state$user", "orgDetails", "OrganizationId", "user", "accessToken", "userInfoObj", "has<PERSON>alid<PERSON><PERSON><PERSON>", "initializeLanguages", "console", "log", "languages", "Language", "length", "userLanguage", "find", "lang", "toLowerCase", "LanguageCode", "error", "handleLanguageChange", "currentLang", "currentInfo", "on", "off", "languageCode", "selectedLanguage", "refreshLanguages", "contextValue", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/contexts/TranslationContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport {\r\n  loadAvailableLanguages,\r\n  getAvailableLanguages,\r\n  getCurrentLanguage,\r\n  changeLanguage as changeI18nLanguage,\r\n  setLoginStatus,\r\n  loadSavedLanguageTranslations,\r\n  LanguageType\r\n} from '../multilinguial/i18n';\r\nimport { updateLanguage } from '../multilinguial/LanguageService';\r\nimport useInfoStore from '../store/UserInfoStore';\r\n\r\ninterface TranslationContextType {\r\n  // Language data\r\n  availableLanguages: LanguageType[];\r\n  currentLanguage: string;\r\n  currentLanguageInfo: LanguageType | null;\r\n\r\n  // Loading states\r\n  isLoading: boolean;\r\n  isInitialized: boolean;\r\n\r\n  // Actions\r\n  changeLanguage: (languageCode: string) => Promise<void>;\r\n  refreshLanguages: () => Promise<void>;\r\n\r\n  // Translation function\r\n  t: (key: string, options?: any) => string;\r\n}\r\n\r\nconst TranslationContext = createContext<TranslationContextType | undefined>(undefined);\r\n\r\nexport const useTranslationContext = () => {\r\n  const context = useContext(TranslationContext);\r\n  if (!context) {\r\n    throw new Error('useTranslationContext must be used within a TranslationProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface TranslationProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const TranslationProvider: React.FC<TranslationProviderProps> = ({ children }) => {\r\n  const { t, i18n } = useTranslation();\r\n  const [availableLanguages, setAvailableLanguages] = useState<LanguageType[]>([]);\r\n  const [currentLanguage, setCurrentLanguage] = useState<string>('en');\r\n  const [currentLanguageInfo, setCurrentLanguageInfo] = useState<LanguageType | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n\r\n  // Get organization ID and user info from store\r\n  const orgId = useInfoStore((state) => state.orgDetails?.OrganizationId || state.user?.OrganizationId);\r\n  const accessToken = useInfoStore((state) => state.accessToken);\r\n  const userInfoObj = useInfoStore((state) => state.user);\r\n\r\n  // Update login status in i18n module when user logs in/out\r\n  useEffect(() => {\r\n    const hasValidLogin = !!(orgId && accessToken);\r\n    setLoginStatus(hasValidLogin, orgId);\r\n  }, [orgId, accessToken]);\r\n\r\n  // Load available languages when user logs in\r\n  useEffect(() => {\r\n    const initializeLanguages = async () => {\r\n      if (!orgId || !accessToken) {\r\n        setAvailableLanguages([]);\r\n        setIsInitialized(false);\r\n        return;\r\n      }\r\n\r\n      try {\r\n        setIsLoading(true);\r\n        console.log('🌐 Loading available languages for organization:', orgId);\r\n\r\n        const languages = await loadAvailableLanguages();\r\n        setAvailableLanguages(languages);\r\n        setIsInitialized(true);\r\n\r\n        // Load saved language translations if user had a saved language\r\n        await loadSavedLanguageTranslations(orgId);\r\n\r\n        // Set initial language from user preference only on first load\r\n        if (userInfoObj?.Language && languages.length > 0) {\r\n          console.log('🔄 Setting initial language from user preference:', userInfoObj.Language);\r\n\r\n          // Find the language code that matches the user's language preference\r\n          const userLanguage = languages.find(lang =>\r\n            lang.Language.toLowerCase() === userInfoObj.Language!.toLowerCase()\r\n          );\r\n\r\n          if (userLanguage) {\r\n            await changeI18nLanguage(userLanguage.LanguageCode);\r\n            console.log('✅ Initial language set to:', userLanguage.LanguageCode);\r\n          } else {\r\n            console.log('⚠️ User language preference not found in available languages:', userInfoObj.Language);\r\n            // Set English as default if user's language is not found\r\n            await changeI18nLanguage('en');\r\n            console.log('✅ Default language (English) set');\r\n          }\r\n        } else if (userInfoObj && !userInfoObj.Language) {\r\n          // If user exists but has no language preference, set English as default\r\n          console.log('ℹ️ No language preference found for user, setting English as default');\r\n          await changeI18nLanguage('en');\r\n          console.log('✅ Default language (English) set');\r\n        }\r\n\r\n        console.log('✅ Languages loaded successfully:', languages.length);\r\n      } catch (error) {\r\n        console.error('❌ Failed to load languages:', error);\r\n        setAvailableLanguages([]);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    initializeLanguages();\r\n  }, [orgId, accessToken]);\r\n\r\n  // Update current language when i18n language changes\r\n  useEffect(() => {\r\n    const handleLanguageChange = () => {\r\n      const currentLang = getCurrentLanguage();\r\n      setCurrentLanguage(currentLang);\r\n\r\n      // Update current language info\r\n      const currentInfo = availableLanguages.find(\r\n        lang => lang.LanguageCode.toLowerCase() === currentLang.toLowerCase()\r\n      );\r\n      setCurrentLanguageInfo(currentInfo || null);\r\n    };\r\n\r\n    // Initial setup\r\n    handleLanguageChange();\r\n\r\n    // Listen for language changes\r\n    i18n.on('languageChanged', handleLanguageChange);\r\n\r\n    return () => {\r\n      i18n.off('languageChanged', handleLanguageChange);\r\n    };\r\n  }, [i18n, availableLanguages]);\r\n\r\n  const changeLanguage = async (languageCode: string): Promise<void> => {\r\n    try {\r\n      setIsLoading(true);\r\n\r\n      const selectedLanguage = availableLanguages.find(\r\n        lang => lang.LanguageCode.toLowerCase() === languageCode.toLowerCase()\r\n      );\r\n\r\n      if (!selectedLanguage) {\r\n        throw new Error(`Language not found: ${languageCode}`);\r\n      }\r\n\r\n      console.log(`🔄 Changing language to ${languageCode} (${selectedLanguage.Language})`);\r\n\r\n      // Call the updateLanguage API to update user's language preference on the server\r\n      await updateLanguage(selectedLanguage.Language);\r\n      console.log(`✅ User language preference updated on server: ${selectedLanguage.Language}`);\r\n\r\n      // Use the simplified change language function\r\n      await changeI18nLanguage(languageCode);\r\n\r\n      console.log('✅ Language changed successfully');\r\n\r\n    } catch (error) {\r\n      console.error('❌ Failed to change language:', error);\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const refreshLanguages = async (): Promise<void> => {\r\n    try {\r\n      setIsLoading(true);\r\n      const languages = await loadAvailableLanguages();\r\n      setAvailableLanguages(languages);\r\n    } catch (error) {\r\n      console.error('Failed to refresh languages:', error);\r\n      throw error;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const contextValue: TranslationContextType = {\r\n    availableLanguages,\r\n    currentLanguage,\r\n    currentLanguageInfo,\r\n    isLoading,\r\n    isInitialized,\r\n    changeLanguage,\r\n    refreshLanguages,\r\n    t,\r\n  };\r\n\r\n  return (\r\n    <TranslationContext.Provider value={contextValue}>\r\n      {children}\r\n    </TranslationContext.Provider>\r\n  );\r\n};\r\n\r\nexport default TranslationProvider;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AACxF,SAASC,cAAc,QAAQ,eAAe;AAC9C,SACEC,sBAAsB,EAEtBC,kBAAkB,EAClBC,cAAc,IAAIC,kBAAkB,EACpCC,cAAc,EACdC,6BAA6B,QAExB,uBAAuB;AAC9B,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAOC,YAAY,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBlD,MAAMC,kBAAkB,gBAAGf,aAAa,CAAqCgB,SAAS,CAAC;AAEvF,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAMC,OAAO,GAAGlB,UAAU,CAACc,kBAAkB,CAAC;EAC9C,IAAI,CAACI,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,iEAAiE,CAAC;EACpF;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,qBAAqB;AAYlC,OAAO,MAAMI,mBAAuD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACvF,MAAM;IAAEC,CAAC;IAAEC;EAAK,CAAC,GAAGrB,cAAc,CAAC,CAAC;EACpC,MAAM,CAACsB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxB,QAAQ,CAAiB,EAAE,CAAC;EAChF,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAS,IAAI,CAAC;EACpE,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAsB,IAAI,CAAC;EACzF,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAMiC,KAAK,GAAGxB,YAAY,CAAEyB,KAAK;IAAA,IAAAC,iBAAA,EAAAC,WAAA;IAAA,OAAK,EAAAD,iBAAA,GAAAD,KAAK,CAACG,UAAU,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBG,cAAc,OAAAF,WAAA,GAAIF,KAAK,CAACK,IAAI,cAAAH,WAAA,uBAAVA,WAAA,CAAYE,cAAc;EAAA,EAAC;EACrG,MAAME,WAAW,GAAG/B,YAAY,CAAEyB,KAAK,IAAKA,KAAK,CAACM,WAAW,CAAC;EAC9D,MAAMC,WAAW,GAAGhC,YAAY,CAAEyB,KAAK,IAAKA,KAAK,CAACK,IAAI,CAAC;;EAEvD;EACAxC,SAAS,CAAC,MAAM;IACd,MAAM2C,aAAa,GAAG,CAAC,EAAET,KAAK,IAAIO,WAAW,CAAC;IAC9ClC,cAAc,CAACoC,aAAa,EAAET,KAAK,CAAC;EACtC,CAAC,EAAE,CAACA,KAAK,EAAEO,WAAW,CAAC,CAAC;;EAExB;EACAzC,SAAS,CAAC,MAAM;IACd,MAAM4C,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAI,CAACV,KAAK,IAAI,CAACO,WAAW,EAAE;QAC1BhB,qBAAqB,CAAC,EAAE,CAAC;QACzBQ,gBAAgB,CAAC,KAAK,CAAC;QACvB;MACF;MAEA,IAAI;QACFF,YAAY,CAAC,IAAI,CAAC;QAClBc,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEZ,KAAK,CAAC;QAEtE,MAAMa,SAAS,GAAG,MAAM5C,sBAAsB,CAAC,CAAC;QAChDsB,qBAAqB,CAACsB,SAAS,CAAC;QAChCd,gBAAgB,CAAC,IAAI,CAAC;;QAEtB;QACA,MAAMzB,6BAA6B,CAAC0B,KAAK,CAAC;;QAE1C;QACA,IAAIQ,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEM,QAAQ,IAAID,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;UACjDJ,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEJ,WAAW,CAACM,QAAQ,CAAC;;UAEtF;UACA,MAAME,YAAY,GAAGH,SAAS,CAACI,IAAI,CAACC,IAAI,IACtCA,IAAI,CAACJ,QAAQ,CAACK,WAAW,CAAC,CAAC,KAAKX,WAAW,CAACM,QAAQ,CAAEK,WAAW,CAAC,CACpE,CAAC;UAED,IAAIH,YAAY,EAAE;YAChB,MAAM5C,kBAAkB,CAAC4C,YAAY,CAACI,YAAY,CAAC;YACnDT,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,YAAY,CAACI,YAAY,CAAC;UACtE,CAAC,MAAM;YACLT,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAEJ,WAAW,CAACM,QAAQ,CAAC;YAClG;YACA,MAAM1C,kBAAkB,CAAC,IAAI,CAAC;YAC9BuC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UACjD;QACF,CAAC,MAAM,IAAIJ,WAAW,IAAI,CAACA,WAAW,CAACM,QAAQ,EAAE;UAC/C;UACAH,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;UACnF,MAAMxC,kBAAkB,CAAC,IAAI,CAAC;UAC9BuC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;QACjD;QAEAD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEC,SAAS,CAACE,MAAM,CAAC;MACnE,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdV,OAAO,CAACU,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD9B,qBAAqB,CAAC,EAAE,CAAC;MAC3B,CAAC,SAAS;QACRM,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDa,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACV,KAAK,EAAEO,WAAW,CAAC,CAAC;;EAExB;EACAzC,SAAS,CAAC,MAAM;IACd,MAAMwD,oBAAoB,GAAGA,CAAA,KAAM;MACjC,MAAMC,WAAW,GAAGrD,kBAAkB,CAAC,CAAC;MACxCuB,kBAAkB,CAAC8B,WAAW,CAAC;;MAE/B;MACA,MAAMC,WAAW,GAAGlC,kBAAkB,CAAC2B,IAAI,CACzCC,IAAI,IAAIA,IAAI,CAACE,YAAY,CAACD,WAAW,CAAC,CAAC,KAAKI,WAAW,CAACJ,WAAW,CAAC,CACtE,CAAC;MACDxB,sBAAsB,CAAC6B,WAAW,IAAI,IAAI,CAAC;IAC7C,CAAC;;IAED;IACAF,oBAAoB,CAAC,CAAC;;IAEtB;IACAjC,IAAI,CAACoC,EAAE,CAAC,iBAAiB,EAAEH,oBAAoB,CAAC;IAEhD,OAAO,MAAM;MACXjC,IAAI,CAACqC,GAAG,CAAC,iBAAiB,EAAEJ,oBAAoB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,CAACjC,IAAI,EAAEC,kBAAkB,CAAC,CAAC;EAE9B,MAAMnB,cAAc,GAAG,MAAOwD,YAAoB,IAAoB;IACpE,IAAI;MACF9B,YAAY,CAAC,IAAI,CAAC;MAElB,MAAM+B,gBAAgB,GAAGtC,kBAAkB,CAAC2B,IAAI,CAC9CC,IAAI,IAAIA,IAAI,CAACE,YAAY,CAACD,WAAW,CAAC,CAAC,KAAKQ,YAAY,CAACR,WAAW,CAAC,CACvE,CAAC;MAED,IAAI,CAACS,gBAAgB,EAAE;QACrB,MAAM,IAAI5C,KAAK,CAAC,uBAAuB2C,YAAY,EAAE,CAAC;MACxD;MAEAhB,OAAO,CAACC,GAAG,CAAC,2BAA2Be,YAAY,KAAKC,gBAAgB,CAACd,QAAQ,GAAG,CAAC;;MAErF;MACA,MAAMvC,cAAc,CAACqD,gBAAgB,CAACd,QAAQ,CAAC;MAC/CH,OAAO,CAACC,GAAG,CAAC,iDAAiDgB,gBAAgB,CAACd,QAAQ,EAAE,CAAC;;MAEzF;MACA,MAAM1C,kBAAkB,CAACuD,YAAY,CAAC;MAEtChB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAEhD,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb,CAAC,SAAS;MACRxB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMgC,gBAAgB,GAAG,MAAAA,CAAA,KAA2B;IAClD,IAAI;MACFhC,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMgB,SAAS,GAAG,MAAM5C,sBAAsB,CAAC,CAAC;MAChDsB,qBAAqB,CAACsB,SAAS,CAAC;IAClC,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb,CAAC,SAAS;MACRxB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMiC,YAAoC,GAAG;IAC3CxC,kBAAkB;IAClBE,eAAe;IACfE,mBAAmB;IACnBE,SAAS;IACTE,aAAa;IACb3B,cAAc;IACd0D,gBAAgB;IAChBzC;EACF,CAAC;EAED,oBACEV,OAAA,CAACC,kBAAkB,CAACoD,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAA5C,QAAA,EAC9CA;EAAQ;IAAA+C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACkB,CAAC;AAElC,CAAC;AAACjD,GAAA,CAhKWF,mBAAuD;EAAA,QAC9CjB,cAAc,EAQpBQ,YAAY,EACNA,YAAY,EACZA,YAAY;AAAA;AAAA6D,EAAA,GAXrBpD,mBAAuD;AAkKpE,eAAeA,mBAAmB;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}