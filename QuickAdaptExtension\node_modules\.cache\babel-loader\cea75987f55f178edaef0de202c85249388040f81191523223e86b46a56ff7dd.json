{"ast": null, "code": "import { adminApiService, userApiService } from './APIService';\nexport const NewAgentTraining = async agent => {\n  try {\n    const response = await userApiService.post(`Assistant/NewAgentTraining`, agent);\n    return response.data;\n  } catch (error) {\n    console.error(\"Error uploading agent\", error);\n    return {\n      message: \"Upload failed\"\n    };\n  }\n};\n_c = NewAgentTraining;\nexport const CreateInteraction = async (userCommand, accountId, targetUrl) => {\n  try {\n    const requestBody = {\n      userCommand,\n      accountId,\n      targetUrl\n    };\n    const response = await adminApiService.post(`Ai/CreateInteraction`, requestBody);\n    return response.data;\n  } catch (error) {\n    console.error(\"Error in creating integration\", error);\n    return [];\n  }\n};\n_c2 = CreateInteraction;\nvar _c, _c2;\n$RefreshReg$(_c, \"NewAgentTraining\");\n$RefreshReg$(_c2, \"CreateInteraction\");", "map": {"version": 3, "names": ["adminApiService", "userApiService", "NewAgentTraining", "agent", "response", "post", "data", "error", "console", "message", "_c", "CreateInteraction", "userCommand", "accountId", "targetUrl", "requestBody", "_c2", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/services/AIService.ts"], "sourcesContent": ["import { adminApiService, userApiService } from './APIService';\r\nimport { ScrapedElement } from './ScrapingService';\r\nexport interface Agent {\r\n  Name: string | undefined;\r\n  Description: string | undefined;\r\n  AccountId:string;\r\n  url: string;\r\n  TrainingFields: ScrapedElement[];\r\n  AdditionalContext?: string;\r\n}\r\n\r\n\r\nexport const NewAgentTraining = async (agent: Agent) => {\r\n    try {\r\n        const response = await userApiService.post(`Assistant/NewAgentTraining`, agent);\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error uploading agent\", error);\r\n        return { message: \"Upload failed\" };\r\n    }\r\n};\r\n\r\n\r\nexport const CreateInteraction = async (userCommand: string, accountId: string,targetUrl:string) => {\r\n    \r\n    try {\r\n        const requestBody = {\r\n\t\t\tuserCommand,\r\n\t\t\taccountId,\r\n\t\t\ttargetUrl,\r\n\t\t};\r\n        const response = await adminApiService.post(`Ai/CreateInteraction`, requestBody)\r\n        return response.data;\r\n    } catch (error) {\r\n        console.error(\"Error in creating integration\", error);\r\n        return [];\r\n    }\r\n}"], "mappings": "AAAA,SAASA,eAAe,EAAEC,cAAc,QAAQ,cAAc;AAY9D,OAAO,MAAMC,gBAAgB,GAAG,MAAOC,KAAY,IAAK;EACpD,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMH,cAAc,CAACI,IAAI,CAAC,4BAA4B,EAAEF,KAAK,CAAC;IAC/E,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO;MAAEE,OAAO,EAAE;IAAgB,CAAC;EACvC;AACJ,CAAC;AAACC,EAAA,GARWR,gBAAgB;AAW7B,OAAO,MAAMS,iBAAiB,GAAG,MAAAA,CAAOC,WAAmB,EAAEC,SAAiB,EAACC,SAAgB,KAAK;EAEhG,IAAI;IACA,MAAMC,WAAW,GAAG;MACzBH,WAAW;MACXC,SAAS;MACTC;IACD,CAAC;IACK,MAAMV,QAAQ,GAAG,MAAMJ,eAAe,CAACK,IAAI,CAAC,sBAAsB,EAAEU,WAAW,CAAC;IAChF,OAAOX,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,OAAO,EAAE;EACb;AACJ,CAAC;AAAAS,GAAA,GAdYL,iBAAiB;AAAA,IAAAD,EAAA,EAAAM,GAAA;AAAAC,YAAA,CAAAP,EAAA;AAAAO,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}