{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\guideList\\\\SnackbarContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useRef, useCallback } from \"react\";\nimport { Snackbar, Alert } from \"@mui/material\";\nimport { useTranslation } from 'react-i18next';\nimport { Fragment as _Fragment, jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SnackbarContext = /*#__PURE__*/createContext(undefined);\nexport const useSnackbar = () => {\n  _s();\n  const context = useContext(SnackbarContext);\n  if (!context) {\n    throw new Error(\"useSnackbar must be used within a SnackbarProvider\");\n  }\n  return context;\n};\n_s(useSnackbar, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const SnackbarProvider = ({\n  children\n}) => {\n  _s2();\n  const {\n    t: translate\n  } = useTranslation();\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState(\"\");\n  const [snackbarSeverity, setSnackbarSeverity] = useState(\"success\");\n\n  // Add a ref to track snackbar timers\n  const snackbarTimerRef = useRef(null);\n  const closeTimerRef = useRef(null);\n  const openSnackbar = useCallback((message, severity) => {\n    // Clear any existing timers to prevent conflicts\n    if (snackbarTimerRef.current) {\n      clearTimeout(snackbarTimerRef.current);\n      snackbarTimerRef.current = null;\n    }\n    if (closeTimerRef.current) {\n      clearTimeout(closeTimerRef.current);\n      closeTimerRef.current = null;\n    }\n\n    // First close any existing snackbar\n    setSnackbarOpen(false);\n\n    // Then set up the new snackbar with a slight delay\n    snackbarTimerRef.current = setTimeout(() => {\n      setSnackbarMessage(translate(message));\n      setSnackbarSeverity(severity);\n      setSnackbarOpen(true);\n\n      // Set up auto-close timer\n      closeTimerRef.current = setTimeout(() => {\n        setSnackbarOpen(false);\n        closeTimerRef.current = null;\n      }, 4000);\n      snackbarTimerRef.current = null;\n    }, 100);\n  }, [translate]);\n  const handleSnackbarClose = () => {\n    if (closeTimerRef.current) {\n      clearTimeout(closeTimerRef.current);\n      closeTimerRef.current = null;\n    }\n    setSnackbarOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(SnackbarContext.Provider, {\n    value: {\n      openSnackbar\n    },\n    children: [children, /*#__PURE__*/_jsxDEV(Snackbar, {\n      className: `qadpt-toaster ${snackbarSeverity === 'success' ? 'qadpt-toaster-success' : 'qadpt-toaster-error'}`,\n      open: snackbarOpen,\n      onClose: handleSnackbarClose,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleSnackbarClose,\n        severity: snackbarSeverity,\n        className: \"qadpt-alert\",\n        icon: /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false),\n        children: snackbarMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 7\n  }, this);\n};\n_s2(SnackbarProvider, \"OyDcqtNdS6FX9WBqEbaPb8bRlSI=\", false, function () {\n  return [useTranslation];\n});\n_c = SnackbarProvider;\nvar _c;\n$RefreshReg$(_c, \"SnackbarProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useRef", "useCallback", "Snackbar", "<PERSON><PERSON>", "useTranslation", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "SnackbarContext", "undefined", "useSnackbar", "_s", "context", "Error", "SnackbarProvider", "children", "_s2", "t", "translate", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "snackbarTimerRef", "closeTimerRef", "openSnackbar", "message", "severity", "current", "clearTimeout", "setTimeout", "handleSnackbarClose", "Provider", "value", "className", "open", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/guideList/SnackbarContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useRef, ReactNode, useCallback } from \"react\";\r\nimport { Snackbar, Alert } from \"@mui/material\";\r\nimport CelebrationOutlinedIcon from \"@mui/icons-material/CelebrationOutlined\";\r\nimport ErrorOutlineOutlinedIcon from \"@mui/icons-material/ErrorOutlineOutlined\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\ntype SnackbarContextType = {\r\n\topenSnackbar: (message: string, severity: \"success\" | \"error\") => void;\r\n};\r\n\r\nconst SnackbarContext = createContext<SnackbarContextType | undefined>(undefined);\r\n\r\nexport const useSnackbar = () => {\r\n\tconst context = useContext(SnackbarContext);\r\n\tif (!context) {\r\n\t\tthrow new Error(\"useSnackbar must be used within a SnackbarProvider\");\r\n\t}\r\n\treturn context;\r\n};\r\n\r\nexport const SnackbarProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\r\n  const { t: translate } = useTranslation();\r\n\tconst [snackbarOpen, setSnackbarOpen] = useState(false);\r\n\tconst [snackbarMessage, setSnackbarMessage] = useState(\"\");\r\n\tconst [snackbarSeverity, setSnackbarSeverity] = useState<\"success\" | \"error\">(\"success\");\r\n\r\n  // Add a ref to track snackbar timers\r\n  const snackbarTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n  const closeTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  const openSnackbar = useCallback((message: string, severity: \"success\" | \"error\") => {\r\n    // Clear any existing timers to prevent conflicts\r\n    if (snackbarTimerRef.current) {\r\n      clearTimeout(snackbarTimerRef.current);\r\n      snackbarTimerRef.current = null;\r\n    }\r\n\r\n    if (closeTimerRef.current) {\r\n      clearTimeout(closeTimerRef.current);\r\n      closeTimerRef.current = null;\r\n    }\r\n\r\n    // First close any existing snackbar\r\n    setSnackbarOpen(false);\r\n\r\n    // Then set up the new snackbar with a slight delay\r\n    snackbarTimerRef.current = setTimeout(() => {\r\n      setSnackbarMessage(translate(message));\r\n      setSnackbarSeverity(severity);\r\n      setSnackbarOpen(true);\r\n\r\n      // Set up auto-close timer\r\n      closeTimerRef.current = setTimeout(() => {\r\n        setSnackbarOpen(false);\r\n        closeTimerRef.current = null;\r\n      }, 4000);\r\n\r\n      snackbarTimerRef.current = null;\r\n    }, 100);\r\n  }, [translate]);\r\n\r\n  const handleSnackbarClose = () => {\r\n    if (closeTimerRef.current) {\r\n      clearTimeout(closeTimerRef.current);\r\n      closeTimerRef.current = null;\r\n    }\r\n    setSnackbarOpen(false);\r\n  };\r\n\r\n    return (\r\n      <SnackbarContext.Provider value={{ openSnackbar }}>\r\n        {children}\r\n        <Snackbar\r\n          className={`qadpt-toaster ${snackbarSeverity === 'success' ? 'qadpt-toaster-success' : 'qadpt-toaster-error'}`}\r\n          open={snackbarOpen}\r\n          onClose={handleSnackbarClose}\r\n          anchorOrigin={{ vertical: \"top\", horizontal: \"center\" }}\r\n        >\r\n          <Alert\r\n            onClose={handleSnackbarClose}\r\n            severity={snackbarSeverity}\r\n            className=\"qadpt-alert\"\r\n            icon={<></>}\r\n          >\r\n            {snackbarMessage}\r\n          </Alert>\r\n        </Snackbar>\r\n      </SnackbarContext.Provider>\r\n  );\r\n};\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAaC,WAAW,QAAQ,OAAO;AAClG,SAASC,QAAQ,EAAEC,KAAK,QAAQ,eAAe;AAG/C,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,QAAA,IAAAC,SAAA,EAAAC,MAAA,IAAAC,OAAA;AAM/C,MAAMC,eAAe,gBAAGZ,aAAa,CAAkCa,SAAS,CAAC;AAEjF,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,OAAO,GAAGf,UAAU,CAACW,eAAe,CAAC;EAC3C,IAAI,CAACI,OAAO,EAAE;IACb,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;EACtE;EACA,OAAOD,OAAO;AACf,CAAC;AAACD,EAAA,CANWD,WAAW;AAQxB,OAAO,MAAMI,gBAAmD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACnF,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGf,cAAc,CAAC,CAAC;EAC1C,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1B,QAAQ,CAAsB,SAAS,CAAC;;EAEvF;EACA,MAAM2B,gBAAgB,GAAG1B,MAAM,CAAwB,IAAI,CAAC;EAC5D,MAAM2B,aAAa,GAAG3B,MAAM,CAAwB,IAAI,CAAC;EAEzD,MAAM4B,YAAY,GAAG3B,WAAW,CAAC,CAAC4B,OAAe,EAAEC,QAA6B,KAAK;IACnF;IACA,IAAIJ,gBAAgB,CAACK,OAAO,EAAE;MAC5BC,YAAY,CAACN,gBAAgB,CAACK,OAAO,CAAC;MACtCL,gBAAgB,CAACK,OAAO,GAAG,IAAI;IACjC;IAEA,IAAIJ,aAAa,CAACI,OAAO,EAAE;MACzBC,YAAY,CAACL,aAAa,CAACI,OAAO,CAAC;MACnCJ,aAAa,CAACI,OAAO,GAAG,IAAI;IAC9B;;IAEA;IACAV,eAAe,CAAC,KAAK,CAAC;;IAEtB;IACAK,gBAAgB,CAACK,OAAO,GAAGE,UAAU,CAAC,MAAM;MAC1CV,kBAAkB,CAACJ,SAAS,CAACU,OAAO,CAAC,CAAC;MACtCJ,mBAAmB,CAACK,QAAQ,CAAC;MAC7BT,eAAe,CAAC,IAAI,CAAC;;MAErB;MACAM,aAAa,CAACI,OAAO,GAAGE,UAAU,CAAC,MAAM;QACvCZ,eAAe,CAAC,KAAK,CAAC;QACtBM,aAAa,CAACI,OAAO,GAAG,IAAI;MAC9B,CAAC,EAAE,IAAI,CAAC;MAERL,gBAAgB,CAACK,OAAO,GAAG,IAAI;IACjC,CAAC,EAAE,GAAG,CAAC;EACT,CAAC,EAAE,CAACZ,SAAS,CAAC,CAAC;EAEf,MAAMe,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIP,aAAa,CAACI,OAAO,EAAE;MACzBC,YAAY,CAACL,aAAa,CAACI,OAAO,CAAC;MACnCJ,aAAa,CAACI,OAAO,GAAG,IAAI;IAC9B;IACAV,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAEC,oBACEb,OAAA,CAACC,eAAe,CAAC0B,QAAQ;IAACC,KAAK,EAAE;MAAER;IAAa,CAAE;IAAAZ,QAAA,GAC/CA,QAAQ,eACTR,OAAA,CAACN,QAAQ;MACPmC,SAAS,EAAE,iBAAiBb,gBAAgB,KAAK,SAAS,GAAG,uBAAuB,GAAG,qBAAqB,EAAG;MAC/Gc,IAAI,EAAElB,YAAa;MACnBmB,OAAO,EAAEL,mBAAoB;MAC7BM,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA1B,QAAA,eAExDR,OAAA,CAACL,KAAK;QACJoC,OAAO,EAAEL,mBAAoB;QAC7BJ,QAAQ,EAAEN,gBAAiB;QAC3Ba,SAAS,EAAC,aAAa;QACvBM,IAAI,eAAEnC,OAAA,CAAAF,SAAA,mBAAI,CAAE;QAAAU,QAAA,EAEXM;MAAe;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAEjC,CAAC;AAAC9B,GAAA,CArEWF,gBAAmD;EAAA,QACrCX,cAAc;AAAA;AAAA4C,EAAA,GAD5BjC,gBAAmD;AAAA,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}