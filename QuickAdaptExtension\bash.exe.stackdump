Stack trace:
Frame         Function      Args
0007FFFFA4F0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA4F0, 0007FFFF93F0) msys-2.0.dll+0x1FE8E
0007FFFFA4F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA7C8) msys-2.0.dll+0x67F9
0007FFFFA4F0  000210046832 (000210286019, 0007FFFFA3A8, 0007FFFFA4F0, 000000000000) msys-2.0.dll+0x6832
0007FFFFA4F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA4F0  000210068E24 (0007FFFFA500, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA7D0  00021006A225 (0007FFFFA500, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8F0390000 ntdll.dll
7FF8EFC00000 KERNEL32.DLL
7FF8EE0B0000 KERNELBASE.dll
7FF8EFF30000 USER32.dll
7FF8EDFE0000 win32u.dll
7FF8EE410000 GDI32.dll
7FF8EDA20000 gdi32full.dll
000210040000 msys-2.0.dll
7FF8EE010000 msvcp_win.dll
7FF8EDE90000 ucrtbase.dll
7FF8EEC80000 advapi32.dll
7FF8EE6C0000 msvcrt.dll
7FF8F00D0000 sechost.dll
7FF8F0220000 RPCRT4.dll
7FF8EDB40000 bcrypt.dll
7FF8ECED0000 CRYPTBASE.DLL
7FF8EDCA0000 bcryptPrimitives.dll
7FF8EF3F0000 IMM32.DLL
