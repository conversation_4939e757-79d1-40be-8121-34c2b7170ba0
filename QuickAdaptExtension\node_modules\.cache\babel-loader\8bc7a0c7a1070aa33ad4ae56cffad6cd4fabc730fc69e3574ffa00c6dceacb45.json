{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\common\\\\LanguageSelector.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { Select, MenuItem, FormControl, Box, Typography, Tooltip, IconButton, TextField, InputAdornment } from '@mui/material';\nimport { Language as LanguageIcon, Search as SearchIcon } from '@mui/icons-material';\nimport { useTranslation } from 'react-i18next';\nimport useInfoStore from '../../store/UserInfoStore';\nimport { useTranslationContext } from '../../contexts/TranslationContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst LanguageSelector = ({\n  variant = 'select',\n  size = 'small',\n  showLabel = false,\n  className = ''\n}) => {\n  _s();\n  var _sortedLanguages$;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    availableLanguages,\n    currentLanguage,\n    changeLanguage,\n    isLoading,\n    isInitialized\n  } = useTranslationContext();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [localLoading, setLocalLoading] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const orgId = useInfoStore(state => {\n    var _state$orgDetails;\n    return (_state$orgDetails = state.orgDetails) === null || _state$orgDetails === void 0 ? void 0 : _state$orgDetails.OrganizationId;\n  });\n\n  // Get native language names\n  const getLanguageDisplayName = (langCode, fallbackName) => {\n    try {\n      const displayNames = new Intl.DisplayNames([langCode], {\n        type: 'language'\n      });\n      return displayNames.of(langCode) || fallbackName;\n    } catch (error) {\n      return fallbackName;\n    }\n  };\n\n  // Sort languages alphabetically by their display name\n  const sortedLanguages = useMemo(() => {\n    return [...availableLanguages].sort((a, b) => {\n      const nameA = getLanguageDisplayName(a.LanguageCode, a.Language);\n      const nameB = getLanguageDisplayName(b.LanguageCode, b.Language);\n      return nameA.localeCompare(nameB);\n    });\n  }, [availableLanguages]);\n\n  // Filter languages based on search query\n  const filteredLanguages = useMemo(() => {\n    if (!searchQuery.trim()) {\n      return sortedLanguages;\n    }\n    return sortedLanguages.filter(lang => {\n      const nativeName = getLanguageDisplayName(lang.LanguageCode, lang.Language);\n      return nativeName.toLowerCase().includes(searchQuery.toLowerCase()) || lang.Language.toLowerCase().includes(searchQuery.toLowerCase()) || lang.LanguageCode.toLowerCase().includes(searchQuery.toLowerCase());\n    });\n  }, [sortedLanguages, searchQuery]);\n\n  // Don't render if not initialized or no languages available\n  if (!isInitialized || availableLanguages.length === 0) {\n    return null;\n  }\n\n  // Ensure we have a valid current language\n  const validCurrentLanguage = sortedLanguages.find(lang => lang.LanguageCode.toLowerCase() === currentLanguage.toLowerCase()) ? currentLanguage : ((_sortedLanguages$ = sortedLanguages[0]) === null || _sortedLanguages$ === void 0 ? void 0 : _sortedLanguages$.LanguageCode) || 'en';\n  const handleLanguageChange = async event => {\n    const newLanguageCode = event.target.value;\n    if (newLanguageCode === validCurrentLanguage) return;\n    setLocalLoading(true);\n    try {\n      await changeLanguage(newLanguageCode);\n      // Language saving is now handled in the i18n module\n    } catch (error) {\n      console.error('Language change failed:', error);\n    } finally {\n      setLocalLoading(false);\n    }\n  };\n  const handleIconClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n    setSearchQuery(''); // Clear search when closing\n  };\n  const handleMenuItemClick = async languageCode => {\n    if (languageCode === validCurrentLanguage) {\n      handleClose();\n      return;\n    }\n    setLocalLoading(true);\n    try {\n      await changeLanguage(languageCode);\n      // Language saving is now handled in the i18n module\n    } catch (error) {\n      console.error('Language change failed:', error);\n    } finally {\n      setLocalLoading(false);\n      handleClose();\n    }\n  };\n  if (variant === 'icon') {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: translate('Change Language'),\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleIconClick,\n          size: size,\n          className: className,\n          disabled: isLoading || localLoading,\n          children: /*#__PURE__*/_jsxDEV(LanguageIcon, {\n            sx: {\n              height: \"22px\",\n              width: \"22px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        open: Boolean(anchorEl),\n        onClose: handleClose,\n        value: validCurrentLanguage,\n        MenuProps: {\n          anchorEl,\n          open: Boolean(anchorEl),\n          onClose: handleClose,\n          anchorOrigin: {\n            vertical: 'bottom',\n            horizontal: 'right'\n          },\n          transformOrigin: {\n            vertical: 'top',\n            horizontal: 'right'\n          },\n          PaperProps: {\n            sx: {\n              maxHeight: 300,\n              minWidth: 250\n            }\n          }\n        },\n        sx: {\n          display: 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'sticky',\n            top: 0,\n            zIndex: 1000,\n            backgroundColor: 'background.paper',\n            p: 1,\n            borderBottom: '1px solid #e0e0e0',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          },\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            placeholder: translate('Search languages...'),\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            onClick: e => e.stopPropagation(),\n            onKeyDown: e => e.stopPropagation(),\n            fullWidth: true,\n            InputProps: {\n              startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                position: \"start\",\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 19\n              }, this)\n            },\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                '& fieldset': {\n                  borderColor: '#e0e0e0'\n                },\n                '&:hover fieldset': {\n                  borderColor: '#b0b0b0'\n                },\n                '&.Mui-focused fieldset': {\n                  borderColor: 'primary.main'\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), filteredLanguages.length === 0 ? /*#__PURE__*/_jsxDEV(MenuItem, {\n          disabled: true,\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: translate('No languages found')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this) : filteredLanguages.map(lang => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: lang.LanguageCode,\n          onClick: () => handleMenuItemClick(lang.LanguageCode),\n          selected: lang.LanguageCode === validCurrentLanguage,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: getLanguageDisplayName(lang.LanguageCode, lang.Language)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 17\n          }, this)\n        }, lang.LanguageId, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(FormControl, {\n    size: size,\n    className: className,\n    children: [showLabel && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      sx: {\n        mb: 0.5\n      },\n      children: translate('Language')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      value: validCurrentLanguage,\n      onChange: handleLanguageChange,\n      onClose: () => setSearchQuery(''),\n      disabled: isLoading || localLoading,\n      MenuProps: {\n        PaperProps: {\n          sx: {\n            maxHeight: 300,\n            minWidth: 250\n          }\n        }\n      },\n      sx: {\n        minWidth: 120,\n        '& .MuiSelect-select': {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'sticky',\n          top: 0,\n          zIndex: 1000,\n          backgroundColor: 'background.paper',\n          p: 1,\n          borderBottom: '1px solid #e0e0e0',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          size: \"small\",\n          placeholder: translate('Search languages...'),\n          value: searchQuery,\n          onChange: e => setSearchQuery(e.target.value),\n          onClick: e => e.stopPropagation(),\n          onKeyDown: e => e.stopPropagation(),\n          fullWidth: true,\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            '& .MuiOutlinedInput-root': {\n              '& fieldset': {\n                borderColor: '#e0e0e0'\n              },\n              '&:hover fieldset': {\n                borderColor: '#b0b0b0'\n              },\n              '&.Mui-focused fieldset': {\n                borderColor: 'primary.main'\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), filteredLanguages.length === 0 ? /*#__PURE__*/_jsxDEV(MenuItem, {\n        disabled: true,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: translate('No languages found')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this) : filteredLanguages.map(lang => /*#__PURE__*/_jsxDEV(MenuItem, {\n        value: lang.LanguageCode,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 1,\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: getLanguageDisplayName(lang.LanguageCode, lang.Language)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 15\n        }, this)\n      }, lang.LanguageId, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 5\n  }, this);\n};\n_s(LanguageSelector, \"ErdMjLbYg7IaRfelJ24qjh3ylWU=\", false, function () {\n  return [useTranslation, useTranslationContext, useInfoStore];\n});\n_c = LanguageSelector;\nexport default LanguageSelector;\nvar _c;\n$RefreshReg$(_c, \"LanguageSelector\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "Select", "MenuItem", "FormControl", "Box", "Typography", "<PERSON><PERSON><PERSON>", "IconButton", "TextField", "InputAdornment", "Language", "LanguageIcon", "Search", "SearchIcon", "useTranslation", "useInfoStore", "useTranslationContext", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LanguageSelector", "variant", "size", "showLabel", "className", "_s", "_sortedLanguages$", "t", "translate", "availableLanguages", "currentLanguage", "changeLanguage", "isLoading", "isInitialized", "anchorEl", "setAnchorEl", "localLoading", "setLocal<PERSON>oading", "searchQuery", "setSearch<PERSON>uery", "orgId", "state", "_state$orgDetails", "orgDetails", "OrganizationId", "getLanguageDisplayName", "langCode", "fallback<PERSON><PERSON>", "displayNames", "Intl", "DisplayNames", "type", "of", "error", "sortedLanguages", "sort", "a", "b", "nameA", "LanguageCode", "nameB", "localeCompare", "filteredLanguages", "trim", "filter", "lang", "nativeName", "toLowerCase", "includes", "length", "validCurrentLanguage", "find", "handleLanguageChange", "event", "newLanguageCode", "target", "value", "console", "handleIconClick", "currentTarget", "handleClose", "handleMenuItemClick", "languageCode", "children", "title", "onClick", "disabled", "sx", "height", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "open", "Boolean", "onClose", "MenuProps", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "maxHeight", "min<PERSON><PERSON><PERSON>", "display", "position", "top", "zIndex", "backgroundColor", "p", "borderBottom", "boxShadow", "placeholder", "onChange", "e", "stopPropagation", "onKeyDown", "fullWidth", "InputProps", "startAdornment", "fontSize", "borderColor", "color", "map", "selected", "alignItems", "gap", "LanguageId", "mb", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/common/LanguageSelector.tsx"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\r\nimport {\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  Box,\r\n  Typography,\r\n  SelectChangeEvent,\r\n  Tooltip,\r\n  IconButton,\r\n  TextField,\r\n  InputAdornment,\r\n} from '@mui/material';\r\nimport { Language as LanguageIcon, Search as SearchIcon } from '@mui/icons-material';\r\nimport { useTranslation } from 'react-i18next';\r\nimport useInfoStore from '../../store/UserInfoStore';\r\nimport { useTranslationContext } from '../../contexts/TranslationContext';\r\n\r\n\r\ninterface LanguageSelectorProps {\r\n  variant?: 'select' | 'icon';\r\n  size?: 'small' | 'medium';\r\n  showLabel?: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst LanguageSelector: React.FC<LanguageSelectorProps> = ({\r\n  variant = 'select',\r\n  size = 'small',\r\n  showLabel = false,\r\n  className = '',\r\n}) => {\r\n  const { t: translate } = useTranslation();\r\n  const { availableLanguages, currentLanguage, changeLanguage, isLoading, isInitialized } = useTranslationContext();\r\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n  const [localLoading, setLocalLoading] = useState(false);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n\r\n  const orgId = useInfoStore((state) => state.orgDetails?.OrganizationId);\r\n\r\n  // Get native language names\r\n  const getLanguageDisplayName = (langCode: string, fallbackName: string) => {\r\n    try {\r\n      const displayNames = new Intl.DisplayNames([langCode], { type: 'language' });\r\n      return displayNames.of(langCode) || fallbackName;\r\n    } catch (error) {\r\n      return fallbackName;\r\n    }\r\n  };\r\n\r\n  // Sort languages alphabetically by their display name\r\n  const sortedLanguages = useMemo(() => {\r\n    return [...availableLanguages].sort((a, b) => {\r\n      const nameA = getLanguageDisplayName(a.LanguageCode, a.Language);\r\n      const nameB = getLanguageDisplayName(b.LanguageCode, b.Language);\r\n      return nameA.localeCompare(nameB);\r\n    });\r\n  }, [availableLanguages]);\r\n\r\n  // Filter languages based on search query\r\n  const filteredLanguages = useMemo(() => {\r\n    if (!searchQuery.trim()) {\r\n      return sortedLanguages;\r\n    }\r\n    return sortedLanguages.filter(lang => {\r\n      const nativeName = getLanguageDisplayName(lang.LanguageCode, lang.Language);\r\n      return nativeName.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        lang.Language.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        lang.LanguageCode.toLowerCase().includes(searchQuery.toLowerCase());\r\n    });\r\n  }, [sortedLanguages, searchQuery]);\r\n\r\n  // Don't render if not initialized or no languages available\r\n  if (!isInitialized || availableLanguages.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  // Ensure we have a valid current language\r\n  const validCurrentLanguage = sortedLanguages.find(\r\n    lang => lang.LanguageCode.toLowerCase() === currentLanguage.toLowerCase()\r\n  ) ? currentLanguage : sortedLanguages[0]?.LanguageCode || 'en';\r\n\r\n  const handleLanguageChange = async (event: SelectChangeEvent<string>) => {\r\n\r\n    const newLanguageCode = event.target.value;\r\n    if (newLanguageCode === validCurrentLanguage) return;\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await changeLanguage(newLanguageCode);\r\n      // Language saving is now handled in the i18n module\r\n    } catch (error) {\r\n      console.error('Language change failed:', error);\r\n    } finally {\r\n      setLocalLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n    setAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setAnchorEl(null);\r\n    setSearchQuery(''); // Clear search when closing\r\n  };\r\n\r\n  const handleMenuItemClick = async (languageCode: string) => {\r\n    if (languageCode === validCurrentLanguage) {\r\n      handleClose();\r\n      return;\r\n    }\r\n\r\n    setLocalLoading(true);\r\n    try {\r\n      await changeLanguage(languageCode);\r\n      // Language saving is now handled in the i18n module\r\n    } catch (error) {\r\n      console.error('Language change failed:', error);\r\n    } finally {\r\n      setLocalLoading(false);\r\n      handleClose();\r\n    }\r\n  };\r\n\r\n  if (variant === 'icon') {\r\n    return (\r\n      <>\r\n        <Tooltip title={translate('Change Language')}>\r\n          <IconButton\r\n            onClick={handleIconClick}\r\n            size={size}\r\n            className={className}\r\n            disabled={isLoading || localLoading}\r\n          >\r\n            <LanguageIcon sx={{height :\"22px\" , width : \"22px\"}} />\r\n          </IconButton>\r\n        </Tooltip>\r\n        <Select\r\n          open={Boolean(anchorEl)}\r\n          onClose={handleClose}\r\n          value={validCurrentLanguage}\r\n          MenuProps={{\r\n            anchorEl,\r\n            open: Boolean(anchorEl),\r\n            onClose: handleClose,\r\n            anchorOrigin: { vertical: 'bottom', horizontal: 'right' },\r\n            transformOrigin: { vertical: 'top', horizontal: 'right' },\r\n            PaperProps: {\r\n              sx: { maxHeight: 300, minWidth: 250 }\r\n            }\r\n          }}\r\n          sx={{ display: 'none' }}\r\n        >\r\n          {/* Sticky search bar at the top */}\r\n          <Box sx={{\r\n            position: 'sticky',\r\n            top: 0,\r\n            zIndex: 1000,\r\n            backgroundColor: 'background.paper',\r\n            p: 1,\r\n            borderBottom: '1px solid #e0e0e0',\r\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\r\n          }}>\r\n            <TextField\r\n              size=\"small\"\r\n              placeholder={translate('Search languages...')}\r\n              value={searchQuery}\r\n              onChange={(e) => setSearchQuery(e.target.value)}\r\n              onClick={(e) => e.stopPropagation()}\r\n              onKeyDown={(e) => e.stopPropagation()}\r\n              fullWidth\r\n              InputProps={{\r\n                startAdornment: (\r\n                  <InputAdornment position=\"start\">\r\n                    <SearchIcon fontSize=\"small\" />\r\n                  </InputAdornment>\r\n                ),\r\n              }}\r\n              sx={{\r\n                '& .MuiOutlinedInput-root': {\r\n                  '& fieldset': {\r\n                    borderColor: '#e0e0e0',\r\n                  },\r\n                  '&:hover fieldset': {\r\n                    borderColor: '#b0b0b0',\r\n                  },\r\n                  '&.Mui-focused fieldset': {\r\n                    borderColor: 'primary.main',\r\n                  },\r\n                },\r\n              }}\r\n            />\r\n          </Box>\r\n\r\n          {filteredLanguages.length === 0 ? (\r\n            <MenuItem disabled>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                {translate('No languages found')}\r\n              </Typography>\r\n            </MenuItem>\r\n          ) : (\r\n            filteredLanguages.map((lang) => (\r\n              <MenuItem\r\n                key={lang.LanguageId}\r\n                value={lang.LanguageCode}\r\n                onClick={() => handleMenuItemClick(lang.LanguageCode)}\r\n                selected={lang.LanguageCode === validCurrentLanguage}\r\n              >\r\n                <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                  <Typography variant=\"body2\">\r\n                    {getLanguageDisplayName(lang.LanguageCode, lang.Language)}\r\n                  </Typography>\r\n                </Box>\r\n              </MenuItem>\r\n            ))\r\n          )}\r\n        </Select>\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <FormControl size={size} className={className}>\r\n      {showLabel && (\r\n        <Typography variant=\"caption\" sx={{ mb: 0.5 }}>\r\n          {translate('Language')}\r\n        </Typography>\r\n      )}\r\n      <Select\r\n        value={validCurrentLanguage}\r\n        onChange={handleLanguageChange}\r\n        onClose={() => setSearchQuery('')}\r\n        disabled={isLoading || localLoading}\r\n        MenuProps={{\r\n          PaperProps: {\r\n            sx: { maxHeight: 300, minWidth: 250 }\r\n          }\r\n        }}\r\n        sx={{\r\n          minWidth: 120,\r\n          '& .MuiSelect-select': {\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: 1,\r\n          },\r\n        }}\r\n      >\r\n        {/* Sticky search bar at the top */}\r\n        <Box sx={{\r\n          position: 'sticky',\r\n          top: 0,\r\n          zIndex: 1000,\r\n          backgroundColor: 'background.paper',\r\n          p: 1,\r\n          borderBottom: '1px solid #e0e0e0',\r\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\r\n        }}>\r\n          <TextField\r\n            size=\"small\"\r\n            placeholder={translate('Search languages...')}\r\n            value={searchQuery}\r\n            onChange={(e) => setSearchQuery(e.target.value)}\r\n            onClick={(e) => e.stopPropagation()}\r\n            onKeyDown={(e) => e.stopPropagation()}\r\n            fullWidth\r\n            InputProps={{\r\n              startAdornment: (\r\n                <InputAdornment position=\"start\">\r\n                  <SearchIcon fontSize=\"small\" />\r\n                </InputAdornment>\r\n              ),\r\n            }}\r\n            sx={{\r\n              '& .MuiOutlinedInput-root': {\r\n                '& fieldset': {\r\n                  borderColor: '#e0e0e0',\r\n                },\r\n                '&:hover fieldset': {\r\n                  borderColor: '#b0b0b0',\r\n                },\r\n                '&.Mui-focused fieldset': {\r\n                  borderColor: 'primary.main',\r\n                },\r\n              },\r\n            }}\r\n          />\r\n        </Box>\r\n\r\n        {filteredLanguages.length === 0 ? (\r\n          <MenuItem disabled>\r\n            <Typography variant=\"body2\" color=\"text.secondary\">\r\n              {translate('No languages found')}\r\n            </Typography>\r\n          </MenuItem>\r\n        ) : (\r\n          filteredLanguages.map((lang) => (\r\n            <MenuItem key={lang.LanguageId} value={lang.LanguageCode}>\r\n              <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                <Typography variant=\"body2\">\r\n                  {getLanguageDisplayName(lang.LanguageCode, lang.Language)}\r\n                </Typography>\r\n              </Box>\r\n            </MenuItem>\r\n          ))\r\n        )}\r\n      </Select>\r\n    </FormControl>\r\n  );\r\n};\r\n\r\nexport default LanguageSelector;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SACEC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,GAAG,EACHC,UAAU,EAEVC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,cAAc,QACT,eAAe;AACtB,SAASC,QAAQ,IAAIC,YAAY,EAAEC,MAAM,IAAIC,UAAU,QAAQ,qBAAqB;AACpF,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,qBAAqB,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAU1E,MAAMC,gBAAiD,GAAGA,CAAC;EACzDC,OAAO,GAAG,QAAQ;EAClBC,IAAI,GAAG,OAAO;EACdC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,iBAAA;EACJ,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGf,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEgB,kBAAkB;IAAEC,eAAe;IAAEC,cAAc;IAAEC,SAAS;IAAEC;EAAc,CAAC,GAAGlB,qBAAqB,CAAC,CAAC;EACjH,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAElD,MAAM0C,KAAK,GAAG1B,YAAY,CAAE2B,KAAK;IAAA,IAAAC,iBAAA;IAAA,QAAAA,iBAAA,GAAKD,KAAK,CAACE,UAAU,cAAAD,iBAAA,uBAAhBA,iBAAA,CAAkBE,cAAc;EAAA,EAAC;;EAEvE;EACA,MAAMC,sBAAsB,GAAGA,CAACC,QAAgB,EAAEC,YAAoB,KAAK;IACzE,IAAI;MACF,MAAMC,YAAY,GAAG,IAAIC,IAAI,CAACC,YAAY,CAAC,CAACJ,QAAQ,CAAC,EAAE;QAAEK,IAAI,EAAE;MAAW,CAAC,CAAC;MAC5E,OAAOH,YAAY,CAACI,EAAE,CAACN,QAAQ,CAAC,IAAIC,YAAY;IAClD,CAAC,CAAC,OAAOM,KAAK,EAAE;MACd,OAAON,YAAY;IACrB;EACF,CAAC;;EAED;EACA,MAAMO,eAAe,GAAGvD,OAAO,CAAC,MAAM;IACpC,OAAO,CAAC,GAAG8B,kBAAkB,CAAC,CAAC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC5C,MAAMC,KAAK,GAAGb,sBAAsB,CAACW,CAAC,CAACG,YAAY,EAAEH,CAAC,CAAC/C,QAAQ,CAAC;MAChE,MAAMmD,KAAK,GAAGf,sBAAsB,CAACY,CAAC,CAACE,YAAY,EAAEF,CAAC,CAAChD,QAAQ,CAAC;MAChE,OAAOiD,KAAK,CAACG,aAAa,CAACD,KAAK,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC/B,kBAAkB,CAAC,CAAC;;EAExB;EACA,MAAMiC,iBAAiB,GAAG/D,OAAO,CAAC,MAAM;IACtC,IAAI,CAACuC,WAAW,CAACyB,IAAI,CAAC,CAAC,EAAE;MACvB,OAAOT,eAAe;IACxB;IACA,OAAOA,eAAe,CAACU,MAAM,CAACC,IAAI,IAAI;MACpC,MAAMC,UAAU,GAAGrB,sBAAsB,CAACoB,IAAI,CAACN,YAAY,EAAEM,IAAI,CAACxD,QAAQ,CAAC;MAC3E,OAAOyD,UAAU,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9B,WAAW,CAAC6B,WAAW,CAAC,CAAC,CAAC,IACjEF,IAAI,CAACxD,QAAQ,CAAC0D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9B,WAAW,CAAC6B,WAAW,CAAC,CAAC,CAAC,IAC/DF,IAAI,CAACN,YAAY,CAACQ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9B,WAAW,CAAC6B,WAAW,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC;EACJ,CAAC,EAAE,CAACb,eAAe,EAAEhB,WAAW,CAAC,CAAC;;EAElC;EACA,IAAI,CAACL,aAAa,IAAIJ,kBAAkB,CAACwC,MAAM,KAAK,CAAC,EAAE;IACrD,OAAO,IAAI;EACb;;EAEA;EACA,MAAMC,oBAAoB,GAAGhB,eAAe,CAACiB,IAAI,CAC/CN,IAAI,IAAIA,IAAI,CAACN,YAAY,CAACQ,WAAW,CAAC,CAAC,KAAKrC,eAAe,CAACqC,WAAW,CAAC,CAC1E,CAAC,GAAGrC,eAAe,GAAG,EAAAJ,iBAAA,GAAA4B,eAAe,CAAC,CAAC,CAAC,cAAA5B,iBAAA,uBAAlBA,iBAAA,CAAoBiC,YAAY,KAAI,IAAI;EAE9D,MAAMa,oBAAoB,GAAG,MAAOC,KAAgC,IAAK;IAEvE,MAAMC,eAAe,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IAC1C,IAAIF,eAAe,KAAKJ,oBAAoB,EAAE;IAE9CjC,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMN,cAAc,CAAC2C,eAAe,CAAC;MACrC;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRhB,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMyC,eAAe,GAAIL,KAAoC,IAAK;IAChEtC,WAAW,CAACsC,KAAK,CAACM,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB7C,WAAW,CAAC,IAAI,CAAC;IACjBI,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;EACtB,CAAC;EAED,MAAM0C,mBAAmB,GAAG,MAAOC,YAAoB,IAAK;IAC1D,IAAIA,YAAY,KAAKZ,oBAAoB,EAAE;MACzCU,WAAW,CAAC,CAAC;MACb;IACF;IAEA3C,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMN,cAAc,CAACmD,YAAY,CAAC;MAClC;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdwB,OAAO,CAACxB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACRhB,eAAe,CAAC,KAAK,CAAC;MACtB2C,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,IAAI3D,OAAO,KAAK,MAAM,EAAE;IACtB,oBACEJ,OAAA,CAAAE,SAAA;MAAAgE,QAAA,gBACElE,OAAA,CAACZ,OAAO;QAAC+E,KAAK,EAAExD,SAAS,CAAC,iBAAiB,CAAE;QAAAuD,QAAA,eAC3ClE,OAAA,CAACX,UAAU;UACT+E,OAAO,EAAEP,eAAgB;UACzBxD,IAAI,EAAEA,IAAK;UACXE,SAAS,EAAEA,SAAU;UACrB8D,QAAQ,EAAEtD,SAAS,IAAII,YAAa;UAAA+C,QAAA,eAEpClE,OAAA,CAACP,YAAY;YAAC6E,EAAE,EAAE;cAACC,MAAM,EAAE,MAAM;cAAGC,KAAK,EAAG;YAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACV5E,OAAA,CAACjB,MAAM;QACL8F,IAAI,EAAEC,OAAO,CAAC7D,QAAQ,CAAE;QACxB8D,OAAO,EAAEhB,WAAY;QACrBJ,KAAK,EAAEN,oBAAqB;QAC5B2B,SAAS,EAAE;UACT/D,QAAQ;UACR4D,IAAI,EAAEC,OAAO,CAAC7D,QAAQ,CAAC;UACvB8D,OAAO,EAAEhB,WAAW;UACpBkB,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDE,UAAU,EAAE;YACVf,EAAE,EAAE;cAAEgB,SAAS,EAAE,GAAG;cAAEC,QAAQ,EAAE;YAAI;UACtC;QACF,CAAE;QACFjB,EAAE,EAAE;UAAEkB,OAAO,EAAE;QAAO,CAAE;QAAAtB,QAAA,gBAGxBlE,OAAA,CAACd,GAAG;UAACoF,EAAE,EAAE;YACPmB,QAAQ,EAAE,QAAQ;YAClBC,GAAG,EAAE,CAAC;YACNC,MAAM,EAAE,IAAI;YACZC,eAAe,EAAE,kBAAkB;YACnCC,CAAC,EAAE,CAAC;YACJC,YAAY,EAAE,mBAAmB;YACjCC,SAAS,EAAE;UACb,CAAE;UAAA7B,QAAA,eACAlE,OAAA,CAACV,SAAS;YACRe,IAAI,EAAC,OAAO;YACZ2F,WAAW,EAAErF,SAAS,CAAC,qBAAqB,CAAE;YAC9CgD,KAAK,EAAEtC,WAAY;YACnB4E,QAAQ,EAAGC,CAAC,IAAK5E,cAAc,CAAC4E,CAAC,CAACxC,MAAM,CAACC,KAAK,CAAE;YAChDS,OAAO,EAAG8B,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;YACpCC,SAAS,EAAGF,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;YACtCE,SAAS;YACTC,UAAU,EAAE;cACVC,cAAc,eACZvG,OAAA,CAACT,cAAc;gBAACkG,QAAQ,EAAC,OAAO;gBAAAvB,QAAA,eAC9BlE,OAAA,CAACL,UAAU;kBAAC6G,QAAQ,EAAC;gBAAO;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB;YAEpB,CAAE;YACFN,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1B,YAAY,EAAE;kBACZmC,WAAW,EAAE;gBACf,CAAC;gBACD,kBAAkB,EAAE;kBAClBA,WAAW,EAAE;gBACf,CAAC;gBACD,wBAAwB,EAAE;kBACxBA,WAAW,EAAE;gBACf;cACF;YACF;UAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL/B,iBAAiB,CAACO,MAAM,KAAK,CAAC,gBAC7BpD,OAAA,CAAChB,QAAQ;UAACqF,QAAQ;UAAAH,QAAA,eAChBlE,OAAA,CAACb,UAAU;YAACiB,OAAO,EAAC,OAAO;YAACsG,KAAK,EAAC,gBAAgB;YAAAxC,QAAA,EAC/CvD,SAAS,CAAC,oBAAoB;UAAC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,GAEX/B,iBAAiB,CAAC8D,GAAG,CAAE3D,IAAI,iBACzBhD,OAAA,CAAChB,QAAQ;UAEP2E,KAAK,EAAEX,IAAI,CAACN,YAAa;UACzB0B,OAAO,EAAEA,CAAA,KAAMJ,mBAAmB,CAAChB,IAAI,CAACN,YAAY,CAAE;UACtDkE,QAAQ,EAAE5D,IAAI,CAACN,YAAY,KAAKW,oBAAqB;UAAAa,QAAA,eAErDlE,OAAA,CAACd,GAAG;YAACsG,OAAO,EAAC,MAAM;YAACqB,UAAU,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAAA5C,QAAA,eAC7ClE,OAAA,CAACb,UAAU;cAACiB,OAAO,EAAC,OAAO;cAAA8D,QAAA,EACxBtC,sBAAsB,CAACoB,IAAI,CAACN,YAAY,EAAEM,IAAI,CAACxD,QAAQ;YAAC;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GATD5B,IAAI,CAAC+D,UAAU;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUZ,CACX,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA,eACT,CAAC;EAEP;EAEA,oBACE5E,OAAA,CAACf,WAAW;IAACoB,IAAI,EAAEA,IAAK;IAACE,SAAS,EAAEA,SAAU;IAAA2D,QAAA,GAC3C5D,SAAS,iBACRN,OAAA,CAACb,UAAU;MAACiB,OAAO,EAAC,SAAS;MAACkE,EAAE,EAAE;QAAE0C,EAAE,EAAE;MAAI,CAAE;MAAA9C,QAAA,EAC3CvD,SAAS,CAAC,UAAU;IAAC;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACb,eACD5E,OAAA,CAACjB,MAAM;MACL4E,KAAK,EAAEN,oBAAqB;MAC5B4C,QAAQ,EAAE1C,oBAAqB;MAC/BwB,OAAO,EAAEA,CAAA,KAAMzD,cAAc,CAAC,EAAE,CAAE;MAClC+C,QAAQ,EAAEtD,SAAS,IAAII,YAAa;MACpC6D,SAAS,EAAE;QACTK,UAAU,EAAE;UACVf,EAAE,EAAE;YAAEgB,SAAS,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAI;QACtC;MACF,CAAE;MACFjB,EAAE,EAAE;QACFiB,QAAQ,EAAE,GAAG;QACb,qBAAqB,EAAE;UACrBC,OAAO,EAAE,MAAM;UACfqB,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE;QACP;MACF,CAAE;MAAA5C,QAAA,gBAGFlE,OAAA,CAACd,GAAG;QAACoF,EAAE,EAAE;UACPmB,QAAQ,EAAE,QAAQ;UAClBC,GAAG,EAAE,CAAC;UACNC,MAAM,EAAE,IAAI;UACZC,eAAe,EAAE,kBAAkB;UACnCC,CAAC,EAAE,CAAC;UACJC,YAAY,EAAE,mBAAmB;UACjCC,SAAS,EAAE;QACb,CAAE;QAAA7B,QAAA,eACAlE,OAAA,CAACV,SAAS;UACRe,IAAI,EAAC,OAAO;UACZ2F,WAAW,EAAErF,SAAS,CAAC,qBAAqB,CAAE;UAC9CgD,KAAK,EAAEtC,WAAY;UACnB4E,QAAQ,EAAGC,CAAC,IAAK5E,cAAc,CAAC4E,CAAC,CAACxC,MAAM,CAACC,KAAK,CAAE;UAChDS,OAAO,EAAG8B,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;UACpCC,SAAS,EAAGF,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;UACtCE,SAAS;UACTC,UAAU,EAAE;YACVC,cAAc,eACZvG,OAAA,CAACT,cAAc;cAACkG,QAAQ,EAAC,OAAO;cAAAvB,QAAA,eAC9BlE,OAAA,CAACL,UAAU;gBAAC6G,QAAQ,EAAC;cAAO;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAEpB,CAAE;UACFN,EAAE,EAAE;YACF,0BAA0B,EAAE;cAC1B,YAAY,EAAE;gBACZmC,WAAW,EAAE;cACf,CAAC;cACD,kBAAkB,EAAE;gBAClBA,WAAW,EAAE;cACf,CAAC;cACD,wBAAwB,EAAE;gBACxBA,WAAW,EAAE;cACf;YACF;UACF;QAAE;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAEL/B,iBAAiB,CAACO,MAAM,KAAK,CAAC,gBAC7BpD,OAAA,CAAChB,QAAQ;QAACqF,QAAQ;QAAAH,QAAA,eAChBlE,OAAA,CAACb,UAAU;UAACiB,OAAO,EAAC,OAAO;UAACsG,KAAK,EAAC,gBAAgB;UAAAxC,QAAA,EAC/CvD,SAAS,CAAC,oBAAoB;QAAC;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,GAEX/B,iBAAiB,CAAC8D,GAAG,CAAE3D,IAAI,iBACzBhD,OAAA,CAAChB,QAAQ;QAAuB2E,KAAK,EAAEX,IAAI,CAACN,YAAa;QAAAwB,QAAA,eACvDlE,OAAA,CAACd,GAAG;UAACsG,OAAO,EAAC,MAAM;UAACqB,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAA5C,QAAA,eAC7ClE,OAAA,CAACb,UAAU;YAACiB,OAAO,EAAC,OAAO;YAAA8D,QAAA,EACxBtC,sBAAsB,CAACoB,IAAI,CAACN,YAAY,EAAEM,IAAI,CAACxD,QAAQ;UAAC;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GALO5B,IAAI,CAAC+D,UAAU;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMpB,CACX,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAElB,CAAC;AAACpE,EAAA,CA3RIL,gBAAiD;EAAA,QAM5BP,cAAc,EACmDE,qBAAqB,EAKjGD,YAAY;AAAA;AAAAoH,EAAA,GAZtB9G,gBAAiD;AA6RvD,eAAeA,gBAAgB;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}