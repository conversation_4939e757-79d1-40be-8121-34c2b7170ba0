{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\PopupSections\\\\Button.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Button, Popover, Typography, IconButton, Tooltip } from \"@mui/material\";\nimport { ChromePicker } from \"react-color\";\nimport { deleteicon, copyicon, settingsicon } from \"../../../assets/icons/icons\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ButtonSection = ({\n  buttonColor,\n  setButtonColor,\n  isBanner,\n  isCloneDisabled,\n  onDelete,\n  onClone\n}) => {\n  _s();\n  var _buttonsContainer$fin, _buttonsContainer$fin2;\n  const {\n    buttons<PERSON>ontainer,\n    cloneButtonContainer,\n    updateButton,\n    addNewButton,\n    deleteButton,\n    deleteButtonContainer,\n    updateContainer,\n    setSettingAnchorEl,\n    selectedTemplate,\n    selectedTemplateTour,\n    setSelectedTemplate,\n    buttonProperty,\n    setButtonProperty,\n    btnBgColor,\n    btnBorderColor,\n    btnTextColor,\n    setButtonId,\n    setCuntainerId,\n    createWithAI,\n    currentStep,\n    ensureAnnouncementButtonContainer,\n    closeAllButtonPopups\n  } = useDrawerStore(state => state);\n  const {\n    t: translate\n  } = useTranslation();\n  const [isEditingPrevious, setIsEditingPrevious] = useState(false);\n  const [isEditingContinue, setIsEditingContinue] = useState(false);\n  const [previousButtonText, setPreviousButtonText] = useState(\"Previous\");\n  const [continueButtonText, setContinueButtonText] = useState(\"Continue\");\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [selectedColor, setSelectedColor] = useState(\"#313030\");\n  const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState(null);\n  const [buttonText, setButtonText] = useState(\"Continue\");\n  const [buttonToEdit, setButtonToEdit] = useState(null);\n  const [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\n  const [currentContainerId, setCurrentContainerId] = useState(\"\");\n  const [currentButtonId, setCurrentButtonId] = useState(\"\");\n  const [isEditingButton, setIsEditingButton] = useState(false);\n  const [isEditing, setIsEditing] = useState(null);\n\n  // Default button color\n  let clickTimeout;\n  useEffect(() => {\n    setAnchorEl(null);\n    setButtonProperty(false);\n  }, []); // Empty dependency array ensures it runs only once\n\n  // Listen for global close all button popups trigger\n  useEffect(() => {\n    if (closeAllButtonPopups > 0) {\n      setAnchorEl(null);\n      setColorPickerAnchorEl(null);\n      setButtonToEdit(null);\n    }\n  }, [closeAllButtonPopups]);\n  const handleClick = (event, buttonId) => {\n    const target = event.currentTarget;\n    clickTimeout = setTimeout(() => {\n      setAnchorEl(target);\n      setCurrentButtonId(buttonId);\n      setIsEditingButton(false);\n      handleEditButtonName(currentContainerId, buttonId, \"isEditing\", false);\n    }, 200);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n    setSettingAnchorEl({\n      containerId: \"\",\n      buttonId: \"\",\n      value: null\n    });\n    setButtonToEdit(null);\n  };\n  const handlePreviousTextChange = event => {\n    setPreviousButtonText(event.target.value);\n  };\n  const handleContinueTextChange = event => {\n    setContinueButtonText(event.target.value);\n  };\n  const handleBackgroundColorClick = event => {\n    setColorPickerAnchorEl(event.currentTarget);\n  };\n  const handleColorChange = color => {\n    setSelectedColor(color.hex);\n    // Update the backgroundColor in the container's style\n    updateContainer(currentContainerId, \"style\", {\n      backgroundColor: color.hex\n    });\n\n    // Also update the BackgroundColor property at the ButtonSection level\n    updateContainer(currentContainerId, \"BackgroundColor\", color.hex);\n  };\n  const handleCloseColorPicker = () => {\n    setColorPickerAnchorEl(null);\n  };\n  const open = Boolean(anchorEl);\n  // const open = Boolean(anchorEl && !isEditingButton);\n  const id = open ? \"button-popover\" : undefined;\n  const colorPickerOpen = Boolean(colorPickerAnchorEl);\n  const toggleEdit = button => {\n    if (button === \"Previous\") {\n      setIsEditingPrevious(true);\n    } else if (button === \"Continue\") {\n      setIsEditingContinue(true);\n    }\n  };\n  const handlePreviousBlur = () => {\n    setIsEditingPrevious(false);\n  };\n  const handleContinueBlur = () => {\n    setIsEditingContinue(false);\n  };\n  const handleEditButtonName = (containerId, buttonId, isEditing, value) => {\n    clearTimeout(clickTimeout);\n    setIsEditingButton(true);\n    updateButton(containerId, buttonId, isEditing, value);\n  };\n  const handleChangeButton = (containerId, buttonId, value) => {\n    updateButton(containerId, buttonId, \"type\", value);\n  };\n  const handleEditButtonText = (containerId, buttonId, newText) => {\n    updateButton(containerId, buttonId, \"name\", newText);\n    setButtonToEdit(null); // Exit edit mode after saving\n  };\n  //   const [buttonCount, setButtonCount] = useState(0);\n  const handleAddIconClick = containerId => {\n    //  const buttonName = buttonCount === 0 ? \"Got It\" : `Button${buttonCount + 1}`;\n    addNewButton({\n      id: crypto.randomUUID(),\n      name: \"Button 1\",\n      position: \"center\",\n      type: \"primary\",\n      isEditing: false,\n      index: 0,\n      style: {\n        backgroundColor: \"#5F9EA0\"\n      }\n    }, containerId);\n    // setButtonCount(buttonCount + 1);\n  };\n\n  // shouldShowAddBtn will be calculated per section inside the map\n\n  const currentContainerColor = ((_buttonsContainer$fin = buttonsContainer.find(item => item.id === currentContainerId)) === null || _buttonsContainer$fin === void 0 ? void 0 : (_buttonsContainer$fin2 = _buttonsContainer$fin.style) === null || _buttonsContainer$fin2 === void 0 ? void 0 : _buttonsContainer$fin2.backgroundColor) || \"#5f9ea0\";\n  const handleDelteContainer = () => {\n    deleteButtonContainer(currentContainerId);\n    setAnchorEl(null);\n\n    // Call the onDelete callback if provided\n    if (onDelete) {\n      onDelete();\n    }\n  };\n  setButtonId(currentButtonId);\n  setCuntainerId(currentButtonId);\n  const handleSettingIconClick = event => {\n    if (selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\") {\n      setSettingAnchorEl({\n        containerId: currentContainerId,\n        buttonId: currentButtonId,\n        // @ts-ignore\n        value: event.currentTarget\n      });\n      setButtonProperty(true);\n    } else {\n      setSettingAnchorEl({\n        containerId: currentContainerId,\n        buttonId: currentButtonId,\n        // @ts-ignore\n        value: event.currentTarget\n      });\n    }\n    setAnchorEl(null);\n    setButtonToEdit(null);\n\n    //setAnchorEl(null);\n  };\n\n  // Determine which containers to use based on whether this is an AI announcement\n  const isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\n  const isTourAnnouncement = createWithAI && selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\";\n  const currentStepIndex = currentStep - 1;\n  let containersToRender = [];\n  if (isAIAnnouncement) {\n    // Use the store function to ensure Button containers exist\n    containersToRender = ensureAnnouncementButtonContainer(currentStepIndex, isTourAnnouncement);\n  } else {\n    // For banners and non-AI content, use buttonsContainer\n    containersToRender = buttonsContainer;\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [containersToRender.map(buttonItem => {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        component: \"div\",\n        id: buttonItem.id\n        // className={isBanner ? \"qadpt-banner-btn\" : \"\"}\n        ,\n        sx: {\n          height: isBanner ? \"40px !important\" : \"60px !important\",\n          width: \"100%\",\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"8px\",\n          padding: \"0 16px\",\n          boxSizing: \"border-box\",\n          backgroundColor: buttonItem.style.backgroundColor ? buttonItem.style.backgroundColor : btnBgColor,\n          justifyContent: \"center\"\n          // \"&.qadpt-banner-btn\": { height: \"40px\" },\n        },\n        onMouseEnter: e => setCurrentContainerId(e.currentTarget.id),\n        children: [buttonItem.buttons.map(item => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: \"relative\",\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            \"&:hover .delete-icon\": {\n              // Add this hover effect to display the delete icon when the button is hovered\n              opacity: 1\n            }\n          },\n          onMouseEnter: () => setCurrentContainerId(buttonItem.id),\n          onMouseLeave: () => setIsDeleteIcon(\"\"),\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            id: item.id,\n            variant: \"contained\",\n            sx: {\n              lineHeight: \"var(--button-lineheight)\",\n              padding: \"var(--button-padding)\",\n              borderRadius: \"8px\",\n              color: item.style.color || \"#fff\",\n              border: item.style.borderColor ? `2px solid ${item.style.borderColor}` : \"none\",\n              textTransform: \"none\",\n              backgroundColor: item.style.backgroundColor,\n              boxShadow: \"none\",\n              \"&:hover\": {\n                boxShadow: \"none !important\"\n              }\n            },\n            onClick: e => handleClick(e, item.id),\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                color: btnTextColor\n              },\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 10\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 9\n          }, this), buttonItem.buttons.length > 1 && /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            className: \"delete-icon\",\n            sx: {\n              position: \"absolute\",\n              right: \"-10px\",\n              top: \"0\",\n              transform: \"translateY(-50%)\",\n              backgroundColor: \"#fff\",\n              boxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\n              opacity: 0,\n              // Initially hidden\n              transition: \"opacity 0.3s ease\",\n              // Smooth transition\n              zIndex: 1,\n              padding: \"3px !important\",\n              \"&:hover\": {\n                backgroundColor: \"#fff\",\n                boxShadow: \"none !important\"\n              },\n              span: {\n                height: \"16px\"\n              },\n              \"& svg\": {\n                width: \"14px\",\n                // Set the width of the SVG\n                height: \"14px\",\n                // Set the height of the SVG\n                path: {\n                  fill: \"#ff0000\"\n                }\n              }\n            },\n            onClick: () => deleteButton(buttonItem.id, item.id),\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 10\n          }, this)]\n        }, item.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 8\n        }, this)), (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && buttonItem.buttons.length < 4 ? /*#__PURE__*/_jsxDEV(IconButton, {\n          sx: {\n            backgroundColor: \"#5F9EA0\",\n            cursor: \"pointer\",\n            zIndex: 1000,\n            padding: \"6px !important\",\n            \"&:hover\": {\n              backgroundColor: \"#70afaf\"\n            }\n          }\n          // sx={sideAddButtonStyle}\n          ,\n          onClick: () => {\n            handleAddIconClick(buttonItem.id);\n            if (onClone) {\n              onClone();\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(AddIcon, {\n            fontSize: \"small\",\n            sx: {\n              color: \"#fff\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 8\n        }, this) : null]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 6\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(Popover, {\n      className: \"qadpt-bunprop\",\n      id: id,\n      open: open,\n      anchorEl: anchorEl,\n      onClose: handleClose,\n      anchorOrigin: {\n        vertical: \"top\",\n        horizontal: \"left\"\n      },\n      transformOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"left\"\n      },\n      sx: {\n        marginTop: isBanner ? \"100px !important\" : \"-5px\"\n      }\n      // className={isBanner ? \"qadpt-banner-btn\" : \"\"}\n      ,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"8px\",\n          padding: \"8px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Settings\"),\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleSettingIconClick,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: settingsicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 6\n        }, this), (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Background Color\"),\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleBackgroundColorClick,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                backgroundColor: selectedColor,\n                borderRadius: \"100%\",\n                width: \"20px\",\n                height: \"20px\",\n                display: \"inline-block\",\n                marginTop: \"-3px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 7\n        }, this), (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: isCloneDisabled ? translate(\"Maximum limit of 3 Button sections reached\") : translate(\"Clone Button\"),\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: () => {\n              cloneButtonContainer(currentContainerId);\n              if (onClone) {\n                onClone();\n              }\n            },\n            disabled: isCloneDisabled,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: copyicon\n              },\n              style: {\n                opacity: isCloneDisabled ? 0.5 : 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 7\n        }, this), (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: translate(\"Delete Button\"),\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleDelteContainer,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              },\n              style: {\n                marginTop: \"-3px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Popover, {\n      open: colorPickerOpen,\n      anchorEl: colorPickerAnchorEl,\n      onClose: handleCloseColorPicker,\n      anchorOrigin: {\n        vertical: \"bottom\",\n        horizontal: \"center\"\n      },\n      transformOrigin: {\n        vertical: \"top\",\n        horizontal: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(ChromePicker, {\n          color: currentContainerColor,\n          onChange: handleColorChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n          children: `\n      .chrome-picker input {\n        padding: 0 !important;\n      }\n    `\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(ButtonSection, \"CMGIEoKUqzG4LnPKS7RxBdITGsk=\", false, function () {\n  return [useDrawerStore, useTranslation];\n});\n_c = ButtonSection;\nexport default ButtonSection;\nvar _c;\n$RefreshReg$(_c, \"ButtonSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "<PERSON><PERSON>", "Popover", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "ChromePicker", "deleteicon", "copyicon", "settingsicon", "useDrawerStore", "AddIcon", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ButtonSection", "buttonColor", "setButtonColor", "isBanner", "isCloneDisabled", "onDelete", "onClone", "_s", "_buttonsContainer$fin", "_buttonsContainer$fin2", "buttonsContainer", "cloneButtonContainer", "updateButton", "addNewButton", "deleteButton", "deleteButtonContainer", "updateContainer", "setSettingAnchorEl", "selectedTemplate", "selectedTemplateTour", "setSelectedTemplate", "buttonProperty", "setButtonProperty", "btnBgColor", "btnBorderColor", "btnTextColor", "setButtonId", "setCuntainerId", "createWithAI", "currentStep", "ensureAnnouncementButtonContainer", "closeAllButtonPopups", "state", "t", "translate", "isEditingPrevious", "setIsEditingPrevious", "isEditingContinue", "setIsEditingContinue", "previousButtonText", "setPreviousButtonText", "continueButtonText", "setContinueButtonText", "anchorEl", "setAnchorEl", "selectedColor", "setSelectedColor", "colorPickerAnchorEl", "setColorPickerAnchorEl", "buttonText", "setButtonText", "buttonToEdit", "setButtonToEdit", "isDeleteIcon", "setIsDeleteIcon", "currentContainerId", "setCurrentContainerId", "currentButtonId", "setCurrentButtonId", "isEditingButton", "setIsEditingButton", "isEditing", "setIsEditing", "clickTimeout", "handleClick", "event", "buttonId", "target", "currentTarget", "setTimeout", "handleEditButtonName", "handleClose", "containerId", "value", "handlePreviousTextChange", "handleContinueTextChange", "handleBackgroundColorClick", "handleColorChange", "color", "hex", "backgroundColor", "handleCloseColorPicker", "open", "Boolean", "id", "undefined", "colorPickerOpen", "toggleEdit", "button", "handlePreviousBlur", "handleContinueBlur", "clearTimeout", "handleChangeButton", "handleEditButtonText", "newText", "handleAddIconClick", "crypto", "randomUUID", "name", "position", "type", "index", "style", "currentContainerColor", "find", "item", "handleDelteContainer", "handleSettingIconClick", "isAIAnnouncement", "isTourAnnouncement", "currentStepIndex", "containersToRender", "children", "map", "buttonItem", "component", "sx", "height", "width", "display", "alignItems", "gap", "padding", "boxSizing", "justifyContent", "onMouseEnter", "e", "buttons", "opacity", "onMouseLeave", "variant", "lineHeight", "borderRadius", "border", "borderColor", "textTransform", "boxShadow", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "size", "className", "right", "top", "transform", "transition", "zIndex", "span", "path", "fill", "dangerouslySetInnerHTML", "__html", "cursor", "fontSize", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "marginTop", "title", "disabled", "onChange", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/PopupSections/Button.tsx"], "sourcesContent": ["import React, { useState,useEffect } from \"react\";\r\nimport { Box, Button, Popover, Typography, Text<PERSON>ield, IconButton, Tooltip } from \"@mui/material\";\r\nimport { ChromePicker, ColorResult } from \"react-color\";\r\nimport { deleteicon, copyicon, settingsicon, backgroundcoloricon, editicon } from \"../../../assets/icons/icons\";\r\nimport useDrawerStore, { TButton } from \"../../../store/drawerStore\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ButtonSettings from \"../../guideBanners/selectedpopupfields/ImageProperties\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ButtonSection: React.FC<{ buttonColor: string; setButtonColor: (str: string) => void; isBanner: boolean; index?: number; isCloneDisabled?: boolean; onDelete?: () => void; onClone?: () => void;}> = ({\r\n\tbuttonColor,\r\n\tsetButtonColor,\r\n\tisBanner,\r\n\tisCloneDisabled,\r\n\tonDelete,\r\n\tonClone\r\n}) => {\r\n\tconst {\r\n\t\tbuttonsContainer,\r\n\t\tcloneButtonContainer,\r\n\t\tupdateButton,\r\n\t\taddNewButton,\r\n\t\tdeleteButton,\r\n\t\tdeleteButtonContainer,\r\n\t\tupdateContainer,\r\n\t\tsetSettingAnchorEl,\r\n\t\tselectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\tsetSelectedTemplate,\r\n\t\tbuttonProperty,\r\n\t\tsetButtonProperty,\r\n\t\tbtnBgColor,\r\n\t\tbtnBorderColor,\r\n\t\tbtnTextColor,\r\n\t\tsetButtonId,\r\n\t\tsetCuntainerId,\r\n\t\tcreateWithAI,\r\n\t\tcurrentStep,\r\n\t\tensureAnnouncementButtonContainer,\r\n\t\tcloseAllButtonPopups,\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isEditingPrevious, setIsEditingPrevious] = useState<boolean>(false);\r\n\tconst [isEditingContinue, setIsEditingContinue] = useState<boolean>(false);\r\n\tconst [previousButtonText, setPreviousButtonText] = useState<string>(\"Previous\");\r\n\tconst [continueButtonText, setContinueButtonText] = useState<string>(\"Continue\");\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [selectedColor, setSelectedColor] = useState<string>(\"#313030\");\r\n\tconst [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [buttonText, setButtonText] = useState<string>(\"Continue\");\r\n\tconst [buttonToEdit, setButtonToEdit] = useState<string | null>(null);\r\n\tconst [isDeleteIcon, setIsDeleteIcon] = useState(\"\");\r\n\tconst [currentContainerId, setCurrentContainerId] = useState(\"\");\r\n\tconst [currentButtonId, setCurrentButtonId] = useState(\"\");\r\n\tconst [isEditingButton, setIsEditingButton] = useState(false);\r\n\tconst [isEditing, setIsEditing] = useState<string | null>(null);\r\n\r\n\t// Default button color\r\n\tlet clickTimeout: NodeJS.Timeout;\r\n\r\n\tuseEffect(() => {\r\n\t\tsetAnchorEl(null);\r\n\t\tsetButtonProperty(false);\r\n\t}, []); // Empty dependency array ensures it runs only once\r\n\r\n\t// Listen for global close all button popups trigger\r\n\tuseEffect(() => {\r\n\t\tif (closeAllButtonPopups > 0) {\r\n\t\t\tsetAnchorEl(null);\r\n\t\t\tsetColorPickerAnchorEl(null);\r\n\t\t\tsetButtonToEdit(null);\r\n\t\t}\r\n\t}, [closeAllButtonPopups]);\r\n\r\n\tconst handleClick = (event: React.MouseEvent<HTMLElement>, buttonId: string) => {\r\n\t\tconst target = event.currentTarget;\r\n\r\n\t\tclickTimeout = setTimeout(() => {\r\n\t\t\tsetAnchorEl(target);\r\n\t\t\tsetCurrentButtonId(buttonId);\r\n\t\t\tsetIsEditingButton(false);\r\n\t\t\thandleEditButtonName(currentContainerId, buttonId, \"isEditing\", false);\r\n\t\t}, 200);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t\tsetSettingAnchorEl({ containerId: \"\", buttonId: \"\", value: null });\r\n\t\tsetButtonToEdit(null);\r\n\t};\r\n\r\n\tconst handlePreviousTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tsetPreviousButtonText(event.target.value);\r\n\t};\r\n\r\n\tconst handleContinueTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tsetContinueButtonText(event.target.value);\r\n\t};\r\n\r\n\tconst handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetColorPickerAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleColorChange = (color: ColorResult) => {\r\n\t\tsetSelectedColor(color.hex);\r\n\t\t// Update the backgroundColor in the container's style\r\n\t\tupdateContainer(currentContainerId, \"style\", {\r\n\t\t\tbackgroundColor: color.hex,\r\n\t\t});\r\n\r\n\t\t// Also update the BackgroundColor property at the ButtonSection level\r\n\t\tupdateContainer(currentContainerId, \"BackgroundColor\", color.hex);\r\n\t};\r\n\r\n\tconst handleCloseColorPicker = () => {\r\n\t\tsetColorPickerAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(anchorEl);\r\n\t// const open = Boolean(anchorEl && !isEditingButton);\r\n\tconst id = open ? \"button-popover\" : undefined;\r\n\tconst colorPickerOpen = Boolean(colorPickerAnchorEl);\r\n\tconst toggleEdit = (button: \"Previous\" | \"Continue\") => {\r\n\t\tif (button === \"Previous\") {\r\n\t\t\tsetIsEditingPrevious(true);\r\n\t\t} else if (button === \"Continue\") {\r\n\t\t\tsetIsEditingContinue(true);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handlePreviousBlur = () => {\r\n\t\tsetIsEditingPrevious(false);\r\n\t};\r\n\r\n\tconst handleContinueBlur = () => {\r\n\t\tsetIsEditingContinue(false);\r\n\t};\r\n\tconst handleEditButtonName = (\r\n\t\tcontainerId: string,\r\n\t\tbuttonId: string,\r\n\t\tisEditing: keyof TButton,\r\n\t\tvalue: TButton[keyof TButton]\r\n\t) => {\r\n\t\tclearTimeout(clickTimeout);\r\n\t\tsetIsEditingButton(true);\r\n\t\tupdateButton(containerId, buttonId, isEditing, value);\r\n\t};\r\n\r\n\tconst handleChangeButton = (containerId: string, buttonId: string, value: TButton[keyof TButton]) => {\r\n\t\tupdateButton(containerId, buttonId, \"type\", value);\r\n\t};\r\n\tconst handleEditButtonText = (containerId: string, buttonId: string, newText: string) => {\r\n\t\tupdateButton(containerId, buttonId, \"name\", newText);\r\n\t\tsetButtonToEdit(null); // Exit edit mode after saving\r\n\t};\r\n\t//   const [buttonCount, setButtonCount] = useState(0);\r\n\tconst handleAddIconClick = (containerId: string) => {\r\n\t\t//  const buttonName = buttonCount === 0 ? \"Got It\" : `Button${buttonCount + 1}`;\r\n\t\taddNewButton(\r\n\t\t\t{\r\n\t\t\t\tid: crypto.randomUUID(),\r\n\t\t\t\tname: \"Button 1\",\r\n\t\t\t\tposition: \"center\",\r\n\t\t\t\ttype: \"primary\",\r\n\t\t\t\tisEditing: false,\r\n\t\t\t\tindex: 0,\r\n\t\t\t\tstyle: {\r\n\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t},\r\n\t\t\t},\r\n\t\t\tcontainerId\r\n\t\t);\r\n\t\t// setButtonCount(buttonCount + 1);\r\n\t};\r\n\r\n\t// shouldShowAddBtn will be calculated per section inside the map\r\n\r\n\tconst currentContainerColor =\r\n\t\tbuttonsContainer.find((item: any) => item.id === currentContainerId)?.style?.backgroundColor || \"#5f9ea0\";\r\n\r\n\tconst handleDelteContainer = () => {\r\n\t\tdeleteButtonContainer(currentContainerId);\r\n\t\tsetAnchorEl(null);\r\n\r\n\t\t// Call the onDelete callback if provided\r\n\t\tif (onDelete) {\r\n\t\t\tonDelete();\r\n\t\t}\r\n\t};\r\n\tsetButtonId(currentButtonId);\r\n\tsetCuntainerId(currentButtonId);\r\n\r\n\tconst handleSettingIconClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tif (selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\") {\r\n\t\t\tsetSettingAnchorEl({\r\n\t\t\t\tcontainerId: currentContainerId,\r\n\t\t\t\tbuttonId: currentButtonId,\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: event.currentTarget,\r\n\t\t\t});\r\n\t\t\tsetButtonProperty(true);\r\n\t\t} else {\r\n\t\t\tsetSettingAnchorEl({\r\n\t\t\t\tcontainerId: currentContainerId,\r\n\t\t\t\tbuttonId: currentButtonId,\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: event.currentTarget,\r\n\t\t\t});\r\n\t\t}\r\n\t\tsetAnchorEl(null);\r\n\t\tsetButtonToEdit(null);\r\n\r\n\t\t//setAnchorEl(null);\r\n\t};\r\n\r\n\t// Determine which containers to use based on whether this is an AI announcement\r\n\tconst isAIAnnouncement = createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\");\r\n\tconst isTourAnnouncement = createWithAI && selectedTemplate === \"Tour\" && selectedTemplateTour === \"Announcement\";\r\n\tconst currentStepIndex = currentStep - 1;\r\n\r\n\tlet containersToRender: any[] = [];\r\n\r\n\tif (isAIAnnouncement) {\r\n\t\t// Use the store function to ensure Button containers exist\r\n\t\tcontainersToRender = ensureAnnouncementButtonContainer(currentStepIndex, isTourAnnouncement);\r\n\t} else {\r\n\t\t// For banners and non-AI content, use buttonsContainer\r\n\t\tcontainersToRender = buttonsContainer;\r\n\t}\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{containersToRender.map((buttonItem: any) => {\r\n\t\t\t\treturn (\r\n\t\t\t\t\t<Box\r\n\t\t\t\t\t\tcomponent=\"div\"\r\n\t\t\t\t\t\tid={buttonItem.id}\r\n\t\t\t\t\t\t// className={isBanner ? \"qadpt-banner-btn\" : \"\"}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\theight: isBanner ? \"40px !important\" : \"60px !important\",\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\tgap: \"8px\",\r\n\t\t\t\t\t\t\tpadding: \"0 16px\",\r\n\t\t\t\t\t\t\tboxSizing: \"border-box\",\r\n\t\t\t\t\t\t\tbackgroundColor: buttonItem.style.backgroundColor ? buttonItem.style.backgroundColor : btnBgColor,\r\n\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t// \"&.qadpt-banner-btn\": { height: \"40px\" },\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tonMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{buttonItem.buttons.map((item: any) => (\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover .delete-icon\": {\r\n\t\t\t\t\t\t\t\t\t\t// Add this hover effect to display the delete icon when the button is hovered\r\n\t\t\t\t\t\t\t\t\t\topacity: 1,\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonMouseEnter={() => setCurrentContainerId(buttonItem.id)}\r\n\t\t\t\t\t\t\t\tonMouseLeave={() => setIsDeleteIcon(\"\")}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tid={item.id}\r\n\t\t\t\t\t\t\t\t\tvariant={\"contained\"}\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\tcolor: item.style.color || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\tborder: item.style.borderColor\r\n\t\t\t\t\t\t\t\t\t\t\t? `2px solid ${item.style.borderColor}`\r\n\t\t\t\t\t\t\t\t\t\t\t: \"none\",\r\n\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: item.style.backgroundColor,\r\n\t\t\t\t\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tonClick={(e) => handleClick(e, item.id)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography style={{ color: btnTextColor }}>{item.name}</Typography>\r\n\t\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t\t{/* Delete Icon - Right Side, only visible on hover */}\r\n\t\t\t\t\t\t\t\t{buttonItem.buttons.length > 1 && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"delete-icon\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"-10px\",\r\n\t\t\t\t\t\t\t\t\t\t\ttop: \"0\",\r\n\t\t\t\t\t\t\t\t\t\t\ttransform: \"translateY(-50%)\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.4) 0px 2px 6px\",\r\n\t\t\t\t\t\t\t\t\t\t\topacity: 0, // Initially hidden\r\n\t\t\t\t\t\t\t\t\t\t\ttransition: \"opacity 0.3s ease\", // Smooth transition\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: 1,\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"3px !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\tspan: {\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"16px\"\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\"& svg\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"14px\", // Set the width of the SVG\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"14px\", // Set the height of the SVG\r\n\t\t\t\t\t\t\t\t\t\t\t\tpath: {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tfill:\"#ff0000\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => deleteButton(buttonItem.id, item.id)}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t))}\r\n\r\n\t\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") &&\r\n\t\t\t\t\t\tbuttonItem.buttons.length < 4  ? (\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"#5F9EA0\",\r\n\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\tpadding: \"6px !important\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#70afaf\",\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t// sx={sideAddButtonStyle}\r\n\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\thandleAddIconClick(buttonItem.id);\r\n\t\t\t\t\t\t\t\t\tif (onClone) {\r\n\t\t\t\t\t\t\t\t\t\tonClone();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<AddIcon\r\n\t\t\t\t\t\t\t\t\tfontSize=\"small\"\r\n\t\t\t\t\t\t\t\t\tsx={{ color: \"#fff\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t) : null}\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t);\r\n\t\t\t})}\r\n\t\t\t<Popover\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-bunprop\"\r\n\t\t\t\tid={id}\r\n\t\t\t\topen={open}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tonClose={handleClose}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"left\",\r\n\t\t\t\t}}\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tmarginTop: isBanner ? \"100px !important\" : \"-5px\",\r\n\t\t\t\t}}\r\n\t\t\t\t// className={isBanner ? \"qadpt-banner-btn\" : \"\"}\r\n\t\t\t>\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tgap: \"8px\",\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t{/* <Tooltip\r\n\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\tPopperProps={{\r\n\t\t\t\t\tsx: {\r\n\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<span>  */}\r\n\t\t\t\t\t{/* <Typography\r\n\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\tsx={{ cursor: \"pointer\", fontWeight: \"bold\", opacity: 0.5 }}\r\n\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\tid=\"primary\"\r\n\t\t\t\t\t\tonClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tPrimary\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t\t{/* </span>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\ttitle=\"Coming Soon\"\r\n\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\tvariant=\"body2\"\r\n\t\t\t\t\t\t\t\tsx={{ cursor: \"pointer\", color: \"gray\", opacity: 0.5 }}\r\n\t\t\t\t\t\t\t\tcomponent={\"div\"}\r\n\t\t\t\t\t\t\t\tid=\"secondary\"\r\n\t\t\t\t\t\t\t\t//onClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\tSecondary\r\n\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t</Tooltip>\r\n\r\n\t\t\t\t\t<Box sx={{ borderLeft: \"1px solid #ccc\", height: \"24px\", marginLeft: \"8px\" }}></Box>\r\n\t\t\t\t\t*/}\r\n\r\n\t\t\t\t\t{/* Icons for additional options */}\r\n\t\t\t\t\t<Tooltip title={translate(\"Settings\")}>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tonClick={handleSettingIconClick}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: settingsicon }} />\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && (\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Background Color\")}>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={handleBackgroundColorClick}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: selectedColor,\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"inline-block\",\r\n\t\t\t\t\t\t\t\t\t\tmarginTop: \"-3px\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && (\r\n\t\t\t\t\t\t<Tooltip title={isCloneDisabled ? translate(\"Maximum limit of 3 Button sections reached\") : translate(\"Clone Button\")}>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\tcloneButtonContainer(currentContainerId);\r\n\t\t\t\t\t\t\t\t\tif (onClone) {\r\n\t\t\t\t\t\t\t\t\t\tonClone();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tdisabled={isCloneDisabled}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: copyicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ opacity: isCloneDisabled ? 0.5 : 1 }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{(selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\") && (\r\n\t\t\t\t\t\t<Tooltip title={translate(\"Delete Button\")}>\r\n\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\tonClick={handleDelteContainer}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: deleteicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ marginTop: \"-3px\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\r\n\t\t\t{/* Color Picker Popover */}\r\n\t\t\t<Popover\r\n\t\t\t\topen={colorPickerOpen}\r\n\t\t\t\tanchorEl={colorPickerAnchorEl}\r\n\t\t\t\tonClose={handleCloseColorPicker}\r\n\t\t\t\tanchorOrigin={{\r\n\t\t\t\t\tvertical: \"bottom\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t\ttransformOrigin={{\r\n\t\t\t\t\tvertical: \"top\",\r\n\t\t\t\t\thorizontal: \"center\",\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<ChromePicker\r\n\t\t\t\t\t\tcolor={currentContainerColor}\r\n\t\t\t\t\t\tonChange={handleColorChange}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<style>\r\n\t\t\t\t\t\t{`\r\n      .chrome-picker input {\r\n        padding: 0 !important;\r\n      }\r\n    `}\r\n\t\t\t\t\t</style>\r\n\t\t\t\t</Box>\r\n\t\t\t</Popover>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ButtonSection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAACC,SAAS,QAAQ,OAAO;AACjD,SAASC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAaC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AAChG,SAASC,YAAY,QAAqB,aAAa;AACvD,SAASC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,QAAuC,6BAA6B;AAC/G,OAAOC,cAAc,MAAmB,4BAA4B;AACpE,OAAOC,OAAO,MAAM,yBAAyB;AAG7C,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,aAAkM,GAAGA,CAAC;EAC3MC,WAAW;EACXC,cAAc;EACdC,QAAQ;EACRC,eAAe;EACfC,QAAQ;EACRC;AACD,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACL,MAAM;IACLC,gBAAgB;IAChBC,oBAAoB;IACpBC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,qBAAqB;IACrBC,eAAe;IACfC,kBAAkB;IAClBC,gBAAgB;IAChBC,oBAAoB;IACpBC,mBAAmB;IACnBC,cAAc;IACdC,iBAAiB;IACjBC,UAAU;IACVC,cAAc;IACdC,YAAY;IACZC,WAAW;IACXC,cAAc;IACdC,YAAY;IACZC,WAAW;IACXC,iCAAiC;IACjCC;EACD,CAAC,GAAGtC,cAAc,CAAEuC,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGvC,cAAc,CAAC,CAAC;EACzC,MAAM,CAACwC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvD,QAAQ,CAAU,KAAK,CAAC;EAC1E,MAAM,CAACwD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzD,QAAQ,CAAU,KAAK,CAAC;EAC1E,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAS,UAAU,CAAC;EAChF,MAAM,CAAC4D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7D,QAAQ,CAAS,UAAU,CAAC;EAChF,MAAM,CAAC8D,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAS,SAAS,CAAC;EACrE,MAAM,CAACkE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnE,QAAQ,CAAqB,IAAI,CAAC;EACxF,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAS,UAAU,CAAC;EAChE,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC4E,eAAe,EAAEC,kBAAkB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8E,eAAe,EAAEC,kBAAkB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAgB,IAAI,CAAC;;EAE/D;EACA,IAAIkF,YAA4B;EAEhCjF,SAAS,CAAC,MAAM;IACf8D,WAAW,CAAC,IAAI,CAAC;IACjBtB,iBAAiB,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAxC,SAAS,CAAC,MAAM;IACf,IAAIiD,oBAAoB,GAAG,CAAC,EAAE;MAC7Ba,WAAW,CAAC,IAAI,CAAC;MACjBI,sBAAsB,CAAC,IAAI,CAAC;MAC5BI,eAAe,CAAC,IAAI,CAAC;IACtB;EACD,CAAC,EAAE,CAACrB,oBAAoB,CAAC,CAAC;EAE1B,MAAMiC,WAAW,GAAGA,CAACC,KAAoC,EAAEC,QAAgB,KAAK;IAC/E,MAAMC,MAAM,GAAGF,KAAK,CAACG,aAAa;IAElCL,YAAY,GAAGM,UAAU,CAAC,MAAM;MAC/BzB,WAAW,CAACuB,MAAM,CAAC;MACnBT,kBAAkB,CAACQ,QAAQ,CAAC;MAC5BN,kBAAkB,CAAC,KAAK,CAAC;MACzBU,oBAAoB,CAACf,kBAAkB,EAAEW,QAAQ,EAAE,WAAW,EAAE,KAAK,CAAC;IACvE,CAAC,EAAE,GAAG,CAAC;EACR,CAAC;EAED,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACzB3B,WAAW,CAAC,IAAI,CAAC;IACjB3B,kBAAkB,CAAC;MAAEuD,WAAW,EAAE,EAAE;MAAEN,QAAQ,EAAE,EAAE;MAAEO,KAAK,EAAE;IAAK,CAAC,CAAC;IAClErB,eAAe,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMsB,wBAAwB,GAAIT,KAA0C,IAAK;IAChFzB,qBAAqB,CAACyB,KAAK,CAACE,MAAM,CAACM,KAAK,CAAC;EAC1C,CAAC;EAED,MAAME,wBAAwB,GAAIV,KAA0C,IAAK;IAChFvB,qBAAqB,CAACuB,KAAK,CAACE,MAAM,CAACM,KAAK,CAAC;EAC1C,CAAC;EAED,MAAMG,0BAA0B,GAAIX,KAAoC,IAAK;IAC5EjB,sBAAsB,CAACiB,KAAK,CAACG,aAAa,CAAC;EAC5C,CAAC;EAED,MAAMS,iBAAiB,GAAIC,KAAkB,IAAK;IACjDhC,gBAAgB,CAACgC,KAAK,CAACC,GAAG,CAAC;IAC3B;IACA/D,eAAe,CAACuC,kBAAkB,EAAE,OAAO,EAAE;MAC5CyB,eAAe,EAAEF,KAAK,CAACC;IACxB,CAAC,CAAC;;IAEF;IACA/D,eAAe,CAACuC,kBAAkB,EAAE,iBAAiB,EAAEuB,KAAK,CAACC,GAAG,CAAC;EAClE,CAAC;EAED,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACpCjC,sBAAsB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMkC,IAAI,GAAGC,OAAO,CAACxC,QAAQ,CAAC;EAC9B;EACA,MAAMyC,EAAE,GAAGF,IAAI,GAAG,gBAAgB,GAAGG,SAAS;EAC9C,MAAMC,eAAe,GAAGH,OAAO,CAACpC,mBAAmB,CAAC;EACpD,MAAMwC,UAAU,GAAIC,MAA+B,IAAK;IACvD,IAAIA,MAAM,KAAK,UAAU,EAAE;MAC1BpD,oBAAoB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM,IAAIoD,MAAM,KAAK,UAAU,EAAE;MACjClD,oBAAoB,CAAC,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAMmD,kBAAkB,GAAGA,CAAA,KAAM;IAChCrD,oBAAoB,CAAC,KAAK,CAAC;EAC5B,CAAC;EAED,MAAMsD,kBAAkB,GAAGA,CAAA,KAAM;IAChCpD,oBAAoB,CAAC,KAAK,CAAC;EAC5B,CAAC;EACD,MAAMgC,oBAAoB,GAAGA,CAC5BE,WAAmB,EACnBN,QAAgB,EAChBL,SAAwB,EACxBY,KAA6B,KACzB;IACJkB,YAAY,CAAC5B,YAAY,CAAC;IAC1BH,kBAAkB,CAAC,IAAI,CAAC;IACxBhD,YAAY,CAAC4D,WAAW,EAAEN,QAAQ,EAAEL,SAAS,EAAEY,KAAK,CAAC;EACtD,CAAC;EAED,MAAMmB,kBAAkB,GAAGA,CAACpB,WAAmB,EAAEN,QAAgB,EAAEO,KAA6B,KAAK;IACpG7D,YAAY,CAAC4D,WAAW,EAAEN,QAAQ,EAAE,MAAM,EAAEO,KAAK,CAAC;EACnD,CAAC;EACD,MAAMoB,oBAAoB,GAAGA,CAACrB,WAAmB,EAAEN,QAAgB,EAAE4B,OAAe,KAAK;IACxFlF,YAAY,CAAC4D,WAAW,EAAEN,QAAQ,EAAE,MAAM,EAAE4B,OAAO,CAAC;IACpD1C,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;EACxB,CAAC;EACD;EACA,MAAM2C,kBAAkB,GAAIvB,WAAmB,IAAK;IACnD;IACA3D,YAAY,CACX;MACCuE,EAAE,EAAEY,MAAM,CAACC,UAAU,CAAC,CAAC;MACvBC,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE,QAAQ;MAClBC,IAAI,EAAE,SAAS;MACfvC,SAAS,EAAE,KAAK;MAChBwC,KAAK,EAAE,CAAC;MACRC,KAAK,EAAE;QACNtB,eAAe,EAAE;MAClB;IACD,CAAC,EACDR,WACD,CAAC;IACD;EACD,CAAC;;EAED;;EAEA,MAAM+B,qBAAqB,GAC1B,EAAA/F,qBAAA,GAAAE,gBAAgB,CAAC8F,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACrB,EAAE,KAAK7B,kBAAkB,CAAC,cAAA/C,qBAAA,wBAAAC,sBAAA,GAApED,qBAAA,CAAsE8F,KAAK,cAAA7F,sBAAA,uBAA3EA,sBAAA,CAA6EuE,eAAe,KAAI,SAAS;EAE1G,MAAM0B,oBAAoB,GAAGA,CAAA,KAAM;IAClC3F,qBAAqB,CAACwC,kBAAkB,CAAC;IACzCX,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACA,IAAIvC,QAAQ,EAAE;MACbA,QAAQ,CAAC,CAAC;IACX;EACD,CAAC;EACDqB,WAAW,CAAC+B,eAAe,CAAC;EAC5B9B,cAAc,CAAC8B,eAAe,CAAC;EAE/B,MAAMkD,sBAAsB,GAAI1C,KAAoC,IAAK;IACxE,IAAI/C,gBAAgB,KAAK,QAAQ,IAAIC,oBAAoB,KAAK,QAAQ,EAAE;MACvEF,kBAAkB,CAAC;QAClBuD,WAAW,EAAEjB,kBAAkB;QAC/BW,QAAQ,EAAET,eAAe;QACzB;QACAgB,KAAK,EAAER,KAAK,CAACG;MACd,CAAC,CAAC;MACF9C,iBAAiB,CAAC,IAAI,CAAC;IACxB,CAAC,MAAM;MACNL,kBAAkB,CAAC;QAClBuD,WAAW,EAAEjB,kBAAkB;QAC/BW,QAAQ,EAAET,eAAe;QACzB;QACAgB,KAAK,EAAER,KAAK,CAACG;MACd,CAAC,CAAC;IACH;IACAxB,WAAW,CAAC,IAAI,CAAC;IACjBQ,eAAe,CAAC,IAAI,CAAC;;IAErB;EACD,CAAC;;EAED;EACA,MAAMwD,gBAAgB,GAAGhF,YAAY,KAAKV,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,CAAC;EACzH,MAAM0F,kBAAkB,GAAGjF,YAAY,IAAIV,gBAAgB,KAAK,MAAM,IAAIC,oBAAoB,KAAK,cAAc;EACjH,MAAM2F,gBAAgB,GAAGjF,WAAW,GAAG,CAAC;EAExC,IAAIkF,kBAAyB,GAAG,EAAE;EAElC,IAAIH,gBAAgB,EAAE;IACrB;IACAG,kBAAkB,GAAGjF,iCAAiC,CAACgF,gBAAgB,EAAED,kBAAkB,CAAC;EAC7F,CAAC,MAAM;IACN;IACAE,kBAAkB,GAAGrG,gBAAgB;EACtC;EAEA,oBACCb,OAAA,CAAAE,SAAA;IAAAiH,QAAA,GACED,kBAAkB,CAACE,GAAG,CAAEC,UAAe,IAAK;MAC5C,oBACCrH,OAAA,CAACd,GAAG;QACHoI,SAAS,EAAC,KAAK;QACf/B,EAAE,EAAE8B,UAAU,CAAC9B;QACf;QAAA;QACAgC,EAAE,EAAE;UACHC,MAAM,EAAElH,QAAQ,GAAG,iBAAiB,GAAG,iBAAiB;UACxDmH,KAAK,EAAE,MAAM;UACbC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,KAAK;UACVC,OAAO,EAAE,QAAQ;UACjBC,SAAS,EAAE,YAAY;UACvB3C,eAAe,EAAEkC,UAAU,CAACZ,KAAK,CAACtB,eAAe,GAAGkC,UAAU,CAACZ,KAAK,CAACtB,eAAe,GAAGzD,UAAU;UACjGqG,cAAc,EAAE;UAChB;QACD,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAKtE,qBAAqB,CAACsE,CAAC,CAAC1D,aAAa,CAACgB,EAAE,CAAE;QAAA4B,QAAA,GAE9DE,UAAU,CAACa,OAAO,CAACd,GAAG,CAAER,IAAS,iBACjC5G,OAAA,CAACd,GAAG;UAEHqI,EAAE,EAAE;YACHjB,QAAQ,EAAE,UAAU;YACpBoB,OAAO,EAAE,MAAM;YACfK,cAAc,EAAE,QAAQ;YACxBJ,UAAU,EAAE,QAAQ;YACpB,sBAAsB,EAAE;cACvB;cACAQ,OAAO,EAAE;YACV;UACD,CAAE;UACFH,YAAY,EAAEA,CAAA,KAAMrE,qBAAqB,CAAC0D,UAAU,CAAC9B,EAAE,CAAE;UACzD6C,YAAY,EAAEA,CAAA,KAAM3E,eAAe,CAAC,EAAE,CAAE;UAAA0D,QAAA,gBAExCnH,OAAA,CAACb,MAAM;YACNoG,EAAE,EAAEqB,IAAI,CAACrB,EAAG;YACZ8C,OAAO,EAAE,WAAY;YACrBd,EAAE,EAAE;cACHe,UAAU,EAAE,0BAA0B;cACtCT,OAAO,EAAE,uBAAuB;cAChCU,YAAY,EAAE,KAAK;cACnBtD,KAAK,EAAE2B,IAAI,CAACH,KAAK,CAACxB,KAAK,IAAI,MAAM;cACjCuD,MAAM,EAAE5B,IAAI,CAACH,KAAK,CAACgC,WAAW,GAC3B,aAAa7B,IAAI,CAACH,KAAK,CAACgC,WAAW,EAAE,GACrC,MAAM;cACTC,aAAa,EAAE,MAAM;cACrBvD,eAAe,EAAEyB,IAAI,CAACH,KAAK,CAACtB,eAAe;cAC3CwD,SAAS,EAAE,MAAM;cACjB,SAAS,EAAE;gBACVA,SAAS,EAAE;cACZ;YACD,CAAE;YACFC,OAAO,EAAGX,CAAC,IAAK9D,WAAW,CAAC8D,CAAC,EAAErB,IAAI,CAACrB,EAAE,CAAE;YAAA4B,QAAA,eAExCnH,OAAA,CAACX,UAAU;cAACoH,KAAK,EAAE;gBAAExB,KAAK,EAAErD;cAAa,CAAE;cAAAuF,QAAA,EAAEP,IAAI,CAACP;YAAI;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,EAGR3B,UAAU,CAACa,OAAO,CAACe,MAAM,GAAG,CAAC,iBAC7BjJ,OAAA,CAACV,UAAU;YACV4J,IAAI,EAAC,OAAO;YACZC,SAAS,EAAC,aAAa;YACvB5B,EAAE,EAAE;cACHjB,QAAQ,EAAE,UAAU;cACpB8C,KAAK,EAAE,OAAO;cACdC,GAAG,EAAE,GAAG;cACRC,SAAS,EAAE,kBAAkB;cAC7BnE,eAAe,EAAE,MAAM;cACvBwD,SAAS,EAAE,gCAAgC;cAC3CR,OAAO,EAAE,CAAC;cAAE;cACZoB,UAAU,EAAE,mBAAmB;cAAE;cACjCC,MAAM,EAAE,CAAC;cACT3B,OAAO,EAAE,gBAAgB;cACzB,SAAS,EAAE;gBACV1C,eAAe,EAAE,MAAM;gBACvBwD,SAAS,EAAE;cACZ,CAAC;cACDc,IAAI,EAAE;gBACLjC,MAAM,EAAE;cACT,CAAC;cACD,OAAO,EAAE;gBACRC,KAAK,EAAE,MAAM;gBAAE;gBACfD,MAAM,EAAE,MAAM;gBAAE;gBAChBkC,IAAI,EAAE;kBACLC,IAAI,EAAC;gBACN;cACD;YACD,CAAE;YACFf,OAAO,EAAEA,CAAA,KAAM3H,YAAY,CAACoG,UAAU,CAAC9B,EAAE,EAAEqB,IAAI,CAACrB,EAAE,CAAE;YAAA4B,QAAA,eAEpDnH,OAAA;cAAM4J,uBAAuB,EAAE;gBAAEC,MAAM,EAAEpK;cAAW;YAAE;cAAAoJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CACZ;QAAA,GAxEIpC,IAAI,CAACrB,EAAE;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAyER,CACL,CAAC,EAED,CAAC3H,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,KAChF+F,UAAU,CAACa,OAAO,CAACe,MAAM,GAAG,CAAC,gBAC5BjJ,OAAA,CAACV,UAAU;UACViI,EAAE,EAAE;YACHpC,eAAe,EAAE,SAAS;YAC1B2E,MAAM,EAAE,SAAS;YACjBN,MAAM,EAAE,IAAI;YACZ3B,OAAO,EAAE,gBAAgB;YACzB,SAAS,EAAE;cACV1C,eAAe,EAAE;YAClB;UACD;UACA;UAAA;UACAyD,OAAO,EAAEA,CAAA,KAAM;YACd1C,kBAAkB,CAACmB,UAAU,CAAC9B,EAAE,CAAC;YACjC,IAAI9E,OAAO,EAAE;cACZA,OAAO,CAAC,CAAC;YACV;UACD,CAAE;UAAA0G,QAAA,eAEFnH,OAAA,CAACH,OAAO;YACPkK,QAAQ,EAAC,OAAO;YAChBxC,EAAE,EAAE;cAAEtC,KAAK,EAAE;YAAO;UAAE;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,GACV,IAAI;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAER,CAAC,CAAC,eACFhJ,OAAA,CAACZ,OAAO;MACH+J,SAAS,EAAC,eAAe;MAC7B5D,EAAE,EAAEA,EAAG;MACPF,IAAI,EAAEA,IAAK;MACXvC,QAAQ,EAAEA,QAAS;MACnBkH,OAAO,EAAEtF,WAAY;MACrBuF,YAAY,EAAE;QACbC,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MACFC,eAAe,EAAE;QAChBF,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACF5C,EAAE,EAAE;QACH8C,SAAS,EAAE/J,QAAQ,GAAG,kBAAkB,GAAG;MAC5C;MACA;MAAA;MAAA6G,QAAA,eAEAnH,OAAA,CAACd,GAAG;QACHqI,EAAE,EAAE;UACHG,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,KAAK;UACVC,OAAO,EAAE;QACV,CAAE;QAAAV,QAAA,gBA+CFnH,OAAA,CAACT,OAAO;UAAC+K,KAAK,EAAEjI,SAAS,CAAC,UAAU,CAAE;UAAA8E,QAAA,eACrCnH,OAAA,CAACV,UAAU;YACV4J,IAAI,EAAC,OAAO;YACZN,OAAO,EAAE9B,sBAAuB;YAAAK,QAAA,eAEhCnH,OAAA;cAAM4J,uBAAuB,EAAE;gBAAEC,MAAM,EAAElK;cAAa;YAAE;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,EACT,CAAC3H,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,kBAC/EtB,OAAA,CAACT,OAAO;UAAC+K,KAAK,EAAEjI,SAAS,CAAC,kBAAkB,CAAE;UAAA8E,QAAA,eAC7CnH,OAAA,CAACV,UAAU;YACV4J,IAAI,EAAC,OAAO;YACZN,OAAO,EAAE7D,0BAA2B;YAAAoC,QAAA,eAEpCnH,OAAA;cACCyG,KAAK,EAAE;gBACNtB,eAAe,EAAEnC,aAAa;gBAC9BuF,YAAY,EAAE,MAAM;gBACpBd,KAAK,EAAE,MAAM;gBACbD,MAAM,EAAE,MAAM;gBACdE,OAAO,EAAE,cAAc;gBACvB2C,SAAS,EAAE;cACZ;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACT,EACA,CAAC3H,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,kBAC/EtB,OAAA,CAACT,OAAO;UAAC+K,KAAK,EAAE/J,eAAe,GAAG8B,SAAS,CAAC,4CAA4C,CAAC,GAAGA,SAAS,CAAC,cAAc,CAAE;UAAA8E,QAAA,eACrHnH,OAAA,CAACV,UAAU;YACV4J,IAAI,EAAC,OAAO;YACZN,OAAO,EAAEA,CAAA,KAAM;cACd9H,oBAAoB,CAAC4C,kBAAkB,CAAC;cACxC,IAAIjD,OAAO,EAAE;gBACZA,OAAO,CAAC,CAAC;cACV;YACD,CAAE;YACF8J,QAAQ,EAAEhK,eAAgB;YAAA4G,QAAA,eAE1BnH,OAAA;cACC4J,uBAAuB,EAAE;gBAAEC,MAAM,EAAEnK;cAAS,CAAE;cAC9C+G,KAAK,EAAE;gBAAE0B,OAAO,EAAE5H,eAAe,GAAG,GAAG,GAAG;cAAE;YAAE;cAAAsI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACT,EACA,CAAC3H,gBAAgB,KAAK,cAAc,IAAIC,oBAAoB,KAAK,cAAc,kBAC/EtB,OAAA,CAACT,OAAO;UAAC+K,KAAK,EAAEjI,SAAS,CAAC,eAAe,CAAE;UAAA8E,QAAA,eAC1CnH,OAAA,CAACV,UAAU;YACV4J,IAAI,EAAC,OAAO;YACZN,OAAO,EAAE/B,oBAAqB;YAAAM,QAAA,eAE9BnH,OAAA;cACC4J,uBAAuB,EAAE;gBAAEC,MAAM,EAAEpK;cAAW,CAAE;cAChDgH,KAAK,EAAE;gBAAE4D,SAAS,EAAE;cAAO;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGVhJ,OAAA,CAACZ,OAAO;MACPiG,IAAI,EAAEI,eAAgB;MACtB3C,QAAQ,EAAEI,mBAAoB;MAC9B8G,OAAO,EAAE5E,sBAAuB;MAChC6E,YAAY,EAAE;QACbC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE;MACb,CAAE;MACFC,eAAe,EAAE;QAChBF,QAAQ,EAAE,KAAK;QACfC,UAAU,EAAE;MACb,CAAE;MAAAhD,QAAA,eAEFnH,OAAA,CAACd,GAAG;QAAAiI,QAAA,gBACHnH,OAAA,CAACR,YAAY;UACZyF,KAAK,EAAEyB,qBAAsB;UAC7B8D,QAAQ,EAAExF;QAAkB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACFhJ,OAAA;UAAAmH,QAAA,EACE;AACP;AACA;AACA;AACA;QAAK;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACT,CAAC;AAEL,CAAC;AAACtI,EAAA,CAlgBIP,aAAkM;EAAA,QA+BnMP,cAAc,EACOE,cAAc;AAAA;AAAA2K,EAAA,GAhClCtK,aAAkM;AAogBxM,eAAeA,aAAa;AAAC,IAAAsK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}