{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideBanners\\\\selectedpopupfields\\\\ImageSectionField.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Box, Typography, Popover, IconButton, Dialog } from \"@mui/material\";\nimport RemoveIcon from \"@mui/icons-material/Remove\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport SettingsIcon from \"@mui/icons-material/Settings\";\nimport { uploadfile, hyperlink, files, uploadicon, replaceimageicon, galleryicon, copyicon, deleteicon, sectionheight } from \"../../../assets/icons/icons\";\nimport ImageGalleryPopup from \"./ImageGalleryPopup\";\nimport ImageProperties from \"./ImageProperties\";\n//import ImagePageInteractions from \"./PageInteraction\";\nimport \"../guideBanner.css\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageSectionField = ({\n  setImageSrc,\n  imageSrc,\n  setImageName\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [imageHeight, setImageHeight] = useState(70);\n  const [showSection, setShowSection] = useState(true);\n  const [isGalleryOpen, setIsGalleryOpen] = useState(false);\n  const [isSettingsOpen, setIsSettingsOpen] = useState(false);\n  const containerStyle = {\n    width: \"100%\",\n    display: \"flex\",\n    flexDirection: \"row\",\n    justifyContent: \"flex-start\",\n    alignItems: \"center\",\n    padding: 0,\n    margin: 0,\n    overflow: \"hidden\"\n  };\n  const imageContainerStyle = {\n    width: \"100%\",\n    height: `${imageHeight}px`,\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    padding: 0,\n    margin: 0,\n    overflow: \"hidden\",\n    backgroundColor: \"#f0f0f0\"\n  };\n  const imageStyle = {\n    width: \"100%\",\n    height: \"100%\",\n    objectFit: \"cover\",\n    margin: 0,\n    padding: 0,\n    borderRadius: \"0\"\n  };\n  const iconRowStyle = {\n    display: \"flex\",\n    justifyContent: \"center\",\n    gap: \"16px\"\n  };\n  const iconTextStyle = {\n    display: \"flex\",\n    flexDirection: \"row\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    gap: \"8px\",\n    width: \"100%\"\n  };\n\n  // const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n  // \tconst file = event.target.files?.[0];\n  // \tif (file) {\n  // \t\tconst reader = new FileReader();\n  // \t\treader.onloadend = () => {\n  // \t\t\t// Use reader.result directly, including the base64 prefix\n  // \t\t\tconst base64String = reader.result as string;\n  // \t\t\tsetImageSrc(base64String); // Set the full base64 string including the prefix\n  // \t\t\tsetImageName(file.name);\n  // \t\t};\n  // \t\treader.readAsDataURL(file); // This will automatically include the correct prefix\n  // \t}\n  // };\n  const handleImageUpload = event => {\n    var _event$target$files;\n    const file = (_event$target$files = event.target.files) === null || _event$target$files === void 0 ? void 0 : _event$target$files[0];\n    if (file) {\n      var _event$target$files2;\n      setImageName((_event$target$files2 = event.target.files) === null || _event$target$files2 === void 0 ? void 0 : _event$target$files2[0].name);\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setImageSrc(reader.result);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n  const handleClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n  const open = Boolean(anchorEl);\n  const id = open ? \"image-popover\" : undefined;\n  const handleIncreaseHeight = () => {\n    setImageHeight(prevHeight => prevHeight + 5);\n  };\n  const handleDecreaseHeight = () => {\n    setImageHeight(prevHeight => prevHeight > 10 ? prevHeight - 5 : prevHeight);\n  };\n  const triggerImageUpload = () => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById(\"replace-upload\")) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.click();\n  };\n\n  // Function to delete the section\n  const handleDeleteSection = () => {\n    setShowSection(false); // Hide the section by updating the state\n  };\n  const handleOpenGallery = () => {\n    setIsGalleryOpen(true);\n  };\n  const handleCloseGallery = () => {\n    setIsGalleryOpen(false);\n  };\n  const handleOpenSettings = () => {\n    setIsSettingsOpen(true);\n  };\n  const handleCloseSettings = () => {\n    setIsSettingsOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: showSection && /*#__PURE__*/_jsxDEV(Box, {\n      sx: containerStyle,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-imageupload\",\n        onClick: handleClick,\n        children: imageSrc ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageSrc,\n          alt: \"Uploaded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 5\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          className: \"upload-container\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"icon-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: uploadfile\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              align: \"center\",\n              children: translate(\"Upload file\", {\n                defaultValue: \"Upload file\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"icon-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: hyperlink\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: files\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: () => {\n                var _document$getElementB2;\n                return (_document$getElementB2 = document.getElementById(\"file-upload\")) === null || _document$getElementB2 === void 0 ? void 0 : _document$getElementB2.click();\n              },\n              dangerouslySetInnerHTML: {\n                __html: uploadicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"file-upload\",\n              accept: \"image/*\",\n              onChange: handleImageUpload\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 5\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(Popover, {\n        id: id,\n        open: open,\n        anchorEl: anchorEl,\n        onClose: handleClose,\n        anchorOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"center\"\n        },\n        transformOrigin: {\n          vertical: \"bottom\",\n          horizontal: \"center\"\n        },\n        PaperProps: {\n          className: \"qadpt-imagepopup\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-imagepopup-content\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-imagepopup-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: replaceimageicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 4\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-imagepopup-text\",\n              onClick: triggerImageUpload,\n              children: translate(\"Replace Image\", {\n                defaultValue: \"Replace Image\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 4\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              id: \"replace-upload\",\n              className: \"qadpt-imagepopup-upload\",\n              accept: \"image/*\",\n              onChange: handleImageUpload\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 4\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-imagepopup-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: galleryicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 4\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-imagepopup-text\",\n              onClick: handleOpenGallery,\n              children: translate(\"Open Gallery\", {\n                defaultValue: \"Open Gallery\"\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 4\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-imagepopup-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: sectionheight\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 4\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleDecreaseHeight,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(RemoveIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 5\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 4\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-imagepopup-text\",\n              children: imageHeight\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 4\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleIncreaseHeight,\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {\n                fontSize: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 5\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 4\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-imagepopup-item\",\n            children: /*#__PURE__*/_jsxDEV(SettingsIcon, {\n              fontSize: \"small\",\n              onClick: handleOpenSettings\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 4\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-imagepopup-item\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: copyicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 4\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 3\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-imagepopup-item\",\n            onClick: handleDeleteSection,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              dangerouslySetInnerHTML: {\n                __html: deleteicon\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 4\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 3\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 2\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 1\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: isSettingsOpen,\n        onClose: handleCloseSettings,\n        children: /*#__PURE__*/_jsxDEV(ImageProperties, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 6\n      }, this), isGalleryOpen && /*#__PURE__*/_jsxDEV(ImageGalleryPopup, {\n        open: isGalleryOpen,\n        onClose: handleCloseGallery\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n};\n_s(ImageSectionField, \"YdcdoDPjtAITSWWE2Zw1wGrFTHI=\", false, function () {\n  return [useTranslation];\n});\n_c = ImageSectionField;\nexport default ImageSectionField;\nvar _c;\n$RefreshReg$(_c, \"ImageSectionField\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Popover", "IconButton", "Dialog", "RemoveIcon", "AddIcon", "SettingsIcon", "uploadfile", "hyperlink", "files", "uploadicon", "replaceimageicon", "galleryicon", "copyicon", "deleteicon", "sectionheight", "ImageGalleryPopup", "ImageProperties", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageSectionField", "setImageSrc", "imageSrc", "setImageName", "_s", "t", "translate", "anchorEl", "setAnchorEl", "imageHeight", "setImageHeight", "showSection", "setShowSection", "isGalleryOpen", "setIsGalleryOpen", "isSettingsOpen", "setIsSettingsOpen", "containerStyle", "width", "display", "flexDirection", "justifyContent", "alignItems", "padding", "margin", "overflow", "imageContainerStyle", "height", "backgroundColor", "imageStyle", "objectFit", "borderRadius", "iconRowStyle", "gap", "iconTextStyle", "handleImageUpload", "event", "_event$target$files", "file", "target", "_event$target$files2", "name", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleClick", "currentTarget", "handleClose", "open", "Boolean", "id", "undefined", "handleIncreaseHeight", "prevHeight", "handleDecreaseHeight", "triggerImageUpload", "_document$getElementB", "document", "getElementById", "click", "handleDeleteSection", "handleOpenGallery", "handleCloseGallery", "handleOpenSettings", "handleCloseSettings", "children", "sx", "className", "onClick", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dangerouslySetInnerHTML", "__html", "align", "defaultValue", "_document$getElementB2", "type", "accept", "onChange", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "PaperProps", "size", "fontSize", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/guideBanners/selectedpopupfields/ImageSectionField.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { <PERSON>, <PERSON>po<PERSON>, Popover, IconButton, Dialog, TextField, MenuItem, Button } from \"@mui/material\";\r\nimport RemoveIcon from \"@mui/icons-material/Remove\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport SettingsIcon from \"@mui/icons-material/Settings\";\r\nimport {\r\n\tuploadfile,\r\n\thyperlink,\r\n\tfiles,\r\n\tuploadicon,\r\n\treplaceimageicon,\r\n\tgalleryicon,\r\n\tbackgroundcoloricon,\r\n\tcopyicon,\r\n\tdeleteicon,\r\n\tsectionheight,\r\n} from \"../../../assets/icons/icons\";\r\nimport ImageGalleryPopup from \"./ImageGalleryPopup\";\r\nimport ImageProperties from \"./ImageProperties\";\r\n//import ImagePageInteractions from \"./PageInteraction\";\r\nimport \"../guideBanner.css\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\nconst ImageSectionField: React.FC<{ setImageSrc: any; imageSrc: any; setImageName: any }> = ({\r\n\tsetImageSrc,\r\n\timageSrc,\r\n\tsetImageName,\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [imageHeight, setImageHeight] = useState<number>(70);\r\n\tconst [showSection, setShowSection] = useState<boolean>(true);\r\n\tconst [isGalleryOpen, setIsGalleryOpen] = useState(false);\r\n\tconst [isSettingsOpen, setIsSettingsOpen] = useState(false);\r\n\r\n\tconst containerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"row\",\r\n\t\tjustifyContent: \"flex-start\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t};\r\n\r\n\tconst imageContainerStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: `${imageHeight}px`,\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\talignItems: \"center\",\r\n\t\tpadding: 0,\r\n\t\tmargin: 0,\r\n\t\toverflow: \"hidden\",\r\n\t\tbackgroundColor: \"#f0f0f0\",\r\n\t};\r\n\r\n\tconst imageStyle: React.CSSProperties = {\r\n\t\twidth: \"100%\",\r\n\t\theight: \"100%\",\r\n\t\tobjectFit: \"cover\",\r\n\t\tmargin: 0,\r\n\t\tpadding: 0,\r\n\t\tborderRadius: \"0\",\r\n\t};\r\n\r\n\tconst iconRowStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"16px\",\r\n\t};\r\n\r\n\tconst iconTextStyle: React.CSSProperties = {\r\n\t\tdisplay: \"flex\",\r\n\t\tflexDirection: \"row\",\r\n\t\talignItems: \"center\",\r\n\t\tjustifyContent: \"center\",\r\n\t\tgap: \"8px\",\r\n\t\twidth: \"100%\",\r\n\t};\r\n\r\n\t// const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t// \tconst file = event.target.files?.[0];\r\n\t// \tif (file) {\r\n\t// \t\tconst reader = new FileReader();\r\n\t// \t\treader.onloadend = () => {\r\n\t// \t\t\t// Use reader.result directly, including the base64 prefix\r\n\t// \t\t\tconst base64String = reader.result as string;\r\n\t// \t\t\tsetImageSrc(base64String); // Set the full base64 string including the prefix\r\n\t// \t\t\tsetImageName(file.name);\r\n\t// \t\t};\r\n\t// \t\treader.readAsDataURL(file); // This will automatically include the correct prefix\r\n\t// \t}\r\n\t// };\r\n\tconst handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n\t\tconst file = event.target.files?.[0];\r\n\r\n\t\tif (file) {\r\n\t\t\tsetImageName(event.target.files?.[0].name);\r\n\t\t\tconst reader = new FileReader();\r\n\t\t\treader.onloadend = () => {\r\n\t\t\t\tsetImageSrc(reader.result as string);\r\n\t\t\t};\r\n\t\t\treader.readAsDataURL(file);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleClick = (event: React.MouseEvent<HTMLElement>) => {\r\n\t\tsetAnchorEl(event.currentTarget);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetAnchorEl(null);\r\n\t};\r\n\r\n\tconst open = Boolean(anchorEl);\r\n\tconst id = open ? \"image-popover\" : undefined;\r\n\r\n\tconst handleIncreaseHeight = () => {\r\n\t\tsetImageHeight((prevHeight) => prevHeight + 5);\r\n\t};\r\n\r\n\tconst handleDecreaseHeight = () => {\r\n\t\tsetImageHeight((prevHeight) => (prevHeight > 10 ? prevHeight - 5 : prevHeight));\r\n\t};\r\n\r\n\tconst triggerImageUpload = () => {\r\n\t\tdocument.getElementById(\"replace-upload\")?.click();\r\n\t};\r\n\r\n\t// Function to delete the section\r\n\tconst handleDeleteSection = () => {\r\n\t\tsetShowSection(false); // Hide the section by updating the state\r\n\t};\r\n\tconst handleOpenGallery = () => {\r\n\t\tsetIsGalleryOpen(true);\r\n\t};\r\n\r\n\tconst handleCloseGallery = () => {\r\n\t\tsetIsGalleryOpen(false);\r\n\t};\r\n\r\n\tconst handleOpenSettings = () => {\r\n\t\tsetIsSettingsOpen(true);\r\n\t};\r\n\r\n\tconst handleCloseSettings = () => {\r\n\t\tsetIsSettingsOpen(false);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{showSection && (\r\n\t\t\t\t<Box sx={containerStyle}>\r\n\t\t\t\t\t<Box className=\"qadpt-imageupload\" onClick={handleClick}>\r\n  {imageSrc ? (\r\n    <img src={imageSrc} alt=\"Uploaded\" />\r\n  ) : (\r\n    <Box className=\"upload-container\">\r\n      <Box className=\"icon-text\">\r\n        <span\r\n          dangerouslySetInnerHTML={{ __html: uploadfile }}\r\n        />\r\n        <Typography align=\"center\">\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Upload file\", { defaultValue: \"Upload file\" })}\r\n        </Typography>\r\n      </Box>\r\n\r\n      <Box className=\"icon-row\">\r\n        <span\r\n          dangerouslySetInnerHTML={{ __html: hyperlink }}\r\n        />\r\n        <span\r\n          dangerouslySetInnerHTML={{ __html: files }}\r\n        />\r\n        <span\r\n          onClick={() => document.getElementById(\"file-upload\")?.click()}\r\n          dangerouslySetInnerHTML={{ __html: uploadicon }}\r\n        />\r\n        <input\r\n          type=\"file\"\r\n          id=\"file-upload\"\r\n          accept=\"image/*\"\r\n          onChange={handleImageUpload}\r\n        />\r\n      </Box>\r\n    </Box>\r\n  )}\r\n</Box>\r\n\r\n<Popover\r\n\tid={id}\r\n\topen={open}\r\n\tanchorEl={anchorEl}\r\n\tonClose={handleClose}\r\n\tanchorOrigin={{\r\n\t\tvertical: \"bottom\",\r\n\t\thorizontal: \"center\",\r\n\t}}\r\n\ttransformOrigin={{\r\n\t\tvertical: \"bottom\",\r\n\t\thorizontal: \"center\",\r\n\t}}\r\n\tPaperProps={{\r\n\t\tclassName: \"qadpt-imagepopup\",\r\n\t}}\r\n>\r\n\t<Box className=\"qadpt-imagepopup-content\">\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: replaceimageicon }} />\r\n\t\t\t<Typography className=\"qadpt-imagepopup-text\" onClick={triggerImageUpload}>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Replace Image\", { defaultValue: \"Replace Image\" })}\r\n\t\t\t</Typography>\r\n\t\t\t<input\r\n\t\t\t\ttype=\"file\"\r\n\t\t\t\tid=\"replace-upload\"\r\n\t\t\t\tclassName=\"qadpt-imagepopup-upload\"\r\n\t\t\t\taccept=\"image/*\"\r\n\t\t\t\tonChange={handleImageUpload}\r\n\t\t\t/>\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: galleryicon }} />\r\n\t\t\t<Typography className=\"qadpt-imagepopup-text\" onClick={handleOpenGallery}>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Open Gallery\", { defaultValue: \"Open Gallery\" })}\r\n\t\t\t</Typography>\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: sectionheight }} />\r\n\t\t\t<IconButton onClick={handleDecreaseHeight} size=\"small\">\r\n\t\t\t\t<RemoveIcon fontSize=\"small\" />\r\n\t\t\t</IconButton>\r\n\t\t\t<Typography className=\"qadpt-imagepopup-text\">{imageHeight}</Typography>\r\n\t\t\t<IconButton onClick={handleIncreaseHeight} size=\"small\">\r\n\t\t\t\t<AddIcon fontSize=\"small\" />\r\n\t\t\t</IconButton>\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<SettingsIcon fontSize=\"small\" onClick={handleOpenSettings} />\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\">\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: copyicon }} />\r\n\t\t</Box>\r\n\r\n\t\t<Box className=\"qadpt-imagepopup-item\" onClick={handleDeleteSection}>\r\n\t\t\t<span dangerouslySetInnerHTML={{ __html: deleteicon }} />\r\n\t\t</Box>\r\n\t</Box>\r\n</Popover>\r\n\r\n\t\t\t\t\t{/* Settings Dialog */}\r\n\t\t\t\t\t<Dialog\r\n\t\t\t\t\t\topen={isSettingsOpen}\r\n\t\t\t\t\t\tonClose={handleCloseSettings}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ImageProperties></ImageProperties>\r\n\r\n\t\t\t\t\t\t{/* <Box sx={{ padding: \"20px\", minWidth: \"300px\" }}>\r\n\t\t\t\t\t\t\t<Typography variant=\"h6\">Settings</Typography>\r\n\t\t\t\t\t\t\t{/* Additional Settings Fields \r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tlabel=\"Hyperlink\"\r\n\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tlabel=\"Actions\"\r\n\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Action1\">Action1</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Action2\">Action2</MenuItem>\r\n\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tlabel=\"Formatting\"\r\n\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\tselect\r\n\t\t\t\t\t\t\t\tsx={{ marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Format1\">Format1</MenuItem>\r\n\t\t\t\t\t\t\t\t<MenuItem value=\"Format2\">Format2</MenuItem>\r\n\t\t\t\t\t\t\t</TextField>\r\n\t\t\t\t\t\t\t<Box sx={{ marginTop: \"20px\", display: \"flex\", justifyContent: \"flex-end\" }}>\r\n\t\t\t\t\t\t\t\t<Button onClick={handleCloseSettings}>Close</Button>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</Box> */}\r\n\t\t\t\t\t</Dialog>\r\n\r\n\t\t\t\t\t{isGalleryOpen && (\r\n\t\t\t\t\t\t<ImageGalleryPopup\r\n\t\t\t\t\t\t\topen={isGalleryOpen}\r\n\t\t\t\t\t\t\tonClose={handleCloseGallery}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default ImageSectionField;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,QAAqC,eAAe;AACzG,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,SACCC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,WAAW,EAEXC,QAAQ,EACRC,UAAU,EACVC,aAAa,QACP,6BAA6B;AACpC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C;AACA,OAAO,oBAAoB;AAC3B,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMC,iBAAmF,GAAGA,CAAC;EAC5FC,WAAW;EACXC,QAAQ;EACRC;AACD,CAAC,KAAK;EAAAC,EAAA;EACL,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGX,cAAc,CAAC,CAAC;EACzC,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAU,IAAI,CAAC;EAC7D,MAAM,CAACsC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM0C,cAAmC,GAAG;IAC3CC,KAAK,EAAE,MAAM;IACbC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACX,CAAC;EAED,MAAMC,mBAAwC,GAAG;IAChDR,KAAK,EAAE,MAAM;IACbS,MAAM,EAAE,GAAGlB,WAAW,IAAI;IAC1BU,OAAO,EAAE,MAAM;IACfE,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,QAAQ;IAClBG,eAAe,EAAE;EAClB,CAAC;EAED,MAAMC,UAA+B,GAAG;IACvCX,KAAK,EAAE,MAAM;IACbS,MAAM,EAAE,MAAM;IACdG,SAAS,EAAE,OAAO;IAClBN,MAAM,EAAE,CAAC;IACTD,OAAO,EAAE,CAAC;IACVQ,YAAY,EAAE;EACf,CAAC;EAED,MAAMC,YAAiC,GAAG;IACzCb,OAAO,EAAE,MAAM;IACfE,cAAc,EAAE,QAAQ;IACxBY,GAAG,EAAE;EACN,CAAC;EAED,MAAMC,aAAkC,GAAG;IAC1Cf,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBY,GAAG,EAAE,KAAK;IACVf,KAAK,EAAE;EACR,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMiB,iBAAiB,GAAIC,KAA0C,IAAK;IAAA,IAAAC,mBAAA;IACzE,MAAMC,IAAI,IAAAD,mBAAA,GAAGD,KAAK,CAACG,MAAM,CAACrD,KAAK,cAAAmD,mBAAA,uBAAlBA,mBAAA,CAAqB,CAAC,CAAC;IAEpC,IAAIC,IAAI,EAAE;MAAA,IAAAE,oBAAA;MACTrC,YAAY,EAAAqC,oBAAA,GAACJ,KAAK,CAACG,MAAM,CAACrD,KAAK,cAAAsD,oBAAA,uBAAlBA,oBAAA,CAAqB,CAAC,CAAC,CAACC,IAAI,CAAC;MAC1C,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM;QACxB3C,WAAW,CAACyC,MAAM,CAACG,MAAgB,CAAC;MACrC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACR,IAAI,CAAC;IAC3B;EACD,CAAC;EAED,MAAMS,WAAW,GAAIX,KAAoC,IAAK;IAC7D5B,WAAW,CAAC4B,KAAK,CAACY,aAAa,CAAC;EACjC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACzBzC,WAAW,CAAC,IAAI,CAAC;EAClB,CAAC;EAED,MAAM0C,IAAI,GAAGC,OAAO,CAAC5C,QAAQ,CAAC;EAC9B,MAAM6C,EAAE,GAAGF,IAAI,GAAG,eAAe,GAAGG,SAAS;EAE7C,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IAClC5C,cAAc,CAAE6C,UAAU,IAAKA,UAAU,GAAG,CAAC,CAAC;EAC/C,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IAClC9C,cAAc,CAAE6C,UAAU,IAAMA,UAAU,GAAG,EAAE,GAAGA,UAAU,GAAG,CAAC,GAAGA,UAAW,CAAC;EAChF,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,cAAAF,qBAAA,uBAAzCA,qBAAA,CAA2CG,KAAK,CAAC,CAAC;EACnD,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IACjClD,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACxB,CAAC;EACD,MAAMmD,iBAAiB,GAAGA,CAAA,KAAM;IAC/BjD,gBAAgB,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMkD,kBAAkB,GAAGA,CAAA,KAAM;IAChClD,gBAAgB,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMmD,kBAAkB,GAAGA,CAAA,KAAM;IAChCjD,iBAAiB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkD,mBAAmB,GAAGA,CAAA,KAAM;IACjClD,iBAAiB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,oBACCnB,OAAA,CAAAE,SAAA;IAAAoE,QAAA,EACExD,WAAW,iBACXd,OAAA,CAACrB,GAAG;MAAC4F,EAAE,EAAEnD,cAAe;MAAAkD,QAAA,gBACvBtE,OAAA,CAACrB,GAAG;QAAC6F,SAAS,EAAC,mBAAmB;QAACC,OAAO,EAAEvB,WAAY;QAAAoB,QAAA,EAC1DjE,QAAQ,gBACPL,OAAA;UAAK0E,GAAG,EAAErE,QAAS;UAACsE,GAAG,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAErC/E,OAAA,CAACrB,GAAG;UAAC6F,SAAS,EAAC,kBAAkB;UAAAF,QAAA,gBAC/BtE,OAAA,CAACrB,GAAG;YAAC6F,SAAS,EAAC,WAAW;YAAAF,QAAA,gBACxBtE,OAAA;cACEgF,uBAAuB,EAAE;gBAAEC,MAAM,EAAE9F;cAAW;YAAE;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACF/E,OAAA,CAACpB,UAAU;cAACsG,KAAK,EAAC,QAAQ;cAAAZ,QAAA,EACtB7D,SAAS,CAAC,aAAa,EAAE;gBAAE0E,YAAY,EAAE;cAAc,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEN/E,OAAA,CAACrB,GAAG;YAAC6F,SAAS,EAAC,UAAU;YAAAF,QAAA,gBACvBtE,OAAA;cACEgF,uBAAuB,EAAE;gBAAEC,MAAM,EAAE7F;cAAU;YAAE;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACF/E,OAAA;cACEgF,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5F;cAAM;YAAE;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACF/E,OAAA;cACEyE,OAAO,EAAEA,CAAA;gBAAA,IAAAW,sBAAA;gBAAA,QAAAA,sBAAA,GAAMtB,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,cAAAqB,sBAAA,uBAAtCA,sBAAA,CAAwCpB,KAAK,CAAC,CAAC;cAAA,CAAC;cAC/DgB,uBAAuB,EAAE;gBAAEC,MAAM,EAAE3F;cAAW;YAAE;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACF/E,OAAA;cACEqF,IAAI,EAAC,MAAM;cACX9B,EAAE,EAAC,aAAa;cAChB+B,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAEjD;YAAkB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN/E,OAAA,CAACnB,OAAO;QACP0E,EAAE,EAAEA,EAAG;QACPF,IAAI,EAAEA,IAAK;QACX3C,QAAQ,EAAEA,QAAS;QACnB8E,OAAO,EAAEpC,WAAY;QACrBqC,YAAY,EAAE;UACbC,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE;QACb,CAAE;QACFC,eAAe,EAAE;UAChBF,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE;QACb,CAAE;QACFE,UAAU,EAAE;UACXrB,SAAS,EAAE;QACZ,CAAE;QAAAF,QAAA,eAEFtE,OAAA,CAACrB,GAAG;UAAC6F,SAAS,EAAC,0BAA0B;UAAAF,QAAA,gBACxCtE,OAAA,CAACrB,GAAG;YAAC6F,SAAS,EAAC,uBAAuB;YAAAF,QAAA,gBACrCtE,OAAA;cAAMgF,uBAAuB,EAAE;gBAAEC,MAAM,EAAE1F;cAAiB;YAAE;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/D/E,OAAA,CAACpB,UAAU;cAAC4F,SAAS,EAAC,uBAAuB;cAACC,OAAO,EAAEb,kBAAmB;cAAAU,QAAA,EACnE7D,SAAS,CAAC,eAAe,EAAE;gBAAE0E,YAAY,EAAE;cAAgB,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACb/E,OAAA;cACCqF,IAAI,EAAC,MAAM;cACX9B,EAAE,EAAC,gBAAgB;cACnBiB,SAAS,EAAC,yBAAyB;cACnCc,MAAM,EAAC,SAAS;cAChBC,QAAQ,EAAEjD;YAAkB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN/E,OAAA,CAACrB,GAAG;YAAC6F,SAAS,EAAC,uBAAuB;YAAAF,QAAA,gBACrCtE,OAAA;cAAMgF,uBAAuB,EAAE;gBAAEC,MAAM,EAAEzF;cAAY;YAAE;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1D/E,OAAA,CAACpB,UAAU;cAAC4F,SAAS,EAAC,uBAAuB;cAACC,OAAO,EAAEP,iBAAkB;cAAAI,QAAA,EAClE7D,SAAS,CAAC,cAAc,EAAE;gBAAE0E,YAAY,EAAE;cAAe,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN/E,OAAA,CAACrB,GAAG;YAAC6F,SAAS,EAAC,uBAAuB;YAAAF,QAAA,gBACrCtE,OAAA;cAAMgF,uBAAuB,EAAE;gBAAEC,MAAM,EAAEtF;cAAc;YAAE;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5D/E,OAAA,CAAClB,UAAU;cAAC2F,OAAO,EAAEd,oBAAqB;cAACmC,IAAI,EAAC,OAAO;cAAAxB,QAAA,eACtDtE,OAAA,CAAChB,UAAU;gBAAC+G,QAAQ,EAAC;cAAO;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACb/E,OAAA,CAACpB,UAAU;cAAC4F,SAAS,EAAC,uBAAuB;cAAAF,QAAA,EAAE1D;YAAW;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACxE/E,OAAA,CAAClB,UAAU;cAAC2F,OAAO,EAAEhB,oBAAqB;cAACqC,IAAI,EAAC,OAAO;cAAAxB,QAAA,eACtDtE,OAAA,CAACf,OAAO;gBAAC8G,QAAQ,EAAC;cAAO;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEN/E,OAAA,CAACrB,GAAG;YAAC6F,SAAS,EAAC,uBAAuB;YAAAF,QAAA,eACrCtE,OAAA,CAACd,YAAY;cAAC6G,QAAQ,EAAC,OAAO;cAACtB,OAAO,EAAEL;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAEN/E,OAAA,CAACrB,GAAG;YAAC6F,SAAS,EAAC,uBAAuB;YAAAF,QAAA,eACrCtE,OAAA;cAAMgF,uBAAuB,EAAE;gBAAEC,MAAM,EAAExF;cAAS;YAAE;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEN/E,OAAA,CAACrB,GAAG;YAAC6F,SAAS,EAAC,uBAAuB;YAACC,OAAO,EAAER,mBAAoB;YAAAK,QAAA,eACnEtE,OAAA;cAAMgF,uBAAuB,EAAE;gBAAEC,MAAM,EAAEvF;cAAW;YAAE;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGL/E,OAAA,CAACjB,MAAM;QACNsE,IAAI,EAAEnC,cAAe;QACrBsE,OAAO,EAAEnB,mBAAoB;QAAAC,QAAA,eAE7BtE,OAAA,CAACH,eAAe;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgC5B,CAAC,EAER/D,aAAa,iBACbhB,OAAA,CAACJ,iBAAiB;QACjByD,IAAI,EAAErC,aAAc;QACpBwE,OAAO,EAAErB;MAAmB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CACD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EACL,gBACA,CAAC;AAEL,CAAC;AAACxE,EAAA,CAzRIJ,iBAAmF;EAAA,QAK/DL,cAAc;AAAA;AAAAkG,EAAA,GALlC7F,iBAAmF;AA2RzF,eAAeA,iBAAiB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}