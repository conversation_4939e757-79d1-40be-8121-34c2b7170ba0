{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\ChecklistLauncherPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport useDrawerStore from '../../store/drawerStore';\nimport ChecklistPreview from \"./ChecklistPreview\";\nimport '../../styles/rtl_styles.scss';\nimport { chkcloseicon, chkicn1 } from '../../assets/icons/icons';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ChecklistLauncher = () => {\n  _s();\n  var _checklistGuideMetaDa, _checklistGuideMetaDa2, _checklistGuideMetaDa3, _checklistGuideMetaDa4, _checklistGuideMetaDa5, _checklistGuideMetaDa6, _checklistGuideMetaDa7, _checklistGuideMetaDa8, _checklistGuideMetaDa9, _checklistGuideMetaDa10, _checklistGuideMetaDa11, _checklistGuideMetaDa12, _checklistGuideMetaDa13, _checklistGuideMetaDa14, _checklistGuideMetaDa15, _checklistGuideMetaDa16, _checklistGuideMetaDa17, _checklistGuideMetaDa18, _checklistGuideMetaDa19, _checklistGuideMetaDa20, _checklistGuideMetaDa21, _checklistGuideMetaDa22, _checklistGuideMetaDa23, _checklistGuideMetaDa24, _checklistGuideMetaDa25, _checklistGuideMetaDa26, _checklistGuideMetaDa27, _checklistGuideMetaDa28, _checklistGuideMetaDa29, _checklistGuideMetaDa30, _checklistGuideMetaDa31, _checklistGuideMetaDa32, _checklistGuideMetaDa33, _checklistGuideMetaDa34, _checklistGuideMetaDa35, _checklistGuideMetaDa36, _checklistGuideMetaDa37, _checklistGuideMetaDa38, _checklistGuideMetaDa39, _checklistGuideMetaDa40, _checklistGuideMetaDa41, _checklistGuideMetaDa42;\n  const {\n    checklistGuideMetaData,\n    setShowLauncherSettings,\n    showLauncherSettings\n  } = useDrawerStore(state => state);\n  const [isOpen, setIsOpen] = useState(false);\n  const [remainingCount, setRemainingCount] = useState(\"00\"); // Initial value - will be updated with actual count\n\n  // Callback function to receive the formatted remaining count\n  const handleRemainingCountUpdate = formattedCount => {\n    console.log(\"Launcher received count update:\", formattedCount);\n    setRemainingCount(formattedCount);\n  };\n\n  // Check localStorage for remainingCount updates\n  useEffect(() => {\n    const handleStorageChange = e => {\n      if (e.key === \"remainingCount\") {\n        console.log(\"Launcher detected storage change for remainingCount:\", e.newValue);\n        if (e.newValue) {\n          setRemainingCount(e.newValue);\n        }\n      }\n    };\n\n    // Set up event listener for storage changes\n    window.addEventListener(\"storage\", handleStorageChange);\n\n    // Check for initial value\n    const storedCount = window.localStorage.getItem(\"remainingCount\");\n    if (storedCount) {\n      console.log(\"Launcher initial count from localStorage:\", storedCount);\n      setRemainingCount(storedCount);\n    }\n\n    // Set up interval to check localStorage periodically\n    const intervalId = setInterval(() => {\n      const currentCount = window.localStorage.getItem(\"remainingCount\");\n      if (currentCount && currentCount !== remainingCount) {\n        console.log(\"Launcher periodic check found new count:\", currentCount);\n        setRemainingCount(currentCount);\n      }\n    }, 1000); // Check every second\n\n    // Clean up event listener and interval\n    return () => {\n      window.removeEventListener(\"storage\", handleStorageChange);\n      clearInterval(intervalId);\n    };\n  }, [remainingCount]);\n  const [isRightPanelVisible, setIsRightPanelVisible] = useState(false); // Track right panel visibility\n  const [icons, setIcons] = useState([{\n    id: 1,\n    component: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: chkicn1\n      },\n      style: {\n        zoom: 1,\n        display: \"flex\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 5\n    }, this),\n    selected: true\n  }]);\n  const iconColor = ((_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : (_checklistGuideMetaDa2 = _checklistGuideMetaDa.launcher) === null || _checklistGuideMetaDa2 === void 0 ? void 0 : _checklistGuideMetaDa2.iconColor) || \"#fff\"; // Default to black if no color\n  const base64IconFinal = (_checklistGuideMetaDa3 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa3 === void 0 ? void 0 : (_checklistGuideMetaDa4 = _checklistGuideMetaDa3.launcher) === null || _checklistGuideMetaDa4 === void 0 ? void 0 : _checklistGuideMetaDa4.icon;\n  let base64Icon;\n  const encodeToBase64 = svgString => {\n    return `data:image/svg+xml;base64,${btoa(svgString)}`;\n  };\n  const initialSelectedIcon = icons.find(icon => icon.selected);\n  if (initialSelectedIcon) {\n    var _initialSelectedIcon$;\n    const svgElement = (_initialSelectedIcon$ = initialSelectedIcon.component.props.dangerouslySetInnerHTML) === null || _initialSelectedIcon$ === void 0 ? void 0 : _initialSelectedIcon$.__html;\n    if (svgElement) {\n      base64Icon = encodeToBase64(svgElement);\n    }\n  }\n  const modifySVGColor = (base64SVG, color) => {\n    if (!base64SVG) {\n      return \"\";\n    }\n    try {\n      // Check if the string is a valid base64 SVG\n      if (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\n        return base64SVG; // Return the original if it's not an SVG\n      }\n      const decodedSVG = atob(base64SVG.split(\",\")[1]);\n\n      // Check if this is primarily a stroke-based or fill-based icon\n      const hasStroke = decodedSVG.includes('stroke=\"');\n      const hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\n      let modifiedSVG = decodedSVG;\n      if (hasStroke && !hasColoredFill) {\n        // This is a stroke-based icon (like chkicn2-6) - only change stroke color\n        modifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\n      } else if (hasColoredFill) {\n        // This is a fill-based icon (like chkicn1) - only change fill color\n        modifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\n      } else {\n        // No existing fill or stroke, add fill to make it visible\n        modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\n        modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\n      }\n      const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\n      return modifiedBase64;\n    } catch (error) {\n      console.error(\"Error modifying SVG color:\", error);\n      return base64SVG; // Return the original if there's an error\n    }\n  };\n  const checklistColor = (_checklistGuideMetaDa5 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa5 === void 0 ? void 0 : (_checklistGuideMetaDa6 = _checklistGuideMetaDa5.canvas) === null || _checklistGuideMetaDa6 === void 0 ? void 0 : _checklistGuideMetaDa6.primaryColor;\n  const modifiedIcon = modifySVGColor(base64IconFinal ? base64IconFinal : base64Icon, iconColor);\n  // Get offset values from launcher settings, with fallback to default values\n  const xOffset = ((_checklistGuideMetaDa7 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa7 === void 0 ? void 0 : (_checklistGuideMetaDa8 = _checklistGuideMetaDa7.launcher) === null || _checklistGuideMetaDa8 === void 0 ? void 0 : (_checklistGuideMetaDa9 = _checklistGuideMetaDa8.launcherposition) === null || _checklistGuideMetaDa9 === void 0 ? void 0 : _checklistGuideMetaDa9.xaxisOffset) || \"10\";\n  const yOffset = ((_checklistGuideMetaDa10 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa10 === void 0 ? void 0 : (_checklistGuideMetaDa11 = _checklistGuideMetaDa10.launcher) === null || _checklistGuideMetaDa11 === void 0 ? void 0 : (_checklistGuideMetaDa12 = _checklistGuideMetaDa11.launcherposition) === null || _checklistGuideMetaDa12 === void 0 ? void 0 : _checklistGuideMetaDa12.yaxisOffset) || \"10\";\n  const isLeft = ((_checklistGuideMetaDa13 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa13 === void 0 ? void 0 : (_checklistGuideMetaDa14 = _checklistGuideMetaDa13.launcher) === null || _checklistGuideMetaDa14 === void 0 ? void 0 : (_checklistGuideMetaDa15 = _checklistGuideMetaDa14.launcherposition) === null || _checklistGuideMetaDa15 === void 0 ? void 0 : _checklistGuideMetaDa15.left) === true;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"absolute\",\n        bottom: `${yOffset}px`,\n        right: isLeft ? \"auto\" : `${xOffset}px`,\n        left: isLeft ? `${xOffset}px` : \"auto\",\n        zIndex: '999999'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          setIsOpen(prev => !prev);\n          setIsRightPanelVisible(true);\n        },\n        style: {\n          backgroundColor: (_checklistGuideMetaDa16 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa16 === void 0 ? void 0 : _checklistGuideMetaDa16.launcher.launcherColor,\n          color: \"white\",\n          borderRadius: ((_checklistGuideMetaDa17 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa17 === void 0 ? void 0 : _checklistGuideMetaDa17.launcher.type) === \"Text\" || ((_checklistGuideMetaDa18 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa18 === void 0 ? void 0 : _checklistGuideMetaDa18.launcher.type) === \"Icon+Txt\" ? \"16px\" : \"50%\",\n          height: \"54px\",\n          width: ((_checklistGuideMetaDa19 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa19 === void 0 ? void 0 : _checklistGuideMetaDa19.launcher.type) === \"Text\" || ((_checklistGuideMetaDa20 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa20 === void 0 ? void 0 : _checklistGuideMetaDa20.launcher.type) === \"Icon+Txt\" ? `auto` : \"54px\",\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          padding: \"8px\",\n          boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",\n          transition: \"all 0.2s ease\",\n          border: \"none\",\n          cursor: \"pointer\",\n          position: \"relative\"\n        },\n        children: [isOpen ? /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: chkcloseicon\n          },\n          style: {\n            borderRadius: \"50%\",\n            padding: \"8px\",\n            display: \"flex\",\n            cursor: \"pointer\",\n            color: \"white\",\n            stroke: \"white\",\n            fill: \"white\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 7\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [((_checklistGuideMetaDa21 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa21 === void 0 ? void 0 : (_checklistGuideMetaDa22 = _checklistGuideMetaDa21.launcher) === null || _checklistGuideMetaDa22 === void 0 ? void 0 : _checklistGuideMetaDa22.type) === \"Icon\" && /*#__PURE__*/_jsxDEV(\"img\", {\n            src: modifiedIcon,\n            alt: \"icon\",\n            style: {\n              width: \"20px\",\n              height: \"20px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 9\n          }, this), ((_checklistGuideMetaDa23 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa23 === void 0 ? void 0 : (_checklistGuideMetaDa24 = _checklistGuideMetaDa23.launcher) === null || _checklistGuideMetaDa24 === void 0 ? void 0 : _checklistGuideMetaDa24.type) === \"Text\" && ((_checklistGuideMetaDa25 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa25 === void 0 ? void 0 : (_checklistGuideMetaDa26 = _checklistGuideMetaDa25.launcher) === null || _checklistGuideMetaDa26 === void 0 ? void 0 : _checklistGuideMetaDa26.text) && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: \"16px\",\n              fontWeight: \"bold\",\n              color: (_checklistGuideMetaDa27 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa27 === void 0 ? void 0 : (_checklistGuideMetaDa28 = _checklistGuideMetaDa27.launcher) === null || _checklistGuideMetaDa28 === void 0 ? void 0 : _checklistGuideMetaDa28.textColor,\n              padding: \"8px\",\n              whiteSpace: \"nowrap\"\n            },\n            children: checklistGuideMetaData[0].launcher.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 9\n          }, this), ((_checklistGuideMetaDa29 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa29 === void 0 ? void 0 : (_checklistGuideMetaDa30 = _checklistGuideMetaDa29.launcher) === null || _checklistGuideMetaDa30 === void 0 ? void 0 : _checklistGuideMetaDa30.type) === \"Icon+Txt\" && ((_checklistGuideMetaDa31 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa31 === void 0 ? void 0 : (_checklistGuideMetaDa32 = _checklistGuideMetaDa31.launcher) === null || _checklistGuideMetaDa32 === void 0 ? void 0 : _checklistGuideMetaDa32.text) && /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: \"8px\",\n              color: (_checklistGuideMetaDa33 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa33 === void 0 ? void 0 : (_checklistGuideMetaDa34 = _checklistGuideMetaDa33.launcher) === null || _checklistGuideMetaDa34 === void 0 ? void 0 : _checklistGuideMetaDa34.textColor,\n              fontSize: \"16px\",\n              fontWeight: \"bold\",\n              padding: \"8px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: modifiedIcon,\n              alt: \"icon\",\n              style: {\n                width: \"20px\",\n                height: \"20px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 11\n            }, this), (_checklistGuideMetaDa35 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa35 === void 0 ? void 0 : (_checklistGuideMetaDa36 = _checklistGuideMetaDa35.launcher) === null || _checklistGuideMetaDa36 === void 0 ? void 0 : _checklistGuideMetaDa36.text]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 10\n          }, this)]\n        }, void 0, true), ((_checklistGuideMetaDa37 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa37 === void 0 ? void 0 : (_checklistGuideMetaDa38 = _checklistGuideMetaDa37.launcher) === null || _checklistGuideMetaDa38 === void 0 ? void 0 : _checklistGuideMetaDa38.notificationBadge) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"absolute\",\n            top: \"-8px\",\n            right: \"-8px\",\n            backgroundColor: (_checklistGuideMetaDa39 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa39 === void 0 ? void 0 : (_checklistGuideMetaDa40 = _checklistGuideMetaDa39.launcher) === null || _checklistGuideMetaDa40 === void 0 ? void 0 : _checklistGuideMetaDa40.notificationBadgeColor,\n            color: (_checklistGuideMetaDa41 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa41 === void 0 ? void 0 : (_checklistGuideMetaDa42 = _checklistGuideMetaDa41.launcher) === null || _checklistGuideMetaDa42 === void 0 ? void 0 : _checklistGuideMetaDa42.notificationTextColor,\n            fontSize: \"12px\",\n            borderRadius: \"9999px\",\n            height: \"24px\",\n            width: \"24px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: window.localStorage && window.localStorage.getItem(\"remainingCount\") || remainingCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(ChecklistPreview, {\n      data: \"\",\n      guideDetails: \"\",\n      isRightPanelVisible: isRightPanelVisible,\n      setIsRightPanelVisible: setIsRightPanelVisible,\n      isOpen: isOpen,\n      onClose: () => setIsOpen(false),\n      onRemainingCountUpdate: handleRemainingCountUpdate\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 3\n  }, this);\n};\n_s(ChecklistLauncher, \"6nF5bid8NKW82HyjTEgHrRVgyQc=\", false, function () {\n  return [useDrawerStore];\n});\n_c = ChecklistLauncher;\nexport default ChecklistLauncher;\nvar _c;\n$RefreshReg$(_c, \"ChecklistLauncher\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDrawerStore", "ChecklistPreview", "chkcloseicon", "chkicn1", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ChecklistLauncher", "_s", "_checklistGuideMetaDa", "_checklistGuideMetaDa2", "_checklistGuideMetaDa3", "_checklistGuideMetaDa4", "_checklistGuideMetaDa5", "_checklistGuideMetaDa6", "_checklistGuideMetaDa7", "_checklistGuideMetaDa8", "_checklistGuideMetaDa9", "_checklistGuideMetaDa10", "_checklistGuideMetaDa11", "_checklistGuideMetaDa12", "_checklistGuideMetaDa13", "_checklistGuideMetaDa14", "_checklistGuideMetaDa15", "_checklistGuideMetaDa16", "_checklistGuideMetaDa17", "_checklistGuideMetaDa18", "_checklistGuideMetaDa19", "_checklistGuideMetaDa20", "_checklistGuideMetaDa21", "_checklistGuideMetaDa22", "_checklistGuideMetaDa23", "_checklistGuideMetaDa24", "_checklistGuideMetaDa25", "_checklistGuideMetaDa26", "_checklistGuideMetaDa27", "_checklistGuideMetaDa28", "_checklistGuideMetaDa29", "_checklistGuideMetaDa30", "_checklistGuideMetaDa31", "_checklistGuideMetaDa32", "_checklistGuideMetaDa33", "_checklistGuideMetaDa34", "_checklistGuideMetaDa35", "_checklistGuideMetaDa36", "_checklistGuideMetaDa37", "_checklistGuideMetaDa38", "_checklistGuideMetaDa39", "_checklistGuideMetaDa40", "_checklistGuideMetaDa41", "_checklistGuideMetaDa42", "checklistGuideMetaData", "setShowLauncherSettings", "showLauncherSettings", "state", "isOpen", "setIsOpen", "remainingCount", "setRemainingCount", "handleRemainingCountUpdate", "formattedCount", "console", "log", "handleStorageChange", "e", "key", "newValue", "window", "addEventListener", "storedCount", "localStorage", "getItem", "intervalId", "setInterval", "currentCount", "removeEventListener", "clearInterval", "isRightPanelVisible", "setIsRightPanelVisible", "icons", "setIcons", "id", "component", "dangerouslySetInnerHTML", "__html", "style", "zoom", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selected", "iconColor", "launcher", "base64IconFinal", "icon", "base64Icon", "encodeToBase64", "svgString", "btoa", "initialSelectedIcon", "find", "_initialSelectedIcon$", "svgElement", "props", "modifySVGColor", "base64SVG", "color", "includes", "decodedSVG", "atob", "split", "hasStroke", "hasColoredFill", "test", "modifiedSVG", "replace", "modifiedBase64", "error", "checklistColor", "canvas", "primaryColor", "modifiedIcon", "xOffset", "launcherposition", "xaxisOffset", "yOffset", "yaxisOffset", "isLeft", "left", "children", "position", "bottom", "right", "zIndex", "onClick", "prev", "backgroundColor", "launcherColor", "borderRadius", "type", "height", "width", "alignItems", "justifyContent", "padding", "boxShadow", "transition", "border", "cursor", "stroke", "fill", "src", "alt", "text", "fontSize", "fontWeight", "textColor", "whiteSpace", "gap", "notificationBadge", "top", "notificationBadgeColor", "notificationTextColor", "data", "guideDetails", "onClose", "onRemainingCountUpdate", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/checklist/ChecklistLauncherPreview.tsx"], "sourcesContent": ["\r\nimport React, { useEffect, useMemo, useState } from 'react';\r\nimport useDrawerStore from '../../store/drawerStore';\r\nimport ChecklistPopup from \"./ChecklistPopup\";\r\nimport ChecklistPreview from \"./ChecklistPreview\";\r\nimport '../../styles/rtl_styles.scss';\r\n\r\nimport { chkcloseicon, chkicn1, closeicon, closepluginicon } from '../../assets/icons/icons';\r\nconst ChecklistLauncher = () => {\r\n\tconst { checklistGuideMetaData, setShowLauncherSettings, showLauncherSettings } = useDrawerStore(\r\n\t\t(state: any) => state\r\n\t);\r\n\tconst [isOpen, setIsOpen] = useState(false);\r\n\tconst [remainingCount, setRemainingCount] = useState(\"00\"); // Initial value - will be updated with actual count\r\n\r\n\t// Callback function to receive the formatted remaining count\r\n\tconst handleRemainingCountUpdate = (formattedCount: string) => {\r\n\t\tconsole.log(\"Launcher received count update:\", formattedCount);\r\n\t\tsetRemainingCount(formattedCount);\r\n\t};\r\n\r\n\t// Check localStorage for remainingCount updates\r\n\tuseEffect(() => {\r\n\t\tconst handleStorageChange = (e: StorageEvent) => {\r\n\t\t\tif (e.key === \"remainingCount\") {\r\n\t\t\t\tconsole.log(\"Launcher detected storage change for remainingCount:\", e.newValue);\r\n\t\t\t\tif (e.newValue) {\r\n\t\t\t\t\tsetRemainingCount(e.newValue);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Set up event listener for storage changes\r\n\t\twindow.addEventListener(\"storage\", handleStorageChange);\r\n\r\n\t\t// Check for initial value\r\n\t\tconst storedCount = window.localStorage.getItem(\"remainingCount\");\r\n\t\tif (storedCount) {\r\n\t\t\tconsole.log(\"Launcher initial count from localStorage:\", storedCount);\r\n\t\t\tsetRemainingCount(storedCount);\r\n\t\t}\r\n\r\n\t\t// Set up interval to check localStorage periodically\r\n\t\tconst intervalId = setInterval(() => {\r\n\t\t\tconst currentCount = window.localStorage.getItem(\"remainingCount\");\r\n\t\t\tif (currentCount && currentCount !== remainingCount) {\r\n\t\t\t\tconsole.log(\"Launcher periodic check found new count:\", currentCount);\r\n\t\t\t\tsetRemainingCount(currentCount);\r\n\t\t\t}\r\n\t\t}, 1000); // Check every second\r\n\r\n\t\t// Clean up event listener and interval\r\n\t\treturn () => {\r\n\t\t\twindow.removeEventListener(\"storage\", handleStorageChange);\r\n\t\t\tclearInterval(intervalId);\r\n\t\t};\r\n\t}, [remainingCount]);\r\n\tconst [isRightPanelVisible, setIsRightPanelVisible] = useState(false); // Track right panel visibility\r\n\tconst [icons, setIcons] = useState<any[]>([\r\n\t\t{\r\n\t\t\tid: 1,\r\n\t\t\tcomponent: (\r\n\t\t\t\t<span\r\n\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn1 }}\r\n\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t/>\r\n\t\t\t),\r\n\t\t\tselected: true,\r\n\t\t},\r\n\t]);\r\n\tconst iconColor = checklistGuideMetaData[0]?.launcher?.iconColor || \"#fff\"; // Default to black if no color\r\n\tconst base64IconFinal = checklistGuideMetaData[0]?.launcher?.icon;\r\n\r\n\tlet base64Icon: any;\r\n\tconst encodeToBase64 = (svgString: string) => {\r\n\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t};\r\n\tconst initialSelectedIcon = icons.find((icon) => icon.selected);\r\n\tif (initialSelectedIcon) {\r\n\t\tconst svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\r\n\t\tif (svgElement) {\r\n\t\t\tbase64Icon = encodeToBase64(svgElement);\r\n\t\t}\r\n\t}\r\n\tconst modifySVGColor = (base64SVG: any, color: any) => {\r\n\t\tif (!base64SVG) {\r\n\t\t\treturn \"\";\r\n\t\t}\r\n\r\n\t\ttry {\r\n\t\t\t// Check if the string is a valid base64 SVG\r\n\t\t\tif (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\r\n\t\t\t\treturn base64SVG; // Return the original if it's not an SVG\r\n\t\t\t}\r\n\r\n\t\t\tconst decodedSVG = atob(base64SVG.split(\",\")[1]);\r\n\r\n\t\t\t// Check if this is primarily a stroke-based or fill-based icon\r\n\t\t\tconst hasStroke = decodedSVG.includes('stroke=\"');\r\n\t\t\tconst hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\r\n\r\n\t\t\tlet modifiedSVG = decodedSVG;\r\n\r\n\t\t\tif (hasStroke && !hasColoredFill) {\r\n\t\t\t\t// This is a stroke-based icon (like chkicn2-6) - only change stroke color\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\r\n\t\t\t} else if (hasColoredFill) {\r\n\t\t\t\t// This is a fill-based icon (like chkicn1) - only change fill color\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\r\n\t\t\t} else {\r\n\t\t\t\t// No existing fill or stroke, add fill to make it visible\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\r\n\t\t\t\tmodifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\r\n\t\t\t}\r\n\r\n\t\t\tconst modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\r\n\t\t\treturn modifiedBase64;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"Error modifying SVG color:\", error);\r\n\t\t\treturn base64SVG; // Return the original if there's an error\r\n\t\t}\r\n\t};\r\n\tconst checklistColor = checklistGuideMetaData[0]?.canvas?.primaryColor;\r\n\r\n\tconst modifiedIcon = modifySVGColor(base64IconFinal ? base64IconFinal : base64Icon, iconColor);\r\n\t// Get offset values from launcher settings, with fallback to default values\r\n\tconst xOffset = checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || \"10\";\r\n\tconst yOffset = checklistGuideMetaData[0]?.launcher?.launcherposition?.yaxisOffset || \"10\";\r\n\tconst isLeft = checklistGuideMetaData[0]?.launcher?.launcherposition?.left === true;\r\n\t\r\n\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t<div\r\n style={{\r\n\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\tbottom: `${yOffset}px`,\r\n\t\t\t\t\tright: isLeft ? \"auto\" : `${xOffset}px`,\r\n\t\t\t\t\tleft: isLeft ? `${xOffset}px` : \"auto\",\r\n\t\t\t\t\tzIndex: '999999',\r\n\t\t\t\t}}\r\n>\r\n\t\t\t\t<button\r\n\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\tsetIsOpen((prev) => !prev);\r\n\t\t\t\t\t\tsetIsRightPanelVisible(true);\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.launcher.launcherColor,\r\n\t\t\t\t\t\tcolor: \"white\",\r\n\t\t\t\t\t\tborderRadius:\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Text\" ||\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Icon+Txt\"\r\n\t\t\t\t\t\t\t\t? \"16px\"\r\n\t\t\t\t\t\t\t\t: \"50%\",\r\n\t\t\t\t\t\theight: \"54px\",\r\n\t\t\t\t\t\twidth:\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Text\" ||\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Icon+Txt\"\r\n\t\t\t\t\t\t\t\t? `auto`\r\n\t\t\t\t\t\t\t\t: \"54px\",\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\tboxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",\r\n\t\t\t\t\t\ttransition: \"all 0.2s ease\",\r\n\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t{isOpen ? (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkcloseicon }}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\tcolor: \"white\",\r\n\t\t\t\t\t\t\t\tstroke: \"white\",\r\n\t\t\t\t\t\t\t\tfill: \"white\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t) : (\r\n\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.type === \"Icon\" && (\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={modifiedIcon}\r\n\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.type === \"Text\" && checklistGuideMetaData[0]?.launcher?.text && (\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.launcher?.textColor,\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{checklistGuideMetaData[0].launcher.text}\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.type === \"Icon+Txt\" &&\r\n\t\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher?.text && (\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tgap: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.launcher?.textColor,\r\n\t\t\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\tsrc={modifiedIcon}\r\n\t\t\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.text}\r\n\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Notification Badge */}\r\n\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.notificationBadge && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\ttop: \"-8px\",\r\n\t\t\t\t\t\t\t\tright: \"-8px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.launcher?.notificationBadgeColor,\r\n\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.launcher?.notificationTextColor,\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\twidth: \"24px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{/* Display the remaining count (number of incomplete steps) */}\r\n\t\t\t\t\t\t\t{(window.localStorage && window.localStorage.getItem(\"remainingCount\")) || remainingCount}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</button>\r\n\t\t\t</div>\r\n\r\n\t\t\t<ChecklistPreview\r\n\t\t\t\tdata={\"\"}\r\n\t\t\t\tguideDetails={\"\"}\r\n\t\t\t\tisRightPanelVisible={isRightPanelVisible}\r\n\t\t\t\tsetIsRightPanelVisible={setIsRightPanelVisible}\r\n\t\t\t\tisOpen={isOpen}\r\n\t\t\t\tonClose={() => setIsOpen(false)}\r\n\t\t\t\tonRemainingCountUpdate={handleRemainingCountUpdate}\r\n\t\t\t/>\r\n\t\t</div>\r\n\t);\r\n};\r\nexport default ChecklistLauncher;"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAWC,QAAQ,QAAQ,OAAO;AAC3D,OAAOC,cAAc,MAAM,yBAAyB;AAEpD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAO,8BAA8B;AAErC,SAASC,YAAY,EAAEC,OAAO,QAAoC,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC7F,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EAC/B,MAAM;IAAEC,sBAAsB;IAAEC,uBAAuB;IAAEC;EAAqB,CAAC,GAAGtD,cAAc,CAC9FuD,KAAU,IAAKA,KACjB,CAAC;EACD,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE5D;EACA,MAAM6D,0BAA0B,GAAIC,cAAsB,IAAK;IAC9DC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,cAAc,CAAC;IAC9DF,iBAAiB,CAACE,cAAc,CAAC;EAClC,CAAC;;EAED;EACA/D,SAAS,CAAC,MAAM;IACf,MAAMkE,mBAAmB,GAAIC,CAAe,IAAK;MAChD,IAAIA,CAAC,CAACC,GAAG,KAAK,gBAAgB,EAAE;QAC/BJ,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEE,CAAC,CAACE,QAAQ,CAAC;QAC/E,IAAIF,CAAC,CAACE,QAAQ,EAAE;UACfR,iBAAiB,CAACM,CAAC,CAACE,QAAQ,CAAC;QAC9B;MACD;IACD,CAAC;;IAED;IACAC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEL,mBAAmB,CAAC;;IAEvD;IACA,MAAMM,WAAW,GAAGF,MAAM,CAACG,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IACjE,IAAIF,WAAW,EAAE;MAChBR,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEO,WAAW,CAAC;MACrEX,iBAAiB,CAACW,WAAW,CAAC;IAC/B;;IAEA;IACA,MAAMG,UAAU,GAAGC,WAAW,CAAC,MAAM;MACpC,MAAMC,YAAY,GAAGP,MAAM,CAACG,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;MAClE,IAAIG,YAAY,IAAIA,YAAY,KAAKjB,cAAc,EAAE;QACpDI,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEY,YAAY,CAAC;QACrEhB,iBAAiB,CAACgB,YAAY,CAAC;MAChC;IACD,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV;IACA,OAAO,MAAM;MACZP,MAAM,CAACQ,mBAAmB,CAAC,SAAS,EAAEZ,mBAAmB,CAAC;MAC1Da,aAAa,CAACJ,UAAU,CAAC;IAC1B,CAAC;EACF,CAAC,EAAE,CAACf,cAAc,CAAC,CAAC;EACpB,MAAM,CAACoB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvE,MAAM,CAACiF,KAAK,EAAEC,QAAQ,CAAC,GAAGlF,QAAQ,CAAQ,CACzC;IACCmF,EAAE,EAAE,CAAC;IACLC,SAAS,eACR9E,OAAA;MACC+E,uBAAuB,EAAE;QAAEC,MAAM,EAAElF;MAAQ,CAAE;MAC7CmF,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACD;IACDC,QAAQ,EAAE;EACX,CAAC,CACD,CAAC;EACF,MAAMC,SAAS,GAAG,EAAApF,qBAAA,GAAA0C,sBAAsB,CAAC,CAAC,CAAC,cAAA1C,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2BqF,QAAQ,cAAApF,sBAAA,uBAAnCA,sBAAA,CAAqCmF,SAAS,KAAI,MAAM,CAAC,CAAC;EAC5E,MAAME,eAAe,IAAApF,sBAAA,GAAGwC,sBAAsB,CAAC,CAAC,CAAC,cAAAxC,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BmF,QAAQ,cAAAlF,sBAAA,uBAAnCA,sBAAA,CAAqCoF,IAAI;EAEjE,IAAIC,UAAe;EACnB,MAAMC,cAAc,GAAIC,SAAiB,IAAK;IAC7C,OAAO,6BAA6BC,IAAI,CAACD,SAAS,CAAC,EAAE;EACtD,CAAC;EACD,MAAME,mBAAmB,GAAGtB,KAAK,CAACuB,IAAI,CAAEN,IAAI,IAAKA,IAAI,CAACJ,QAAQ,CAAC;EAC/D,IAAIS,mBAAmB,EAAE;IAAA,IAAAE,qBAAA;IACxB,MAAMC,UAAU,IAAAD,qBAAA,GAAGF,mBAAmB,CAACnB,SAAS,CAACuB,KAAK,CAACtB,uBAAuB,cAAAoB,qBAAA,uBAA3DA,qBAAA,CAA6DnB,MAAM;IAEtF,IAAIoB,UAAU,EAAE;MACfP,UAAU,GAAGC,cAAc,CAACM,UAAU,CAAC;IACxC;EACD;EACA,MAAME,cAAc,GAAGA,CAACC,SAAc,EAAEC,KAAU,KAAK;IACtD,IAAI,CAACD,SAAS,EAAE;MACf,OAAO,EAAE;IACV;IAEA,IAAI;MACH;MACA,IAAI,CAACA,SAAS,CAACE,QAAQ,CAAC,4BAA4B,CAAC,EAAE;QACtD,OAAOF,SAAS,CAAC,CAAC;MACnB;MAEA,MAAMG,UAAU,GAAGC,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEhD;MACA,MAAMC,SAAS,GAAGH,UAAU,CAACD,QAAQ,CAAC,UAAU,CAAC;MACjD,MAAMK,cAAc,GAAG,uBAAuB,CAACC,IAAI,CAACL,UAAU,CAAC;MAE/D,IAAIM,WAAW,GAAGN,UAAU;MAE5B,IAAIG,SAAS,IAAI,CAACC,cAAc,EAAE;QACjC;QACAE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,iBAAiB,EAAE,WAAWT,KAAK,GAAG,CAAC;MAC1E,CAAC,MAAM,IAAIM,cAAc,EAAE;QAC1B;QACAE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,uBAAuB,EAAE,SAAST,KAAK,GAAG,CAAC;MAC9E,CAAC,MAAM;QACN;QACAQ,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,sBAAsB,EAAE,eAAeT,KAAK,GAAG,CAAC;QAClFQ,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,qBAAqB,EAAE,cAAcT,KAAK,GAAG,CAAC;MACjF;MAEA,MAAMU,cAAc,GAAG,6BAA6BlB,IAAI,CAACgB,WAAW,CAAC,EAAE;MACvE,OAAOE,cAAc;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACf1D,OAAO,CAAC0D,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAOZ,SAAS,CAAC,CAAC;IACnB;EACD,CAAC;EACD,MAAMa,cAAc,IAAA3G,sBAAA,GAAGsC,sBAAsB,CAAC,CAAC,CAAC,cAAAtC,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2B4G,MAAM,cAAA3G,sBAAA,uBAAjCA,sBAAA,CAAmC4G,YAAY;EAEtE,MAAMC,YAAY,GAAGjB,cAAc,CAACX,eAAe,GAAGA,eAAe,GAAGE,UAAU,EAAEJ,SAAS,CAAC;EAC9F;EACA,MAAM+B,OAAO,GAAG,EAAA7G,sBAAA,GAAAoC,sBAAsB,CAAC,CAAC,CAAC,cAAApC,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2B+E,QAAQ,cAAA9E,sBAAA,wBAAAC,sBAAA,GAAnCD,sBAAA,CAAqC6G,gBAAgB,cAAA5G,sBAAA,uBAArDA,sBAAA,CAAuD6G,WAAW,KAAI,IAAI;EAC1F,MAAMC,OAAO,GAAG,EAAA7G,uBAAA,GAAAiC,sBAAsB,CAAC,CAAC,CAAC,cAAAjC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B4E,QAAQ,cAAA3E,uBAAA,wBAAAC,uBAAA,GAAnCD,uBAAA,CAAqC0G,gBAAgB,cAAAzG,uBAAA,uBAArDA,uBAAA,CAAuD4G,WAAW,KAAI,IAAI;EAC1F,MAAMC,MAAM,GAAG,EAAA5G,uBAAA,GAAA8B,sBAAsB,CAAC,CAAC,CAAC,cAAA9B,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2ByE,QAAQ,cAAAxE,uBAAA,wBAAAC,uBAAA,GAAnCD,uBAAA,CAAqCuG,gBAAgB,cAAAtG,uBAAA,uBAArDA,uBAAA,CAAuD2G,IAAI,MAAK,IAAI;EAGnF,oBACC9H,OAAA;IAAA+H,QAAA,gBACC/H,OAAA;MACFiF,KAAK,EAAE;QACH+C,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,GAAGN,OAAO,IAAI;QACtBO,KAAK,EAAEL,MAAM,GAAG,MAAM,GAAG,GAAGL,OAAO,IAAI;QACvCM,IAAI,EAAED,MAAM,GAAG,GAAGL,OAAO,IAAI,GAAG,MAAM;QACtCW,MAAM,EAAE;MACT,CAAE;MAAAJ,QAAA,eAEF/H,OAAA;QACCoI,OAAO,EAAEA,CAAA,KAAM;UACdhF,SAAS,CAAEiF,IAAI,IAAK,CAACA,IAAI,CAAC;UAC1B3D,sBAAsB,CAAC,IAAI,CAAC;QAC7B,CAAE;QACFO,KAAK,EAAE;UACNqD,eAAe,GAAAlH,uBAAA,GAAE2B,sBAAsB,CAAC,CAAC,CAAC,cAAA3B,uBAAA,uBAAzBA,uBAAA,CAA2BsE,QAAQ,CAAC6C,aAAa;UAClE/B,KAAK,EAAE,OAAO;UACdgC,YAAY,EACX,EAAAnH,uBAAA,GAAA0B,sBAAsB,CAAC,CAAC,CAAC,cAAA1B,uBAAA,uBAAzBA,uBAAA,CAA2BqE,QAAQ,CAAC+C,IAAI,MAAK,MAAM,IACnD,EAAAnH,uBAAA,GAAAyB,sBAAsB,CAAC,CAAC,CAAC,cAAAzB,uBAAA,uBAAzBA,uBAAA,CAA2BoE,QAAQ,CAAC+C,IAAI,MAAK,UAAU,GACpD,MAAM,GACN,KAAK;UACTC,MAAM,EAAE,MAAM;UACdC,KAAK,EACJ,EAAApH,uBAAA,GAAAwB,sBAAsB,CAAC,CAAC,CAAC,cAAAxB,uBAAA,uBAAzBA,uBAAA,CAA2BmE,QAAQ,CAAC+C,IAAI,MAAK,MAAM,IACnD,EAAAjH,uBAAA,GAAAuB,sBAAsB,CAAC,CAAC,CAAC,cAAAvB,uBAAA,uBAAzBA,uBAAA,CAA2BkE,QAAQ,CAAC+C,IAAI,MAAK,UAAU,GACpD,MAAM,GACN,MAAM;UACVtD,OAAO,EAAE,MAAM;UACfyD,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,OAAO,EAAE,KAAK;UACdC,SAAS,EAAE,yEAAyE;UACpFC,UAAU,EAAE,eAAe;UAC3BC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,SAAS;UACjBlB,QAAQ,EAAE;QACX,CAAE;QAAAD,QAAA,GAED5E,MAAM,gBACNnD,OAAA;UACC+E,uBAAuB,EAAE;YAAEC,MAAM,EAAEnF;UAAa,CAAE;UAClDoF,KAAK,EAAE;YACNuD,YAAY,EAAE,KAAK;YACnBM,OAAO,EAAE,KAAK;YACd3D,OAAO,EAAE,MAAM;YACf+D,MAAM,EAAE,SAAS;YACjB1C,KAAK,EAAE,OAAO;YACd2C,MAAM,EAAE,OAAO;YACfC,IAAI,EAAE;UACP;QAAE;UAAAhE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,gBAEFvF,OAAA,CAAAE,SAAA;UAAA6H,QAAA,GACE,EAAAtG,uBAAA,GAAAsB,sBAAsB,CAAC,CAAC,CAAC,cAAAtB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BiE,QAAQ,cAAAhE,uBAAA,uBAAnCA,uBAAA,CAAqC+G,IAAI,MAAK,MAAM,iBACpDzI,OAAA;YACCqJ,GAAG,EAAE9B,YAAa;YAClB+B,GAAG,EAAC,MAAM;YACVrE,KAAK,EAAE;cAAE0D,KAAK,EAAE,MAAM;cAAED,MAAM,EAAE;YAAO;UAAE;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CACD,EAEA,EAAA5D,uBAAA,GAAAoB,sBAAsB,CAAC,CAAC,CAAC,cAAApB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B+D,QAAQ,cAAA9D,uBAAA,uBAAnCA,uBAAA,CAAqC6G,IAAI,MAAK,MAAM,MAAA5G,uBAAA,GAAIkB,sBAAsB,CAAC,CAAC,CAAC,cAAAlB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B6D,QAAQ,cAAA5D,uBAAA,uBAAnCA,uBAAA,CAAqCyH,IAAI,kBACjGvJ,OAAA;YACCiF,KAAK,EAAE;cACNuE,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,MAAM;cAClBjD,KAAK,GAAAzE,uBAAA,GAAEgB,sBAAsB,CAAC,CAAC,CAAC,cAAAhB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B2D,QAAQ,cAAA1D,uBAAA,uBAAnCA,uBAAA,CAAqC0H,SAAS;cACrDZ,OAAO,EAAE,KAAK;cACda,UAAU,EAAE;YACb,CAAE;YAAA5B,QAAA,EAEDhF,sBAAsB,CAAC,CAAC,CAAC,CAAC2C,QAAQ,CAAC6D;UAAI;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CACN,EAEA,EAAAtD,uBAAA,GAAAc,sBAAsB,CAAC,CAAC,CAAC,cAAAd,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2ByD,QAAQ,cAAAxD,uBAAA,uBAAnCA,uBAAA,CAAqCuG,IAAI,MAAK,UAAU,MAAAtG,uBAAA,GACxDY,sBAAsB,CAAC,CAAC,CAAC,cAAAZ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BuD,QAAQ,cAAAtD,uBAAA,uBAAnCA,uBAAA,CAAqCmH,IAAI,kBACxCvJ,OAAA;YACCiF,KAAK,EAAE;cACNE,OAAO,EAAE,MAAM;cACfyD,UAAU,EAAE,QAAQ;cACpBgB,GAAG,EAAE,KAAK;cACVpD,KAAK,GAAAnE,uBAAA,GAAEU,sBAAsB,CAAC,CAAC,CAAC,cAAAV,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BqD,QAAQ,cAAApD,uBAAA,uBAAnCA,uBAAA,CAAqCoH,SAAS;cACrDF,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,MAAM;cAClBX,OAAO,EAAE;YACV,CAAE;YAAAf,QAAA,gBAEF/H,OAAA;cACCqJ,GAAG,EAAE9B,YAAa;cAClB+B,GAAG,EAAC,MAAM;cACVrE,KAAK,EAAE;gBAAE0D,KAAK,EAAE,MAAM;gBAAED,MAAM,EAAE;cAAO;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,GAAAhD,uBAAA,GACDQ,sBAAsB,CAAC,CAAC,CAAC,cAAAR,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BmD,QAAQ,cAAAlD,uBAAA,uBAAnCA,uBAAA,CAAqC+G,IAAI;UAAA;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CACN;QAAA,eACD,CACF,EAGA,EAAA9C,uBAAA,GAAAM,sBAAsB,CAAC,CAAC,CAAC,cAAAN,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BiD,QAAQ,cAAAhD,uBAAA,uBAAnCA,uBAAA,CAAqCmH,iBAAiB,kBACtD7J,OAAA;UACCiF,KAAK,EAAE;YACN+C,QAAQ,EAAE,UAAU;YACpB8B,GAAG,EAAE,MAAM;YACX5B,KAAK,EAAE,MAAM;YACbI,eAAe,GAAA3F,uBAAA,GAAEI,sBAAsB,CAAC,CAAC,CAAC,cAAAJ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B+C,QAAQ,cAAA9C,uBAAA,uBAAnCA,uBAAA,CAAqCmH,sBAAsB;YAC5EvD,KAAK,GAAA3D,uBAAA,GAAEE,sBAAsB,CAAC,CAAC,CAAC,cAAAF,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B6C,QAAQ,cAAA5C,uBAAA,uBAAnCA,uBAAA,CAAqCkH,qBAAqB;YACjER,QAAQ,EAAE,MAAM;YAChBhB,YAAY,EAAE,QAAQ;YACtBE,MAAM,EAAE,MAAM;YACdC,KAAK,EAAE,MAAM;YACbxD,OAAO,EAAE,MAAM;YACfyD,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UACjB,CAAE;UAAAd,QAAA,EAGAhE,MAAM,CAACG,YAAY,IAAIH,MAAM,CAACG,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAKd;QAAc;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENvF,OAAA,CAACJ,gBAAgB;MAChBqK,IAAI,EAAE,EAAG;MACTC,YAAY,EAAE,EAAG;MACjBzF,mBAAmB,EAAEA,mBAAoB;MACzCC,sBAAsB,EAAEA,sBAAuB;MAC/CvB,MAAM,EAAEA,MAAO;MACfgH,OAAO,EAAEA,CAAA,KAAM/G,SAAS,CAAC,KAAK,CAAE;MAChCgH,sBAAsB,EAAE7G;IAA2B;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAER,CAAC;AAACnF,EAAA,CAtQID,iBAAiB;EAAA,QAC4DR,cAAc;AAAA;AAAA0K,EAAA,GAD3FlK,iBAAiB;AAuQvB,eAAeA,iBAAiB;AAAC,IAAAkK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}