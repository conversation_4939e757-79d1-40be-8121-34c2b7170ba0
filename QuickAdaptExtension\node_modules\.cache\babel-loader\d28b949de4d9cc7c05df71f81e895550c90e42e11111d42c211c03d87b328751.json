{"ast": null, "code": "import { create } from \"zustand\";\nimport { devtools, persist } from \"zustand/middleware\";\nimport { immer } from \"zustand/middleware/immer\";\n\n// Define the UserInfo interface for state\n\n// Zustand store with typed state\nconst useInfoStore = create()(devtools(persist(immer(set => ({\n  accessToken: \"\",\n  oidcInfo: {},\n  user: {},\n  orgDetails: {},\n  userType: \"\",\n  orgLanguages: [],\n  // <-- add this\n  userRoles: {},\n  setAccessToken: token => {\n    set(state => {\n      state.accessToken = token;\n    });\n  },\n  clearAccessToken: () => {\n    set(state => {\n      state.accessToken = \"\";\n    });\n  },\n  setOidcInfo: info => {\n    set(state => {\n      state.oidcInfo = info;\n    });\n  },\n  setUser: user => {\n    set(state => {\n      state.user = user;\n    });\n  },\n  setOrgDetails: orgDetails => {\n    set(state => {\n      state.orgDetails = orgDetails;\n    });\n  },\n  clearAll: () => {\n    // Reset all fields to initial values\n    set(state => {\n      state.accessToken = \"\";\n      state.oidcInfo = {};\n      state.user = null;\n      state.orgDetails = {};\n      state.userType = \"\";\n      state.orgLanguages = []; // <-- add this\n      state.userRoles = {};\n    });\n  },\n  setUserType: userType => {\n    set(state => {\n      state.userType = userType;\n    });\n  },\n  setOrgLanguages: languages => {\n    set(state => {\n      state.orgLanguages = languages;\n    });\n  },\n  setUserRoles: userRoles => {\n    set(state => {\n      state.userRoles = userRoles;\n    });\n  }\n})), {\n  name: \"user-info-storage\",\n  // unique name for localStorage\n  partialize: state => ({\n    accessToken: state.accessToken,\n    oidcInfo: state.oidcInfo,\n    user: state.user,\n    orgDetails: state.orgDetails,\n    userType: state.userType,\n    orgLanguages: state.orgLanguages,\n    userRoles: state.userRoles\n  }) // Persist these fields\n})));\nexport default useInfoStore;", "map": {"version": 3, "names": ["create", "devtools", "persist", "immer", "useInfoStore", "set", "accessToken", "oidcInfo", "user", "orgDetails", "userType", "orgLanguages", "userRoles", "setAccessToken", "token", "state", "clearAccessToken", "setOidcInfo", "info", "setUser", "setOrgDetails", "clearAll", "setUserType", "setOrgLanguages", "languages", "setUserRoles", "name", "partialize"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/store/UserInfoStore.ts"], "sourcesContent": ["import { LoginUserInfo } from './../models/LoginUserInfo';\r\nimport { create } from \"zustand\";\r\nimport { devtools, persist } from \"zustand/middleware\";\r\nimport { immer } from \"zustand/middleware/immer\";\r\nimport { User } from '../models/User';\r\nimport { Organization } from '../models/Organization';\r\n\r\n// Define the UserInfo interface for state\r\ninterface UserInfo {\r\n  accessToken: string;\r\n  oidcInfo: any;\r\n  user: User | null;\r\n  orgDetails: Organization;\r\n  userType: string; // added userType here\r\n  orgLanguages: string[]; // <-- add this\r\n  userRoles: any;\r\n  setAccessToken: (token: string) => void;\r\n  clearAccessToken: () => void;\r\n  setOidcInfo: (info: any) => void;\r\n  setUser: (user: User) => void;\r\n  setOrgDetails: (orgDetails: Organization) => void;\r\n  setUserType: (userType: string) => void;\r\n  setOrgLanguages: (languages: string[]) => void; // <-- add this\r\n  clearAll: () => void; \r\n  setUserRoles: (userRoles: any) => void;\r\n}\r\n\r\n// Zustand store with typed state\r\nconst useInfoStore = create<UserInfo>()(\r\n  devtools(\r\n    persist(\r\n      immer((set) => ({\r\n        accessToken: \"\",\r\n        oidcInfo: {},\r\n        user: {} as User,\r\n        orgDetails: {} as Organization,\r\n        userType: \"\",\r\n        orgLanguages: [], // <-- add this\r\n        userRoles: {} as any,\r\n\r\n        setAccessToken: (token: string) => {\r\n          set((state) => {\r\n            state.accessToken = token;\r\n          });\r\n        },\r\n\r\n        clearAccessToken: () => {\r\n          set((state) => {\r\n            state.accessToken = \"\";\r\n          });\r\n        },\r\n            \r\n        setOidcInfo: (info: any) => {\r\n          set((state) => {\r\n            state.oidcInfo = info;\r\n          });\r\n        },\r\n\r\n        setUser: (user: User | null) => {\r\n          set((state) => {\r\n            state.user = user;\r\n          });\r\n        },\r\n\r\n        setOrgDetails: (orgDetails: Organization) => {\r\n          set((state) => {\r\n            state.orgDetails = orgDetails;\r\n          });\r\n        },\r\n        clearAll: () => {  // Reset all fields to initial values\r\n            set((state) => {\r\n              state.accessToken = \"\";\r\n              state.oidcInfo = {};\r\n              state.user = null;\r\n              state.orgDetails = {} as Organization;\r\n              state.userType = \"\";\r\n              state.orgLanguages = []; // <-- add this\r\n              state.userRoles = {};\r\n            });\r\n          },\r\n        setUserType: (userType: string) => {\r\n          set((state) => {\r\n            state.userType = userType;\r\n          });\r\n        },\r\n        setOrgLanguages: (languages: string[]) => {\r\n          set((state) => {\r\n            state.orgLanguages = languages;\r\n          });\r\n        },\r\n        setUserRoles: (userRoles: any) => {\r\n          set((state) => {\r\n            state.userRoles = userRoles;\r\n          })\r\n        }\r\n      })),\r\n      {\r\n        name: \"user-info-storage\", // unique name for localStorage\r\n        partialize: (state) => ({\r\n          accessToken: state.accessToken,\r\n          oidcInfo: state.oidcInfo,\r\n          user: state.user,\r\n          orgDetails: state.orgDetails,\r\n          userType: state.userType,\r\n          orgLanguages: state.orgLanguages, \r\n          userRoles: state.userRoles,\r\n        }), // Persist these fields\r\n      }\r\n    )\r\n  )\r\n);\r\n\r\nexport default useInfoStore;\r\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,QAAQ,EAAEC,OAAO,QAAQ,oBAAoB;AACtD,SAASC,KAAK,QAAQ,0BAA0B;;AAIhD;;AAoBA;AACA,MAAMC,YAAY,GAAGJ,MAAM,CAAW,CAAC,CACrCC,QAAQ,CACNC,OAAO,CACLC,KAAK,CAAEE,GAAG,KAAM;EACdC,WAAW,EAAE,EAAE;EACfC,QAAQ,EAAE,CAAC,CAAC;EACZC,IAAI,EAAE,CAAC,CAAS;EAChBC,UAAU,EAAE,CAAC,CAAiB;EAC9BC,QAAQ,EAAE,EAAE;EACZC,YAAY,EAAE,EAAE;EAAE;EAClBC,SAAS,EAAE,CAAC,CAAQ;EAEpBC,cAAc,EAAGC,KAAa,IAAK;IACjCT,GAAG,CAAEU,KAAK,IAAK;MACbA,KAAK,CAACT,WAAW,GAAGQ,KAAK;IAC3B,CAAC,CAAC;EACJ,CAAC;EAEDE,gBAAgB,EAAEA,CAAA,KAAM;IACtBX,GAAG,CAAEU,KAAK,IAAK;MACbA,KAAK,CAACT,WAAW,GAAG,EAAE;IACxB,CAAC,CAAC;EACJ,CAAC;EAEDW,WAAW,EAAGC,IAAS,IAAK;IAC1Bb,GAAG,CAAEU,KAAK,IAAK;MACbA,KAAK,CAACR,QAAQ,GAAGW,IAAI;IACvB,CAAC,CAAC;EACJ,CAAC;EAEDC,OAAO,EAAGX,IAAiB,IAAK;IAC9BH,GAAG,CAAEU,KAAK,IAAK;MACbA,KAAK,CAACP,IAAI,GAAGA,IAAI;IACnB,CAAC,CAAC;EACJ,CAAC;EAEDY,aAAa,EAAGX,UAAwB,IAAK;IAC3CJ,GAAG,CAAEU,KAAK,IAAK;MACbA,KAAK,CAACN,UAAU,GAAGA,UAAU;IAC/B,CAAC,CAAC;EACJ,CAAC;EACDY,QAAQ,EAAEA,CAAA,KAAM;IAAG;IACfhB,GAAG,CAAEU,KAAK,IAAK;MACbA,KAAK,CAACT,WAAW,GAAG,EAAE;MACtBS,KAAK,CAACR,QAAQ,GAAG,CAAC,CAAC;MACnBQ,KAAK,CAACP,IAAI,GAAG,IAAI;MACjBO,KAAK,CAACN,UAAU,GAAG,CAAC,CAAiB;MACrCM,KAAK,CAACL,QAAQ,GAAG,EAAE;MACnBK,KAAK,CAACJ,YAAY,GAAG,EAAE,CAAC,CAAC;MACzBI,KAAK,CAACH,SAAS,GAAG,CAAC,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC;EACHU,WAAW,EAAGZ,QAAgB,IAAK;IACjCL,GAAG,CAAEU,KAAK,IAAK;MACbA,KAAK,CAACL,QAAQ,GAAGA,QAAQ;IAC3B,CAAC,CAAC;EACJ,CAAC;EACDa,eAAe,EAAGC,SAAmB,IAAK;IACxCnB,GAAG,CAAEU,KAAK,IAAK;MACbA,KAAK,CAACJ,YAAY,GAAGa,SAAS;IAChC,CAAC,CAAC;EACJ,CAAC;EACDC,YAAY,EAAGb,SAAc,IAAK;IAChCP,GAAG,CAAEU,KAAK,IAAK;MACbA,KAAK,CAACH,SAAS,GAAGA,SAAS;IAC7B,CAAC,CAAC;EACJ;AACF,CAAC,CAAC,CAAC,EACH;EACEc,IAAI,EAAE,mBAAmB;EAAE;EAC3BC,UAAU,EAAGZ,KAAK,KAAM;IACtBT,WAAW,EAAES,KAAK,CAACT,WAAW;IAC9BC,QAAQ,EAAEQ,KAAK,CAACR,QAAQ;IACxBC,IAAI,EAAEO,KAAK,CAACP,IAAI;IAChBC,UAAU,EAAEM,KAAK,CAACN,UAAU;IAC5BC,QAAQ,EAAEK,KAAK,CAACL,QAAQ;IACxBC,YAAY,EAAEI,KAAK,CAACJ,YAAY;IAChCC,SAAS,EAAEG,KAAK,CAACH;EACnB,CAAC,CAAC,CAAE;AACN,CACF,CACF,CACF,CAAC;AAED,eAAeR,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}