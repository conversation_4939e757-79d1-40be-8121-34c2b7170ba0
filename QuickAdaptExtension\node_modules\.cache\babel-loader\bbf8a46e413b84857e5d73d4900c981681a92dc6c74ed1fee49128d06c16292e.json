{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\ChecklistPopup.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport ChecklistCircle from \"./ChecklistCheckIcon\";\nimport useDrawerStore from '../../store/drawerStore';\nimport LauncherSettings from './LauncherSettings';\nimport ImageCarousel from \"./ImageCarousel\";\nimport VideoPlayer from \"./VideoPlayer\";\nimport { editInteractionName } from './Chekpoints';\nimport { closepluginicon, maximize, chkicn1, chkdefault } from '../../assets/icons/icons';\nimport AlertPopup from '../drawer/AlertPopup';\nimport '../../styles/rtl_styles.scss';\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// Function to modify the color of an SVG icon\nconst modifySVGColor = (base64SVG, color) => {\n  if (!base64SVG) {\n    return \"\";\n  }\n  try {\n    // Check if the string is a valid base64 SVG\n    if (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\n      return base64SVG; // Return the original if it's not an SVG\n    }\n    const decodedSVG = atob(base64SVG.split(\",\")[1]);\n\n    // Check if this is primarily a stroke-based or fill-based icon\n    const hasStroke = decodedSVG.includes('stroke=\"');\n    const hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\n    let modifiedSVG = decodedSVG;\n    if (hasStroke && !hasColoredFill) {\n      // This is a stroke-based icon (like chkicn2-6) - only change stroke color\n      modifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\n    } else if (hasColoredFill) {\n      // This is a fill-based icon (like chkicn1) - only change fill color\n      modifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\n    } else {\n      // No existing fill or stroke, add fill to make it visible\n      modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\n      modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\n    }\n    const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\n    return modifiedBase64;\n  } catch (error) {\n    console.error(\"Error modifying SVG color:\", error);\n    return base64SVG; // Return the original if there's an error\n  }\n};\nconst ChecklistPopup = ({\n  isOpen,\n  onClose,\n  onRemainingCountUpdate,\n  data,\n  guideDetails,\n  setopenWarning,\n  handleLeave\n}) => {\n  _s();\n  var _checklistGuideMetaDa, _checklistGuideMetaDa2, _checklistGuideMetaDa3, _checkpointslistData$, _checklistGuideMetaDa4, _checklistGuideMetaDa5, _checklistGuideMetaDa6, _checklistGuideMetaDa7, _checklistGuideMetaDa8, _checklistGuideMetaDa9, _checklistGuideMetaDa10, _checklistGuideMetaDa11, _checklistGuideMetaDa12, _checklistGuideMetaDa13, _checklistGuideMetaDa14, _checklistGuideMetaDa15, _checklistGuideMetaDa16, _checklistGuideMetaDa17, _checklistGuideMetaDa18, _checklistGuideMetaDa19, _checklistGuideMetaDa20, _checklistGuideMetaDa21, _checklistGuideMetaDa22, _checklistGuideMetaDa23, _checklistGuideMetaDa24, _checklistGuideMetaDa25, _checklistGuideMetaDa26, _checklistGuideMetaDa27, _checklistGuideMetaDa28, _checklistGuideMetaDa29, _checklistGuideMetaDa30, _checklistGuideMetaDa31, _checklistGuideMetaDa32, _checklistGuideMetaDa33, _checklistGuideMetaDa34, _checklistGuideMetaDa35, _checklistGuideMetaDa36, _checklistGuideMetaDa37, _checklistGuideMetaDa38, _checklistGuideMetaDa39, _checklistGuideMetaDa40, _checklistGuideMetaDa41, _checklistGuideMetaDa42, _selectedItem$support, _selectedItem$support2, _checklistGuideMetaDa49, _checklistGuideMetaDa50, _selectedItem$support3, _checklistGuideMetaDa51, _checklistGuideMetaDa52;\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    checklistGuideMetaData,\n    checkpointsEditPopup,\n    isUnSavedChanges,\n    openWarning\n  } = useDrawerStore(state => state);\n  const initialCompletedStatus = {};\n\n  // State to track which steps are completed\n  const [completedStatus, setCompletedStatus] = useState({});\n\n  // Map checkpoints and set the first one as completed by default\n  const checkpointslistData = ((_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : (_checklistGuideMetaDa2 = _checklistGuideMetaDa.checkpoints) === null || _checklistGuideMetaDa2 === void 0 ? void 0 : (_checklistGuideMetaDa3 = _checklistGuideMetaDa2.checkpointsList) === null || _checklistGuideMetaDa3 === void 0 ? void 0 : _checklistGuideMetaDa3.map((checkpoint, index) => ({\n    ...checkpoint,\n    completed: index === 0 ? true : false\n  }))) || [];\n  const [checklistItems, setChecklistItems] = useState(checkpointslistData);\n  const [activeItem, setActiveItem] = useState(checkpointslistData.length > 0 && !checkpointsEditPopup ? (_checkpointslistData$ = checkpointslistData[0]) === null || _checkpointslistData$ === void 0 ? void 0 : _checkpointslistData$.id : \"default-placeholder\");\n  const checklistColor = (_checklistGuideMetaDa4 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa4 === void 0 ? void 0 : (_checklistGuideMetaDa5 = _checklistGuideMetaDa4.canvas) === null || _checklistGuideMetaDa5 === void 0 ? void 0 : _checklistGuideMetaDa5.primaryColor;\n  function getLocalizedTitleSubTitle(ts, t) {\n    const defaultTitle = \"Checklist Title\";\n    const defaultSubTitle = \"Context about the tasks in the checklist below users should prioritize completing.\";\n    return {\n      title: !(ts !== null && ts !== void 0 && ts.title) || ts.title === defaultTitle ? t(defaultTitle, {\n        defaultValue: defaultTitle\n      }) : ts.title,\n      subTitle: !(ts !== null && ts !== void 0 && ts.subTitle) || ts.subTitle === defaultSubTitle ? t(defaultSubTitle, {\n        defaultValue: defaultSubTitle\n      }) : ts.subTitle\n    };\n  }\n  const localizedTitleSubTitle = getLocalizedTitleSubTitle((_checklistGuideMetaDa6 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa6 === void 0 ? void 0 : _checklistGuideMetaDa6.TitleSubTitle, translate);\n  {/*this is here what i did is i am getting the title and subtitle from the checklistGuideMetaData and then i am using it for modifying inital translation of the title and subtitle so if you want to modify data such as color and all go to store and look for checklistGuideMetaData */}\n\n  // Initialize completedStatus when checkpointslistData changes\n  useEffect(() => {\n    if (checkpointslistData.length > 0) {\n      const initialCompletedStatus = {};\n      checkpointslistData.forEach((item, index) => {\n        // Set the first item as completed by default, others as not completed\n        initialCompletedStatus[item.id] = index === 0;\n      });\n\n      // console.log(\"Initializing completedStatus:\", initialCompletedStatus);\n      setCompletedStatus(initialCompletedStatus);\n\n      // Calculate and update the initial remaining count\n      const remainingItems = checkpointslistData.length - 1; // All items except the first one\n      const formattedCount = remainingItems.toString().padStart(2, \"0\");\n      // console.log(\"Initial remaining count set to:\", formattedCount);\n\n      // Update localStorage directly\n      if (window.localStorage) {\n        window.localStorage.setItem(\"remainingCount\", formattedCount);\n      }\n\n      // Call the callback\n      if (onRemainingCountUpdate) {\n        onRemainingCountUpdate(formattedCount);\n      }\n    }\n  }, [checkpointslistData, onRemainingCountUpdate, completedStatus]);\n  useEffect(() => {\n    document.documentElement.style.setProperty(\"--chkcolor\", checklistColor);\n  }, [checklistColor]);\n  useEffect(() => {\n    if (checkpointslistData.length === 0) {\n      setActiveItem(\"default-placeholder\");\n    } else {\n      var _checkpointslistData$2;\n      setActiveItem((_checkpointslistData$2 = checkpointslistData[0]) === null || _checkpointslistData$2 === void 0 ? void 0 : _checkpointslistData$2.id);\n    }\n  }, [checkpointslistData.length == 1]);\n  const [icons, setIcons] = useState([{\n    id: 1,\n    component: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: chkicn1\n      },\n      style: {\n        zoom: 1,\n        display: \"flex\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 5\n    }, this),\n    selected: true\n  }]);\n  const encodeToBase64 = svgString => {\n    return `data:image/svg+xml;base64,${btoa(svgString)}`;\n  };\n  let base64Icon;\n  const initialSelectedIcon = icons.find(icon => icon.selected);\n  if (initialSelectedIcon && checkpointslistData.length == 0) {\n    var _initialSelectedIcon$;\n    const svgElement = (_initialSelectedIcon$ = initialSelectedIcon.component.props.dangerouslySetInnerHTML) === null || _initialSelectedIcon$ === void 0 ? void 0 : _initialSelectedIcon$.__html;\n    if (svgElement) {\n      base64Icon = encodeToBase64(svgElement);\n    }\n  }\n  const iconColor = ((_checklistGuideMetaDa7 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa7 === void 0 ? void 0 : (_checklistGuideMetaDa8 = _checklistGuideMetaDa7.launcher) === null || _checklistGuideMetaDa8 === void 0 ? void 0 : _checklistGuideMetaDa8.iconColor) || \"#fff\"; // Default to black if no color\n  const base64IconFinal = ((_checklistGuideMetaDa9 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa9 === void 0 ? void 0 : (_checklistGuideMetaDa10 = _checklistGuideMetaDa9.launcher) === null || _checklistGuideMetaDa10 === void 0 ? void 0 : _checklistGuideMetaDa10.icon) || base64Icon;\n  const totalItems = checkpointslistData.length || 1;\n  const progress = 1;\n\n  // Update the remaining count whenever completedStatus or checkpointslistData changes\n  useEffect(() => {\n    if (checkpointslistData.length > 0 && Object.keys(completedStatus).length > 0) {\n      // Count the number of incomplete steps\n      const remainingItems = checkpointslistData.length - Object.values(completedStatus).filter(status => status).length;\n      // Format with leading zero (01, 02, 03, etc.)\n      const formattedCount = remainingItems.toString().padStart(2, \"0\");\n      // console.log(\"ChecklistPopup updating count to:\", formattedCount, \"from completedStatus:\", completedStatus);\n\n      // Make sure the callback is called with the updated count\n      if (onRemainingCountUpdate) {\n        onRemainingCountUpdate(formattedCount);\n      }\n\n      // Also update the count in the drawerStore to ensure it's available to all components\n      if (window.localStorage) {\n        window.localStorage.setItem(\"remainingCount\", formattedCount);\n      }\n    }\n  }, [completedStatus, checkpointslistData, onRemainingCountUpdate]);\n  const toggleItemCompletion = id => {\n    setCompletedStatus(prevStatus => {\n      const newStatus = {\n        ...prevStatus,\n        [id]: !prevStatus[id]\n      };\n\n      // Update the remaining count immediately\n      if (checkpointslistData.length > 0) {\n        const remainingItems = checkpointslistData.length - Object.values(newStatus).filter(status => status).length;\n        const formattedCount = remainingItems.toString().padStart(2, \"0\");\n        console.log(\"toggleItemCompletion updating count to:\", formattedCount);\n\n        // Update localStorage directly\n        if (window.localStorage) {\n          window.localStorage.setItem(\"remainingCount\", formattedCount);\n        }\n\n        // Call the callback\n        if (onRemainingCountUpdate) {\n          onRemainingCountUpdate(formattedCount);\n        }\n      }\n      return newStatus;\n    });\n  };\n\n  // Mark the active item as completed\n  const handleMarkAsCompleted = () => {\n    if (activeItem && activeItem !== \"default-placeholder\") {\n      setCompletedStatus(prevStatus => {\n        const newStatus = {\n          ...prevStatus,\n          [activeItem]: true\n        };\n\n        // Update the remaining count immediately\n        if (checkpointslistData.length > 0) {\n          const remainingItems = checkpointslistData.length - Object.values(newStatus).filter(status => status).length;\n          const formattedCount = remainingItems.toString().padStart(2, \"0\");\n          console.log(\"handleMarkAsCompleted updating count to:\", formattedCount);\n\n          // Update localStorage directly\n          if (window.localStorage) {\n            window.localStorage.setItem(\"remainingCount\", formattedCount);\n          }\n\n          // Call the callback\n          if (onRemainingCountUpdate) {\n            onRemainingCountUpdate(formattedCount);\n          }\n        }\n        console.log(\"Marked item as completed:\", activeItem);\n        return newStatus;\n      });\n    }\n  };\n  const handleSelect = id => {\n    setActiveItem(id);\n  };\n  const handleClose = () => {\n    var _checkpointslistData$3;\n    onClose();\n    setActiveItem((_checkpointslistData$3 = checkpointslistData[0]) === null || _checkpointslistData$3 === void 0 ? void 0 : _checkpointslistData$3.id);\n  };\n  if (!isOpen) return null;\n  if (!isOpen) return null;\n  const selectedItem = checkpointslistData.length > 0 ? checkpointslistData.find(item => checkpointsEditPopup ? item.id === editInteractionName : item.id === activeItem) || {\n    id: \"default-placeholder\",\n    title: translate(\"Step Title\", {\n      defaultValue: \"Step Title\"\n    }),\n    description: translate(\"Step Description\", {\n      defaultValue: \"Step Description\"\n    }),\n    icon: base64Icon,\n    mediaTitle: translate(\"Media Title\", {\n      defaultValue: \"Media Title\"\n    }),\n    mediaDescription: translate(\"Media Description\", {\n      defaultValue: \"Media Description\"\n    }),\n    supportingMedia: []\n  } : {\n    id: \"default-placeholder\",\n    title: translate(\"Step Title\", {\n      defaultValue: \"Step Title\"\n    }),\n    description: translate(\"Step Description\", {\n      defaultValue: \"Step Description\"\n    }),\n    icon: base64Icon,\n    mediaTitle: translate(\"Media Title\", {\n      defaultValue: \"Media Title\"\n    }),\n    mediaDescription: translate(\"Media Description\", {\n      defaultValue: \"Media Description\"\n    }),\n    supportingMedia: []\n  };\n  const handleNavigate = () => {\n    // window.open(\"http://localhost:3000/\", '_blank');\n  };\n  const isRTL = document.documentElement.getAttribute('dir') === 'rtl' || document.body.getAttribute('dir') === 'rtl';\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isUnSavedChanges && openWarning && /*#__PURE__*/_jsxDEV(AlertPopup, {\n      openWarning: openWarning,\n      setopenWarning: setopenWarning,\n      handleLeave: handleLeave\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"fixed\",\n        inset: 0,\n        display: \"flex\",\n        alignItems: \"center\",\n        // justifyContent: 'center',\n        zIndex: 99999\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: \"absolute\",\n          inset: 0,\n          backgroundColor: \"rgba(0, 0, 0, 0.3)\"\n        },\n        onClick: handleClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-chkpopup\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: (_checklistGuideMetaDa11 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa11 === void 0 ? void 0 : (_checklistGuideMetaDa12 = _checklistGuideMetaDa11.canvas) === null || _checklistGuideMetaDa12 === void 0 ? void 0 : _checklistGuideMetaDa12.backgroundColor,\n            border: `${(_checklistGuideMetaDa13 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa13 === void 0 ? void 0 : (_checklistGuideMetaDa14 = _checklistGuideMetaDa13.canvas) === null || _checklistGuideMetaDa14 === void 0 ? void 0 : _checklistGuideMetaDa14.borderWidth}px solid ${(_checklistGuideMetaDa15 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa15 === void 0 ? void 0 : (_checklistGuideMetaDa16 = _checklistGuideMetaDa15.canvas) === null || _checklistGuideMetaDa16 === void 0 ? void 0 : _checklistGuideMetaDa16.borderColor}`,\n            borderRadius: `${(_checklistGuideMetaDa17 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa17 === void 0 ? void 0 : (_checklistGuideMetaDa18 = _checklistGuideMetaDa17.canvas) === null || _checklistGuideMetaDa18 === void 0 ? void 0 : _checklistGuideMetaDa18.cornerRadius}px`,\n            width: `${((_checklistGuideMetaDa19 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa19 === void 0 ? void 0 : (_checklistGuideMetaDa20 = _checklistGuideMetaDa19.canvas) === null || _checklistGuideMetaDa20 === void 0 ? void 0 : _checklistGuideMetaDa20.width) || 930}px`,\n            height: `${((_checklistGuideMetaDa21 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa21 === void 0 ? void 0 : (_checklistGuideMetaDa22 = _checklistGuideMetaDa21.canvas) === null || _checklistGuideMetaDa22 === void 0 ? void 0 : _checklistGuideMetaDa22.height) || 500}px`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              height: \"100%\",\n              width: \"100%\",\n              overflow: \"auto hidden\"\n            },\n            className: \"qadpt-chkcontent\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-chkrgt\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  flexDirection: \"column\",\n                  gap: \"16px\",\n                  borderBottom: \"1px solid #E8E8E8\",\n                  padding: \"24px 24px 16px 24px\",\n                  textAlign: isRTL ? 'right' : 'left'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    gap: \"6px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: \"20px\",\n                      fontWeight: (_checklistGuideMetaDa23 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa23 !== void 0 && (_checklistGuideMetaDa24 = _checklistGuideMetaDa23.TitleSubTitle) !== null && _checklistGuideMetaDa24 !== void 0 && _checklistGuideMetaDa24.titleBold ? \"bold\" : \"normal\",\n                      fontStyle: (_checklistGuideMetaDa25 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa25 !== void 0 && (_checklistGuideMetaDa26 = _checklistGuideMetaDa25.TitleSubTitle) !== null && _checklistGuideMetaDa26 !== void 0 && _checklistGuideMetaDa26.titleItalic ? \"italic\" : \"normal\",\n                      color: ((_checklistGuideMetaDa27 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa27 === void 0 ? void 0 : (_checklistGuideMetaDa28 = _checklistGuideMetaDa27.TitleSubTitle) === null || _checklistGuideMetaDa28 === void 0 ? void 0 : _checklistGuideMetaDa28.titleColor) || \"#333\",\n                      display: \"block\",\n                      textOverflow: \"ellipsis\",\n                      whiteSpace: \"nowrap\",\n                      wordBreak: \"break-word\",\n                      overflow: \"hidden\"\n                    },\n                    children: localizedTitleSubTitle.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: \"14px\",\n                      fontWeight: (_checklistGuideMetaDa29 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa29 !== void 0 && (_checklistGuideMetaDa30 = _checklistGuideMetaDa29.TitleSubTitle) !== null && _checklistGuideMetaDa30 !== void 0 && _checklistGuideMetaDa30.subTitleBold ? \"bold\" : \"normal\",\n                      fontStyle: (_checklistGuideMetaDa31 = checklistGuideMetaData[0]) !== null && _checklistGuideMetaDa31 !== void 0 && (_checklistGuideMetaDa32 = _checklistGuideMetaDa31.TitleSubTitle) !== null && _checklistGuideMetaDa32 !== void 0 && _checklistGuideMetaDa32.subTitleItalic ? \"italic\" : \"normal\",\n                      color: ((_checklistGuideMetaDa33 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa33 === void 0 ? void 0 : (_checklistGuideMetaDa34 = _checklistGuideMetaDa33.TitleSubTitle) === null || _checklistGuideMetaDa34 === void 0 ? void 0 : _checklistGuideMetaDa34.subTitleColor) || \"#8D8D8D\"\n                    },\n                    className: \"qadpt-subtl\",\n                    children: localizedTitleSubTitle.subTitle\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      justifyContent: \"space-between\",\n                      marginBottom: \"8px\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: \"14px\",\n                        color: \"#6b7280\"\n                      },\n                      children: [progress, \"/\", totalItems]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 422,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      height: \"8px\",\n                      backgroundColor: \"#e5e7eb\",\n                      borderRadius: \"9999px\",\n                      overflow: \"hidden\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: \"100%\",\n                        backgroundColor: (_checklistGuideMetaDa35 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa35 === void 0 ? void 0 : (_checklistGuideMetaDa36 = _checklistGuideMetaDa35.canvas) === null || _checklistGuideMetaDa36 === void 0 ? void 0 : _checklistGuideMetaDa36.primaryColor,\n                        borderRadius: \"9999px\",\n                        width: `${progress / totalItems * 100}%`\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 11\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  overflow: \"auto\",\n                  maxHeight: `calc(${((_checklistGuideMetaDa37 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa37 === void 0 ? void 0 : (_checklistGuideMetaDa38 = _checklistGuideMetaDa37.canvas) === null || _checklistGuideMetaDa38 === void 0 ? void 0 : _checklistGuideMetaDa38.height) || 500}px - 190px)`\n                },\n                children: checkpointslistData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `${activeItem === \"default-placeholder\" ? \"qadpt-chkstp\" : \"\"}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"qadpt-chkstpctn\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\",\n                        width: \"100%\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: \"flex\",\n                          alignItems: \"center\",\n                          gap: \"8px\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                          src: base64Icon,\n                          alt: \"icon\",\n                          style: {\n                            width: \"20px\",\n                            height: \"20px\"\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 464,\n                          columnNumber: 15\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            color: (_checklistGuideMetaDa39 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa39 === void 0 ? void 0 : (_checklistGuideMetaDa40 = _checklistGuideMetaDa39.checkpoints) === null || _checklistGuideMetaDa40 === void 0 ? void 0 : _checklistGuideMetaDa40.checkpointTitles\n                          },\n                          children: translate(\"Step Title\", {\n                            defaultValue: \"Step Title\"\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 469,\n                          columnNumber: 15\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 462,\n                        columnNumber: 14\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: /*#__PURE__*/_jsxDEV(ChecklistCircle, {\n                          completed: true,\n                          onClick: () => {},\n                          size: \"sm\"\n                        }, \"default\", false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 474,\n                          columnNumber: 15\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 473,\n                        columnNumber: 14\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 13\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        marginTop: \"8px\",\n                        gap: \"8px\"\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          fontSize: \"14px\",\n                          color: (_checklistGuideMetaDa41 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa41 === void 0 ? void 0 : (_checklistGuideMetaDa42 = _checklistGuideMetaDa41.checkpoints) === null || _checklistGuideMetaDa42 === void 0 ? void 0 : _checklistGuideMetaDa42.checkpointsDescription,\n                          lineHeight: \"1.5\",\n                          margin: 0,\n                          whiteSpace: \"normal\",\n                          wordBreak: \"break-word\"\n                        },\n                        children: translate(\"Step Description\", {\n                          defaultValue: \"Step Description\"\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 483,\n                        columnNumber: 14\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 13\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 11\n                }, this) : checkpointslistData === null || checkpointslistData === void 0 ? void 0 : checkpointslistData.map(item => {\n                  var _checklistGuideMetaDa43, _checklistGuideMetaDa44, _checklistGuideMetaDa45, _checklistGuideMetaDa46, _checklistGuideMetaDa47, _checklistGuideMetaDa48;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${(checkpointsEditPopup ? editInteractionName === item.id : activeItem === item.id) ? \"qadpt-chkstp\" : \"\"}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"qadpt-chkstpctn\",\n                      onClick: () => handleSelect(item.id),\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          paddingLeft: \"10px\",\n                          display: \"flex\",\n                          gap: \"6px\",\n                          flexDirection: \"column\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"space-between\",\n                            width: \"100%\"\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: \"flex\",\n                              alignItems: \"center\",\n                              gap: \"10px\",\n                              flexDirection: \"row\",\n                              width: \"calc(100% - 60px)\"\n                            },\n                            children: [item.icon && typeof item.icon === \"string\" ? /*#__PURE__*/_jsxDEV(\"img\", {\n                              src: modifySVGColor(item.icon, ((_checklistGuideMetaDa43 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa43 === void 0 ? void 0 : (_checklistGuideMetaDa44 = _checklistGuideMetaDa43.checkpoints) === null || _checklistGuideMetaDa44 === void 0 ? void 0 : _checklistGuideMetaDa44.checkpointsIcons) || \"#333\"),\n                              alt: \"icon\",\n                              style: {\n                                width: \"20px\",\n                                height: \"20px\"\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 532,\n                              columnNumber: 18\n                            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                width: \"20px\",\n                                height: \"20px\",\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"center\"\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                style: {\n                                  width: \"16px\",\n                                  height: \"16px\"\n                                }\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 547,\n                                columnNumber: 19\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 538,\n                              columnNumber: 18\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                color: ((_checklistGuideMetaDa45 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa45 === void 0 ? void 0 : (_checklistGuideMetaDa46 = _checklistGuideMetaDa45.checkpoints) === null || _checklistGuideMetaDa46 === void 0 ? void 0 : _checklistGuideMetaDa46.checkpointTitles) || \"#333\",\n                                overflow: \"hidden\",\n                                textOverflow: \"ellipsis\",\n                                whiteSpace: \"nowrap\",\n                                wordBreak: \"break-word\"\n                              },\n                              children: item.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 551,\n                              columnNumber: 17\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 522,\n                            columnNumber: 16\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: /*#__PURE__*/_jsxDEV(ChecklistCircle, {\n                              completed: completedStatus[item.id] || false,\n                              onClick: () => toggleItemCompletion(item.id),\n                              size: \"sm\"\n                            }, item.id, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 565,\n                              columnNumber: 17\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 564,\n                            columnNumber: 16\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 514,\n                          columnNumber: 15\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            style: {\n                              fontSize: \"14px\",\n                              color: (_checklistGuideMetaDa47 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa47 === void 0 ? void 0 : (_checklistGuideMetaDa48 = _checklistGuideMetaDa47.checkpoints) === null || _checklistGuideMetaDa48 === void 0 ? void 0 : _checklistGuideMetaDa48.checkpointsDescription\n                            },\n                            className: \"qadpt-chkpopdesc\",\n                            children: item.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 574,\n                            columnNumber: 16\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 573,\n                          columnNumber: 15\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 513,\n                        columnNumber: 14\n                      }, this)\n                    }, item.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 13\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 12\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: \"60%\",\n                padding: \"20px 20px 0 20px\"\n              },\n              className: \"qadpt-chklft\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  placeContent: \"end\",\n                  width: \"100%\",\n                  gap: \"6px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: maximize\n                  },\n                  style: {\n                    background: \"#e8e8e8\",\n                    borderRadius: \"50%\",\n                    padding: \"6px\",\n                    display: \"flex\",\n                    cursor: \"pointer\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  dangerouslySetInnerHTML: {\n                    __html: closepluginicon\n                  },\n                  style: {\n                    background: \"#e8e8e8\",\n                    borderRadius: \"50%\",\n                    padding: \"8px\",\n                    display: \"flex\",\n                    cursor: \"pointer\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  flexDirection: \"column\",\n                  gap: \"10px\",\n                  height: \"calc(100% - 90px)\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    overflow: \"hidden auto\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    flexDirection: \"column\",\n                    width: \"-webkit-fill-available\"\n                  },\n                  children: [(selectedItem === null || selectedItem === void 0 ? void 0 : (_selectedItem$support = selectedItem.supportingMedia) === null || _selectedItem$support === void 0 ? void 0 : _selectedItem$support.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [selectedItem.supportingMedia.some(file => {\n                      var _file$Base;\n                      return file === null || file === void 0 ? void 0 : (_file$Base = file.Base64) === null || _file$Base === void 0 ? void 0 : _file$Base.startsWith(\"data:image\");\n                    }) && /*#__PURE__*/_jsxDEV(ImageCarousel, {\n                      selectedItem: selectedItem,\n                      activeItem: activeItem,\n                      images: selectedItem.supportingMedia.filter(file => {\n                        var _file$Base2;\n                        return file === null || file === void 0 ? void 0 : (_file$Base2 = file.Base64) === null || _file$Base2 === void 0 ? void 0 : _file$Base2.startsWith(\"data:image\");\n                      }).map(file => file.Base64),\n                      isMaximized: \"\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 13\n                    }, this), selectedItem.supportingMedia.some(file => {\n                      var _file$Base3;\n                      return file === null || file === void 0 ? void 0 : (_file$Base3 = file.Base64) === null || _file$Base3 === void 0 ? void 0 : _file$Base3.startsWith(\"data:video\");\n                    }) && selectedItem.supportingMedia.filter(file => {\n                      var _file$Base4;\n                      return file === null || file === void 0 ? void 0 : (_file$Base4 = file.Base64) === null || _file$Base4 === void 0 ? void 0 : _file$Base4.startsWith(\"data:video\");\n                    }).map((file, index) => /*#__PURE__*/_jsxDEV(VideoPlayer, {\n                      videoFile: file.Base64,\n                      isMaximized: \"\"\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 663,\n                      columnNumber: 15\n                    }, this))]\n                  }, void 0, true), ((selectedItem === null || selectedItem === void 0 ? void 0 : (_selectedItem$support2 = selectedItem.supportingMedia) === null || _selectedItem$support2 === void 0 ? void 0 : _selectedItem$support2.length) === 0 || !(selectedItem !== null && selectedItem !== void 0 && selectedItem.supportingMedia)) && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"auto\",\n                      height: \"244px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      dangerouslySetInnerHTML: {\n                        __html: chkdefault\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 673,\n                      columnNumber: 12\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        color: \"#8D8D8D\"\n                      },\n                      children: translate('Check tasks, stay organized, and finish strong!', {\n                        defaultValue: 'Check tasks, stay organized, and finish strong!'\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 12\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: \"100%\",\n                      marginTop: \"10px\"\n                    },\n                    className: \"qadpt-chkdesc\",\n                    children: selectedItem && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        height: \"100%\",\n                        display: \"flex\",\n                        flexDirection: \"column\"\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          textAlign: isRTL ? \"right\" : \"left\",\n                          display: \"flex\",\n                          flexDirection: \"column\",\n                          gap: \"12px\"\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            fontSize: \"16px\",\n                            fontWeight: 600,\n                            color: \"#333\",\n                            overflow: \"hidden\",\n                            textOverflow: \"ellipsis\",\n                            whiteSpace: \"nowrap\",\n                            wordBreak: \"break-word\"\n                          },\n                          children: selectedItem.mediaTitle\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 692,\n                          columnNumber: 14\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"qadpt-desc\",\n                          style: {\n                            color: \"#8D8D8D\"\n                          },\n                          children: selectedItem.mediaDescription\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 706,\n                          columnNumber: 14\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 684,\n                        columnNumber: 13\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 10\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 10\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 630,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  gap: \"12px\",\n                  alignItems: \"center\",\n                  placeContent: \"end\",\n                  paddingBottom: \"20px\"\n                },\n                className: \"qadpt-btnsec\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    backgroundColor: (_checklistGuideMetaDa49 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa49 === void 0 ? void 0 : (_checklistGuideMetaDa50 = _checklistGuideMetaDa49.canvas) === null || _checklistGuideMetaDa50 === void 0 ? void 0 : _checklistGuideMetaDa50.primaryColor,\n                    borderRadius: \"10px\",\n                    padding: \"9px 16px\",\n                    color: \"#fff\",\n                    border: \"none\",\n                    cursor: \"pointer\"\n                  },\n                  children: translate('Take Tour', {\n                    defaultValue: 'Take Tour'\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 10\n                }, this), (selectedItem === null || selectedItem === void 0 ? void 0 : (_selectedItem$support3 = selectedItem.supportingMedia) === null || _selectedItem$support3 === void 0 ? void 0 : _selectedItem$support3.length) > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    borderRadius: \"10px\",\n                    padding: \"9px 16px\",\n                    color: (_checklistGuideMetaDa51 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa51 === void 0 ? void 0 : (_checklistGuideMetaDa52 = _checklistGuideMetaDa51.canvas) === null || _checklistGuideMetaDa52 === void 0 ? void 0 : _checklistGuideMetaDa52.primaryColor,\n                    border: \"none\",\n                    background: \"#D3D9DA\",\n                    cursor: \"pointer\"\n                  },\n                  onClick: handleMarkAsCompleted,\n                  children: translate('Mark as Completed', {\n                    defaultValue: 'Mark as Completed'\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(ChecklistPopup, \"2GB1KQ0VJEmX6ifHqwuJFKoLaKA=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ChecklistPopup;\nconst ChecklistApp = ({\n  setopenWarning,\n  handleLeave\n}) => {\n  _s2();\n  var _checklistGuideMetaDa53, _checklistGuideMetaDa54, _checklistGuideMetaDa55, _checklistGuideMetaDa56, _checklistGuideMetaDa57, _checklistGuideMetaDa58, _checklistGuideMetaDa59, _checklistGuideMetaDa60, _checklistGuideMetaDa61, _checklistGuideMetaDa62, _checklistGuideMetaDa63, _checklistGuideMetaDa64, _checklistGuideMetaDa65, _checklistGuideMetaDa66, _checklistGuideMetaDa67, _checklistGuideMetaDa68, _checklistGuideMetaDa69, _checklistGuideMetaDa70, _checklistGuideMetaDa71, _checklistGuideMetaDa72, _checklistGuideMetaDa73, _checklistGuideMetaDa74, _checklistGuideMetaDa75, _checklistGuideMetaDa76, _checklistGuideMetaDa77, _checklistGuideMetaDa78, _checklistGuideMetaDa79, _checklistGuideMetaDa80, _checklistGuideMetaDa81, _checklistGuideMetaDa82, _checklistGuideMetaDa83, _checklistGuideMetaDa84, _checklistGuideMetaDa85;\n  const {\n    checklistGuideMetaData,\n    setShowLauncherSettings,\n    showLauncherSettings,\n    setOpenWarning\n  } = useDrawerStore(state => state);\n  let base64Icon;\n  const [icons, setIcons] = useState([{\n    id: 1,\n    component: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: chkicn1\n      },\n      style: {\n        zoom: 1,\n        display: \"flex\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 775,\n      columnNumber: 5\n    }, this),\n    selected: true\n  }]);\n  const encodeToBase64 = svgString => {\n    return `data:image/svg+xml;base64,${btoa(svgString)}`;\n  };\n  const iconColor = ((_checklistGuideMetaDa53 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa53 === void 0 ? void 0 : (_checklistGuideMetaDa54 = _checklistGuideMetaDa53.launcher) === null || _checklistGuideMetaDa54 === void 0 ? void 0 : _checklistGuideMetaDa54.iconColor) || \"#fff\"; // Default to black if no color\n  const base64IconFinal = (_checklistGuideMetaDa55 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa55 === void 0 ? void 0 : (_checklistGuideMetaDa56 = _checklistGuideMetaDa55.launcher) === null || _checklistGuideMetaDa56 === void 0 ? void 0 : _checklistGuideMetaDa56.icon;\n  const initialSelectedIcon = icons.find(icon => icon.selected);\n  if (initialSelectedIcon) {\n    var _initialSelectedIcon$2;\n    const svgElement = (_initialSelectedIcon$2 = initialSelectedIcon.component.props.dangerouslySetInnerHTML) === null || _initialSelectedIcon$2 === void 0 ? void 0 : _initialSelectedIcon$2.__html;\n    if (svgElement) {\n      base64Icon = encodeToBase64(svgElement);\n    }\n  }\n  // Using the modifySVGColor function defined at the top of the file\n  const modifiedIcon = modifySVGColor(base64IconFinal || base64Icon, iconColor);\n  const [isOpen, setIsOpen] = useState(true);\n  const [remainingCount, setRemainingCount] = useState(\"00\");\n  const handleRemainingCountUpdate = formattedCount => {\n    setRemainingCount(formattedCount);\n  };\n  const [anchorEl, setAnchorEl] = useState(document.body);\n  const [currentStep, setCurrentStep] = useState(0);\n  const [isAnnouncementOpen, setAnnouncementOpen] = useState(false);\n  const [scrollPercentage, setScrollPercentage] = useState(0);\n  const currentUrl = window.location.href;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-chklayout\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          setIsOpen(true);\n          setShowLauncherSettings(true);\n        },\n        style: {\n          backgroundColor: (_checklistGuideMetaDa57 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa57 === void 0 ? void 0 : _checklistGuideMetaDa57.launcher.launcherColor,\n          color: \"white\",\n          borderRadius: ((_checklistGuideMetaDa58 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa58 === void 0 ? void 0 : _checklistGuideMetaDa58.launcher.type) === \"Text\" || ((_checklistGuideMetaDa59 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa59 === void 0 ? void 0 : _checklistGuideMetaDa59.launcher.type) === \"Icon+Txt\" ? \"16px\" : \"50%\",\n          height: \"54px\",\n          width: ((_checklistGuideMetaDa60 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa60 === void 0 ? void 0 : _checklistGuideMetaDa60.launcher.type) === \"Text\" || ((_checklistGuideMetaDa61 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa61 === void 0 ? void 0 : _checklistGuideMetaDa61.launcher.type) === \"Icon+Txt\" ? `auto` : \"54px\",\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          padding: \"8px\",\n          boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",\n          transition: \"all 0.2s ease\",\n          border: \"none\",\n          cursor: \"pointer\",\n          position: \"relative\"\n        },\n        children: [((_checklistGuideMetaDa62 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa62 === void 0 ? void 0 : (_checklistGuideMetaDa63 = _checklistGuideMetaDa62.launcher) === null || _checklistGuideMetaDa63 === void 0 ? void 0 : _checklistGuideMetaDa63.type) === \"Icon\" && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: modifiedIcon,\n          alt: \"icon\",\n          style: {\n            width: \"20px\",\n            height: \"20px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 7\n        }, this), ((_checklistGuideMetaDa64 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa64 === void 0 ? void 0 : (_checklistGuideMetaDa65 = _checklistGuideMetaDa64.launcher) === null || _checklistGuideMetaDa65 === void 0 ? void 0 : _checklistGuideMetaDa65.type) === \"Text\" && ((_checklistGuideMetaDa66 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa66 === void 0 ? void 0 : (_checklistGuideMetaDa67 = _checklistGuideMetaDa66.launcher) === null || _checklistGuideMetaDa67 === void 0 ? void 0 : _checklistGuideMetaDa67.text) && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: \"16px\",\n            fontWeight: \"bold\",\n            color: (_checklistGuideMetaDa68 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa68 === void 0 ? void 0 : (_checklistGuideMetaDa69 = _checklistGuideMetaDa68.launcher) === null || _checklistGuideMetaDa69 === void 0 ? void 0 : _checklistGuideMetaDa69.textColor,\n            padding: \"8px\",\n            whiteSpace: \"nowrap\"\n          },\n          children: checklistGuideMetaData[0].launcher.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 860,\n          columnNumber: 7\n        }, this), ((_checklistGuideMetaDa70 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa70 === void 0 ? void 0 : (_checklistGuideMetaDa71 = _checklistGuideMetaDa70.launcher) === null || _checklistGuideMetaDa71 === void 0 ? void 0 : _checklistGuideMetaDa71.type) === \"Icon+Txt\" && ((_checklistGuideMetaDa72 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa72 === void 0 ? void 0 : (_checklistGuideMetaDa73 = _checklistGuideMetaDa72.launcher) === null || _checklistGuideMetaDa73 === void 0 ? void 0 : _checklistGuideMetaDa73.text) && ((_checklistGuideMetaDa74 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa74 === void 0 ? void 0 : (_checklistGuideMetaDa75 = _checklistGuideMetaDa74.launcher) === null || _checklistGuideMetaDa75 === void 0 ? void 0 : _checklistGuideMetaDa75.icon) && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"8px\",\n            color: (_checklistGuideMetaDa76 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa76 === void 0 ? void 0 : (_checklistGuideMetaDa77 = _checklistGuideMetaDa76.launcher) === null || _checklistGuideMetaDa77 === void 0 ? void 0 : _checklistGuideMetaDa77.textColor,\n            fontSize: \"16px\",\n            fontWeight: \"bold\",\n            padding: \"8px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: modifiedIcon,\n            alt: \"icon\",\n            style: {\n              width: \"20px\",\n              height: \"20px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 9\n          }, this), (_checklistGuideMetaDa78 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa78 === void 0 ? void 0 : (_checklistGuideMetaDa79 = _checklistGuideMetaDa78.launcher) === null || _checklistGuideMetaDa79 === void 0 ? void 0 : _checklistGuideMetaDa79.text]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 8\n        }, this), ((_checklistGuideMetaDa80 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa80 === void 0 ? void 0 : (_checklistGuideMetaDa81 = _checklistGuideMetaDa80.launcher) === null || _checklistGuideMetaDa81 === void 0 ? void 0 : _checklistGuideMetaDa81.notificationBadge) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"absolute\",\n            top: \"-8px\",\n            right: \"-8px\",\n            backgroundColor: (_checklistGuideMetaDa82 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa82 === void 0 ? void 0 : (_checklistGuideMetaDa83 = _checklistGuideMetaDa82.launcher) === null || _checklistGuideMetaDa83 === void 0 ? void 0 : _checklistGuideMetaDa83.notificationBadgeColor,\n            color: (_checklistGuideMetaDa84 = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa84 === void 0 ? void 0 : (_checklistGuideMetaDa85 = _checklistGuideMetaDa84.launcher) === null || _checklistGuideMetaDa85 === void 0 ? void 0 : _checklistGuideMetaDa85.notificationTextColor,\n            fontSize: \"12px\",\n            borderRadius: \"9999px\",\n            height: \"24px\",\n            width: \"24px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: remainingCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 817,\n      columnNumber: 4\n    }, this), showLauncherSettings && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(LauncherSettings, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 923,\n        columnNumber: 6\n      }, this)\n    }, void 0, false), /*#__PURE__*/_jsxDEV(ChecklistPopup, {\n      data: \"\",\n      guideDetails: \"\",\n      isOpen: isOpen,\n      onClose: () => setIsOpen(true),\n      onRemainingCountUpdate: handleRemainingCountUpdate,\n      setopenWarning: setOpenWarning,\n      handleLeave: handleLeave\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 928,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 813,\n    columnNumber: 3\n  }, this);\n};\n_s2(ChecklistApp, \"1JT5ljjm3H+yIEPaT9akVjCVaXw=\", false, function () {\n  return [useDrawerStore];\n});\n_c2 = ChecklistApp;\nexport default ChecklistApp;\nvar _c, _c2;\n$RefreshReg$(_c, \"ChecklistPopup\");\n$RefreshReg$(_c2, \"ChecklistApp\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "ChecklistCircle", "useDrawerStore", "LauncherSettings", "ImageCarousel", "VideoPlayer", "editInteractionName", "closepluginicon", "maximize", "chkicn1", "chkdefault", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "modifySVGColor", "base64SVG", "color", "includes", "decodedSVG", "atob", "split", "hasStroke", "hasColoredFill", "test", "modifiedSVG", "replace", "modifiedBase64", "btoa", "error", "console", "ChecklistPopup", "isOpen", "onClose", "onRemainingCountUpdate", "data", "guideDetails", "setopenWarning", "handleLeave", "_s", "_checklistGuideMetaDa", "_checklistGuideMetaDa2", "_checklistGuideMetaDa3", "_checkpointslistData$", "_checklistGuideMetaDa4", "_checklistGuideMetaDa5", "_checklistGuideMetaDa6", "_checklistGuideMetaDa7", "_checklistGuideMetaDa8", "_checklistGuideMetaDa9", "_checklistGuideMetaDa10", "_checklistGuideMetaDa11", "_checklistGuideMetaDa12", "_checklistGuideMetaDa13", "_checklistGuideMetaDa14", "_checklistGuideMetaDa15", "_checklistGuideMetaDa16", "_checklistGuideMetaDa17", "_checklistGuideMetaDa18", "_checklistGuideMetaDa19", "_checklistGuideMetaDa20", "_checklistGuideMetaDa21", "_checklistGuideMetaDa22", "_checklistGuideMetaDa23", "_checklistGuideMetaDa24", "_checklistGuideMetaDa25", "_checklistGuideMetaDa26", "_checklistGuideMetaDa27", "_checklistGuideMetaDa28", "_checklistGuideMetaDa29", "_checklistGuideMetaDa30", "_checklistGuideMetaDa31", "_checklistGuideMetaDa32", "_checklistGuideMetaDa33", "_checklistGuideMetaDa34", "_checklistGuideMetaDa35", "_checklistGuideMetaDa36", "_checklistGuideMetaDa37", "_checklistGuideMetaDa38", "_checklistGuideMetaDa39", "_checklistGuideMetaDa40", "_checklistGuideMetaDa41", "_checklistGuideMetaDa42", "_selectedItem$support", "_selectedItem$support2", "_checklistGuideMetaDa49", "_checklistGuideMetaDa50", "_selectedItem$support3", "_checklistGuideMetaDa51", "_checklistGuideMetaDa52", "t", "translate", "checklistGuideMetaData", "checkpointsEditPopup", "isUnSavedChanges", "openWarning", "state", "initialCompletedStatus", "completedStatus", "setCompletedStatus", "checkpointslistData", "checkpoints", "checkpointsList", "map", "checkpoint", "index", "completed", "checklistItems", "setChecklistItems", "activeItem", "setActiveItem", "length", "id", "checklistColor", "canvas", "primaryColor", "getLocalizedTitleSubTitle", "ts", "defaultTitle", "defaultSubTitle", "title", "defaultValue", "subTitle", "localizedTitleSubTitle", "TitleSubTitle", "for<PERSON>ach", "item", "remainingItems", "formattedCount", "toString", "padStart", "window", "localStorage", "setItem", "document", "documentElement", "style", "setProperty", "_checkpointslistData$2", "icons", "setIcons", "component", "dangerouslySetInnerHTML", "__html", "zoom", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selected", "encodeToBase64", "svgString", "base64Icon", "initialSelectedIcon", "find", "icon", "_initialSelectedIcon$", "svgElement", "props", "iconColor", "launcher", "base64IconFinal", "totalItems", "progress", "Object", "keys", "values", "filter", "status", "toggleItemCompletion", "prevStatus", "newStatus", "log", "handleMarkAsCompleted", "handleSelect", "handleClose", "_checkpointslistData$3", "selectedItem", "description", "mediaTitle", "mediaDescription", "supportingMedia", "handleNavigate", "isRTL", "getAttribute", "body", "children", "position", "inset", "alignItems", "zIndex", "backgroundColor", "onClick", "className", "border", "borderWidth", "borderColor", "borderRadius", "cornerRadius", "width", "height", "overflow", "flexDirection", "gap", "borderBottom", "padding", "textAlign", "fontSize", "fontWeight", "titleBold", "fontStyle", "titleItalic", "titleColor", "textOverflow", "whiteSpace", "wordBreak", "subTitleBold", "subTitleItalic", "subTitleColor", "justifyContent", "marginBottom", "maxHeight", "src", "alt", "checkpointTitles", "size", "marginTop", "checkpointsDescription", "lineHeight", "margin", "_checklistGuideMetaDa43", "_checklistGuideMetaDa44", "_checklistGuideMetaDa45", "_checklistGuideMetaDa46", "_checklistGuideMetaDa47", "_checklistGuideMetaDa48", "paddingLeft", "checkpointsIcons", "place<PERSON><PERSON>nt", "background", "cursor", "some", "file", "_file$Base", "Base64", "startsWith", "images", "_file$Base2", "isMaximized", "_file$Base3", "_file$Base4", "videoFile", "paddingBottom", "_c", "ChecklistApp", "_s2", "_checklistGuideMetaDa53", "_checklistGuideMetaDa54", "_checklistGuideMetaDa55", "_checklistGuideMetaDa56", "_checklistGuideMetaDa57", "_checklistGuideMetaDa58", "_checklistGuideMetaDa59", "_checklistGuideMetaDa60", "_checklistGuideMetaDa61", "_checklistGuideMetaDa62", "_checklistGuideMetaDa63", "_checklistGuideMetaDa64", "_checklistGuideMetaDa65", "_checklistGuideMetaDa66", "_checklistGuideMetaDa67", "_checklistGuideMetaDa68", "_checklistGuideMetaDa69", "_checklistGuideMetaDa70", "_checklistGuideMetaDa71", "_checklistGuideMetaDa72", "_checklistGuideMetaDa73", "_checklistGuideMetaDa74", "_checklistGuideMetaDa75", "_checklistGuideMetaDa76", "_checklistGuideMetaDa77", "_checklistGuideMetaDa78", "_checklistGuideMetaDa79", "_checklistGuideMetaDa80", "_checklistGuideMetaDa81", "_checklistGuideMetaDa82", "_checklistGuideMetaDa83", "_checklistGuideMetaDa84", "_checklistGuideMetaDa85", "setShowLauncherSettings", "showLauncherSettings", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_initialSelectedIcon$2", "modifiedIcon", "setIsOpen", "remainingCount", "setRemainingCount", "handleRemainingCountUpdate", "anchorEl", "setAnchorEl", "currentStep", "setCurrentStep", "isAnnouncementOpen", "setAnnouncementOpen", "scrollPercentage", "setScrollPercentage", "currentUrl", "location", "href", "launcherColor", "type", "boxShadow", "transition", "text", "textColor", "notificationBadge", "top", "right", "notificationBadgeColor", "notificationTextColor", "_c2", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/checklist/ChecklistPopup.tsx"], "sourcesContent": ["import React, { useEffect, useMemo, useState } from 'react';\r\nimport ChecklistCircle from \"./ChecklistCheckIcon\";\r\nimport useDrawerStore from '../../store/drawerStore';\r\nimport LauncherSettings from './LauncherSettings';\r\nimport ImageCarousel from \"./ImageCarousel\";\r\nimport VideoPlayer from \"./VideoPlayer\";\r\nimport { editInteractionName } from './Chekpoints';\r\nimport { GetGudeDetailsByGuideId } from '../../services/GuideListServices';\r\nimport { closeicon, closepluginicon, maximize, chkicn1, chkdefault } from '../../assets/icons/icons';\r\nimport AlertPopup from '../drawer/AlertPopup';\r\nimport '../../styles/rtl_styles.scss';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n\r\n\r\ninterface CheckListPopupProps {\r\n  isOpen: any;\r\n  onClose: () => void;\r\n  onRemainingCountUpdate: (formattedCount: string) => void;\r\n  data: any;\r\n  guideDetails: any;\r\n  setopenWarning: any;\r\n  handleLeave: () => void;\r\n\r\n}\r\n// Function to modify the color of an SVG icon\r\nconst modifySVGColor = (base64SVG: any, color: any) => {\r\n\tif (!base64SVG) {\r\n\t\treturn \"\";\r\n\t}\r\n\r\n\ttry {\r\n\t\t// Check if the string is a valid base64 SVG\r\n\t\tif (!base64SVG.includes(\"data:image/svg+xml;base64,\")) {\r\n\t\t\treturn base64SVG; // Return the original if it's not an SVG\r\n\t\t}\r\n\r\n\t\tconst decodedSVG = atob(base64SVG.split(\",\")[1]);\r\n\r\n\t\t// Check if this is primarily a stroke-based or fill-based icon\r\n\t\tconst hasStroke = decodedSVG.includes('stroke=\"');\r\n\t\tconst hasColoredFill = /fill=\"(?!none)[^\"]+\"/g.test(decodedSVG);\r\n\r\n\t\tlet modifiedSVG = decodedSVG;\r\n\r\n\t\tif (hasStroke && !hasColoredFill) {\r\n\t\t\t// This is a stroke-based icon (like chkicn2-6) - only change stroke color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/stroke=\"[^\"]+\"/g, `stroke=\"${color}\"`);\r\n\t\t} else if (hasColoredFill) {\r\n\t\t\t// This is a fill-based icon (like chkicn1) - only change fill color\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/fill=\"(?!none)[^\"]+\"/g, `fill=\"${color}\"`);\r\n\t\t} else {\r\n\t\t\t// No existing fill or stroke, add fill to make it visible\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill=\"${color}\"`);\r\n\t\t\tmodifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill=\"${color}\"`);\r\n\t\t}\r\n\r\n\t\tconst modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;\r\n\t\treturn modifiedBase64;\r\n\t} catch (error) {\r\n\t\tconsole.error(\"Error modifying SVG color:\", error);\r\n\t\treturn base64SVG; // Return the original if there's an error\r\n\t}\r\n};\r\n\r\nconst ChecklistPopup: React.FC<CheckListPopupProps> = ({\r\n\tisOpen,\r\n\tonClose,\r\n\tonRemainingCountUpdate,\r\n\tdata,\r\n\tguideDetails,\r\n\tsetopenWarning,\r\n\thandleLeave,\r\n}) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst { checklistGuideMetaData, checkpointsEditPopup, isUnSavedChanges, openWarning } = useDrawerStore(\r\n\t\t(state: any) => state\r\n\t);\r\n\r\n\tconst initialCompletedStatus: { [key: string]: boolean } = {};\r\n\r\n\t// State to track which steps are completed\r\n\tconst [completedStatus, setCompletedStatus] = useState<{ [key: string]: boolean }>({});\r\n\r\n\t// Map checkpoints and set the first one as completed by default\r\n\tconst checkpointslistData =\r\n\t\tchecklistGuideMetaData[0]?.checkpoints?.checkpointsList?.map((checkpoint: any, index: number) => ({\r\n\t\t\t...checkpoint,\r\n\t\t\tcompleted: index === 0 ? true : false,\r\n\t\t})) || [];\r\n\r\n\tconst [checklistItems, setChecklistItems] = useState(checkpointslistData);\r\n\tconst [activeItem, setActiveItem] = useState(\r\n\t\tcheckpointslistData.length > 0 && !checkpointsEditPopup ? checkpointslistData[0]?.id : \"default-placeholder\"\r\n\t);\r\n\tconst checklistColor = checklistGuideMetaData[0]?.canvas?.primaryColor;\r\n\r\nfunction getLocalizedTitleSubTitle(ts: any, t: any) {\r\n\t  const defaultTitle = \"Checklist Title\";\r\n\t  const defaultSubTitle = \"Context about the tasks in the checklist below users should prioritize completing.\";\r\n\t  return {\r\n\t    title:\r\n\t      !ts?.title || ts.title === defaultTitle\r\n\t        ? t(defaultTitle, { defaultValue: defaultTitle })\r\n\t        : ts.title,\r\n\t    subTitle:\r\n\t      !ts?.subTitle || ts.subTitle === defaultSubTitle\r\n\t        ? t(defaultSubTitle, { defaultValue: defaultSubTitle })\r\n\t        : ts.subTitle,\r\n\t  };\r\n\t}\r\n\r\n\tconst localizedTitleSubTitle = getLocalizedTitleSubTitle(checklistGuideMetaData[0]?.TitleSubTitle, translate); {/*this is here what i did is i am getting the title and subtitle from the checklistGuideMetaData and then i am using it for modifying inital translation of the title and subtitle so if you want to modify data such as color and all go to store and look for checklistGuideMetaData */ }\r\n\r\n\t// Initialize completedStatus when checkpointslistData changes\r\n\tuseEffect(() => {\r\n\t\tif (checkpointslistData.length > 0) {\r\n\t\t\tconst initialCompletedStatus: { [key: string]: boolean } = {};\r\n\r\n\t\t\tcheckpointslistData.forEach((item: any, index: number) => {\r\n\t\t\t\t// Set the first item as completed by default, others as not completed\r\n\t\t\t\tinitialCompletedStatus[item.id] = index === 0;\r\n\t\t\t});\r\n\r\n\t\t\t// console.log(\"Initializing completedStatus:\", initialCompletedStatus);\r\n\t\t\tsetCompletedStatus(initialCompletedStatus);\r\n\r\n\t\t\t// Calculate and update the initial remaining count\r\n\t\t\tconst remainingItems = checkpointslistData.length - 1; // All items except the first one\r\n\t\t\tconst formattedCount = remainingItems.toString().padStart(2, \"0\");\r\n\t\t\t// console.log(\"Initial remaining count set to:\", formattedCount);\r\n\r\n\t\t\t// Update localStorage directly\r\n\t\t\tif (window.localStorage) {\r\n\t\t\t\twindow.localStorage.setItem(\"remainingCount\", formattedCount);\r\n\t\t\t}\r\n\r\n\t\t\t// Call the callback\r\n\t\t\tif (onRemainingCountUpdate) {\r\n\t\t\t\tonRemainingCountUpdate(formattedCount);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [checkpointslistData, onRemainingCountUpdate,completedStatus]);\r\n\tuseEffect(() => {\r\n\t\tdocument.documentElement.style.setProperty(\"--chkcolor\", checklistColor);\r\n\t}, [checklistColor]);\r\n\r\n\tuseEffect(() => {\r\n\t\tif (checkpointslistData.length === 0) {\r\n\t\t\tsetActiveItem(\"default-placeholder\");\r\n\t\t} else {\r\n\t\t\tsetActiveItem(checkpointslistData[0]?.id);\r\n\t\t}\r\n\t}, [checkpointslistData.length == 1]);\r\n\r\n\tconst [icons, setIcons] = useState<any[]>([\r\n\t\t{\r\n\t\t\tid: 1,\r\n\t\t\tcomponent: (\r\n\t\t\t\t<span\r\n\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn1 }}\r\n\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t/>\r\n\t\t\t),\r\n\t\t\tselected: true,\r\n\t\t},\r\n\t]);\r\n\tconst encodeToBase64 = (svgString: string) => {\r\n\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t};\r\n\tlet base64Icon: any;\r\n\r\n\tconst initialSelectedIcon = icons.find((icon) => icon.selected);\r\n\tif (initialSelectedIcon && checkpointslistData.length == 0) {\r\n\t\tconst svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\r\n\t\tif (svgElement) {\r\n\t\t\tbase64Icon = encodeToBase64(svgElement);\r\n\t\t}\r\n\t}\r\n\tconst iconColor = checklistGuideMetaData[0]?.launcher?.iconColor || \"#fff\"; // Default to black if no color\r\n\tconst base64IconFinal = checklistGuideMetaData[0]?.launcher?.icon || base64Icon;\r\n\r\n\tconst totalItems = checkpointslistData.length || 1;\r\n\tconst progress = 1;\r\n\r\n\t// Update the remaining count whenever completedStatus or checkpointslistData changes\r\n\tuseEffect(() => {\r\n\t\tif (checkpointslistData.length > 0 && Object.keys(completedStatus).length > 0) {\r\n\t\t\t// Count the number of incomplete steps\r\n\t\t\tconst remainingItems =\r\n\t\t\t\tcheckpointslistData.length - Object.values(completedStatus).filter((status) => status).length;\r\n\t\t\t// Format with leading zero (01, 02, 03, etc.)\r\n\t\t\tconst formattedCount = remainingItems.toString().padStart(2, \"0\");\r\n\t\t\t// console.log(\"ChecklistPopup updating count to:\", formattedCount, \"from completedStatus:\", completedStatus);\r\n\r\n\t\t\t// Make sure the callback is called with the updated count\r\n\t\t\tif (onRemainingCountUpdate) {\r\n\t\t\t\tonRemainingCountUpdate(formattedCount);\r\n\t\t\t}\r\n\r\n\t\t\t// Also update the count in the drawerStore to ensure it's available to all components\r\n\t\t\tif (window.localStorage) {\r\n\t\t\t\twindow.localStorage.setItem(\"remainingCount\", formattedCount);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [completedStatus, checkpointslistData, onRemainingCountUpdate]);\r\n\r\n\tconst toggleItemCompletion = (id: string) => {\r\n\t\tsetCompletedStatus((prevStatus) => {\r\n\t\t\tconst newStatus = {\r\n\t\t\t\t...prevStatus,\r\n\t\t\t\t[id]: !prevStatus[id],\r\n\t\t\t};\r\n\r\n\t\t\t// Update the remaining count immediately\r\n\t\t\tif (checkpointslistData.length > 0) {\r\n\t\t\t\tconst remainingItems = checkpointslistData.length - Object.values(newStatus).filter((status) => status).length;\r\n\t\t\t\tconst formattedCount = remainingItems.toString().padStart(2, \"0\");\r\n\t\t\t\tconsole.log(\"toggleItemCompletion updating count to:\", formattedCount);\r\n\r\n\t\t\t\t// Update localStorage directly\r\n\t\t\t\tif (window.localStorage) {\r\n\t\t\t\t\twindow.localStorage.setItem(\"remainingCount\", formattedCount);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Call the callback\r\n\t\t\t\tif (onRemainingCountUpdate) {\r\n\t\t\t\t\tonRemainingCountUpdate(formattedCount);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn newStatus;\r\n\t\t});\r\n\t};\r\n\r\n\t// Mark the active item as completed\r\n\tconst handleMarkAsCompleted = () => {\r\n\t\tif (activeItem && activeItem !== \"default-placeholder\") {\r\n\t\t\tsetCompletedStatus((prevStatus) => {\r\n\t\t\t\tconst newStatus = {\r\n\t\t\t\t\t...prevStatus,\r\n\t\t\t\t\t[activeItem]: true,\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// Update the remaining count immediately\r\n\t\t\t\tif (checkpointslistData.length > 0) {\r\n\t\t\t\t\tconst remainingItems =\r\n\t\t\t\t\t\tcheckpointslistData.length - Object.values(newStatus).filter((status) => status).length;\r\n\t\t\t\t\tconst formattedCount = remainingItems.toString().padStart(2, \"0\");\r\n\t\t\t\t\tconsole.log(\"handleMarkAsCompleted updating count to:\", formattedCount);\r\n\r\n\t\t\t\t\t// Update localStorage directly\r\n\t\t\t\t\tif (window.localStorage) {\r\n\t\t\t\t\t\twindow.localStorage.setItem(\"remainingCount\", formattedCount);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Call the callback\r\n\t\t\t\t\tif (onRemainingCountUpdate) {\r\n\t\t\t\t\t\tonRemainingCountUpdate(formattedCount);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log(\"Marked item as completed:\", activeItem);\r\n\t\t\t\treturn newStatus;\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleSelect = (id: any) => {\r\n\t\tsetActiveItem(id);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tonClose();\r\n\t\tsetActiveItem(checkpointslistData[0]?.id);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tif (!isOpen) return null;\r\n\tconst selectedItem =\r\n\t\tcheckpointslistData.length > 0\r\n\t\t\t? checkpointslistData.find((item: any) =>\r\n\t\t\t\t\tcheckpointsEditPopup ? item.id === editInteractionName : item.id === activeItem\r\n\t\t\t  ) || {\r\n\t\t\t\t\tid: \"default-placeholder\",\r\n\t\t\t\ttitle: translate(\"Step Title\", { defaultValue: \"Step Title\" }),\r\n\t\t\t\tdescription: translate(\"Step Description\", { defaultValue: \"Step Description\" }),\r\n\t\t\t\t\ticon: base64Icon,\r\n\t\t\t\tmediaTitle: translate(\"Media Title\", { defaultValue: \"Media Title\" }),\r\n\t\t\t\tmediaDescription: translate(\"Media Description\", { defaultValue: \"Media Description\" }),\r\n\t\t\t\t\tsupportingMedia: [],\r\n\t\t\t  }\r\n\t\t\t: {\r\n\t\t\t\t\tid: \"default-placeholder\",\r\n\t\t\t\ttitle: translate(\"Step Title\", { defaultValue: \"Step Title\" }),\r\n\t\t\t\tdescription: translate(\"Step Description\", { defaultValue: \"Step Description\" }),\r\n\t\t\t\t\ticon: base64Icon,\r\n\t\t\t\tmediaTitle: translate(\"Media Title\", { defaultValue: \"Media Title\" }),\r\n\t\t\t\tmediaDescription: translate(\"Media Description\", { defaultValue: \"Media Description\" }),\r\n\t\t\t\t\tsupportingMedia: [],\r\n\t\t\t  };\r\n\r\n\tconst handleNavigate = () => {\r\n\t\t// window.open(\"http://localhost:3000/\", '_blank');\r\n\t};\r\n\tconst isRTL = \r\n  document.documentElement.getAttribute('dir') === 'rtl' ||\r\n  document.body.getAttribute('dir') === 'rtl';\r\n\t\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{isUnSavedChanges && openWarning && (\r\n\t\t\t\t<AlertPopup\r\n\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t\t<div\r\n\t\t\t\tstyle={{\r\n\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\tinset: 0,\r\n\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t// justifyContent: 'center',\r\n\t\t\t\t\tzIndex: 99999,\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\tinset: 0,\r\n\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.3)\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t></div>\r\n\r\n\t\t\t\t<div\r\n\t\t\t\t\t\r\n\t\t\t\t\tclassName='qadpt-chkpopup'\r\n\t\t\t\t>\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.backgroundColor,\r\n\t\t\t\t\t\t\tborder: `${checklistGuideMetaData[0]?.canvas?.borderWidth}px solid ${checklistGuideMetaData[0]?.canvas?.borderColor}`,\r\n\t\t\t\t\t\t\tborderRadius: `${checklistGuideMetaData[0]?.canvas?.cornerRadius}px`,\r\n\t\t\t\t\t\t\twidth: `${checklistGuideMetaData[0]?.canvas?.width || 930}px`,\r\n\t\t\t\t\t\t\theight: `${checklistGuideMetaData[0]?.canvas?.height || 500}px`,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\toverflow: \"auto hidden\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-chkcontent\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{/* Left side - Checklist items */}\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-chkrgt\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\tborderBottom: \"1px solid #E8E8E8\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"24px 24px 16px 24px\",\r\n\t\t\t\t\t\t\t\t\t\ttextAlign : isRTL ? 'right' : 'left',\r\n\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.titleBold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.titleItalic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.TitleSubTitle?.titleColor || \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"block\",\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{localizedTitleSubTitle.title}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleBold ? \"bold\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tfontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleItalic ? \"italic\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleColor || \"#8D8D8D\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-subtl\"\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{localizedTitleSubTitle.subTitle}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<span style={{ fontSize: \"14px\", color: \"#6b7280\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{progress}/{totalItems}\r\n\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"#e5e7eb\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twidth: `${(progress / totalItems) * 100}%`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t></div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t<div style={{ overflow: \"auto\", maxHeight:  `calc(${checklistGuideMetaData[0]?.canvas?.height || 500}px - 190px)`}}>\r\n\t\t\t\t\t\t\t\t\t{checkpointslistData.length === 0 ? (\r\n\t\t\t\t\t\t\t\t\t\t<div className={`${activeItem === \"default-placeholder\" ? \"qadpt-chkstp\" : \"\"}`}>\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName='qadpt-chkstpctn'\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{/* Title Section */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", gap: \"8px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{/* Default Icon */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={base64Icon}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ color: checklistGuideMetaData[0]?.checkpoints?.checkpointTitles }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Step Title\", { defaultValue: \"Step Title\" })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ChecklistCircle\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey=\"default\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcompleted={true}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", alignItems: \"center\", marginTop: \"8px\", gap: \"8px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<p\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointsDescription,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: \"1.5\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: 0,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Step Description\", { defaultValue: \"Step Description\" })}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\tcheckpointslistData?.map((item: any) => (\r\n\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName={`${\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t(checkpointsEditPopup ? editInteractionName === item.id : activeItem === item.id)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t? \"qadpt-chkstp\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t: \"\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t}`}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tclassName='qadpt-chkstpctn'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleSelect(item.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{/* Title Section */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div style={{ paddingLeft: \"10px\", display: \"flex\", gap: \"6px\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"row\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"calc(100% - 60px)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.icon && typeof item.icon === \"string\" ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={modifySVGColor(item.icon, checklistGuideMetaData[0]?.checkpoints?.checkpointsIcons || \"#333\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span style={{ width: \"16px\", height: \"16px\" }}></span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointTitles || \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.title}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<ChecklistCircle\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={item.id}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcompleted={completedStatus[item.id] || false}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => toggleItemCompletion(item.id)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<p\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"14px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.checkpoints?.checkpointsDescription,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkpopdesc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{item.description}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t{/* Right side - Selected item details - only show when an item is selected */}\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\twidth: \"60%\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"20px 20px 0 20px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-chklft\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tgap: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: maximize }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: closepluginicon }}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#e8e8e8\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t    height: \"calc(100% - 90px)\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\r\n\r\n\t\t\t\t\t\t\t\t\t<div style={{\r\n    \t\t\t\t\t\t\t\t\toverflow: \"hidden auto\",display: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",width:\"-webkit-fill-available\"}} >\r\n\r\n\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith(\"data:image\")) && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ImageCarousel\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tselectedItem={selectedItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tactiveItem={activeItem}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\timages={selectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:image\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any) => file.Base64)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={\"\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith(\"data:video\")) &&\r\n\t\t\t\t\t\t\t\t\t\t\t\tselectedItem.supportingMedia\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.filter((file: any) => file?.Base64?.startsWith(\"data:video\"))\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t.map((file: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<VideoPlayer\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvideoFile={file.Base64}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tisMaximized={\"\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t{(selectedItem?.supportingMedia?.length === 0 || !selectedItem?.supportingMedia) && (\r\n\t\t\t\t\t\t\t\t\t\t<div style={{ width: \"auto\", height: \"244px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: chkdefault }} />\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ color: \"#8D8D8D\" }}>{translate('Check tasks, stay organized, and finish strong!', { defaultValue: 'Check tasks, stay organized, and finish strong!' })}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\", marginTop: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chkdesc\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{selectedItem && (\r\n\t\t\t\t\t\t\t\t\t\t\t<div style={{ height: \"100%\", display: \"flex\", flexDirection: \"column\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: isRTL ? \"right\": \"left\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tflexDirection: \"column\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontWeight: 600,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"#333\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextOverflow: \"ellipsis\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaTitle}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-desc\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ color: \"#8D8D8D\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t{selectedItem.mediaDescription}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tplaceContent: \"end\",\r\n\t\t\t\t\t\t\t\t\t\tpaddingBottom: \"20px\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-btnsec\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate('Take Tour', { defaultValue: 'Take Tour' })}\r\n\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t{selectedItem?.supportingMedia?.length > 0 && (\r\n\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"9px 16px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.canvas?.primaryColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#D3D9DA\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={handleMarkAsCompleted}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate('Mark as Completed', { defaultValue: 'Mark as Completed' })}\r\n\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t{/* )} */}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nconst ChecklistApp = ({ setopenWarning, handleLeave }: { setopenWarning: any; handleLeave: any }) => {\r\n\tconst { checklistGuideMetaData, setShowLauncherSettings, showLauncherSettings, setOpenWarning } = useDrawerStore(\r\n\t\t(state: any) => state\r\n\t);\r\n\tlet base64Icon: any;\r\n\tconst [icons, setIcons] = useState<any[]>([\r\n\t\t{\r\n\t\t\tid: 1,\r\n\t\t\tcomponent: (\r\n\t\t\t\t<span\r\n\t\t\t\t\tdangerouslySetInnerHTML={{ __html: chkicn1 }}\r\n\t\t\t\t\tstyle={{ zoom: 1, display: \"flex\" }}\r\n\t\t\t\t/>\r\n\t\t\t),\r\n\t\t\tselected: true,\r\n\t\t},\r\n\t]);\r\n\tconst encodeToBase64 = (svgString: string) => {\r\n\t\treturn `data:image/svg+xml;base64,${btoa(svgString)}`;\r\n\t};\r\n\tconst iconColor = checklistGuideMetaData[0]?.launcher?.iconColor || \"#fff\"; // Default to black if no color\r\n\tconst base64IconFinal = checklistGuideMetaData[0]?.launcher?.icon;\r\n\r\n\tconst initialSelectedIcon = icons.find((icon) => icon.selected);\r\n\tif (initialSelectedIcon) {\r\n\t\tconst svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;\r\n\r\n\t\tif (svgElement) {\r\n\t\t\tbase64Icon = encodeToBase64(svgElement);\r\n\t\t}\r\n\t}\r\n\t// Using the modifySVGColor function defined at the top of the file\r\n\tconst modifiedIcon = modifySVGColor(base64IconFinal || base64Icon, iconColor);\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [remainingCount, setRemainingCount] = useState(\"00\");\r\n\r\n\tconst handleRemainingCountUpdate = (formattedCount: string) => {\r\n\t\tsetRemainingCount(formattedCount);\r\n\t};\r\n\r\n\tconst [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);\r\n\tconst [currentStep, setCurrentStep] = useState(0);\r\n\tconst [isAnnouncementOpen, setAnnouncementOpen] = useState(false);\r\n\tconst [scrollPercentage, setScrollPercentage] = useState(0);\r\n\tconst currentUrl = window.location.href;\r\n\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t{/* Your main app content here */}\r\n\r\n\t\t\t{/* Floating action button */}\r\n\t\t\t<div\r\n  className='qadpt-chklayout'\r\n>\r\n\t\t\t\t<button\r\n\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\tsetIsOpen(true);\r\n\t\t\t\t\t\tsetShowLauncherSettings(true);\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.launcher.launcherColor,\r\n\t\t\t\t\t\tcolor: \"white\",\r\n\t\t\t\t\t\tborderRadius:\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Text\" ||\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Icon+Txt\"\r\n\t\t\t\t\t\t\t\t? \"16px\"\r\n\t\t\t\t\t\t\t\t: \"50%\",\r\n\t\t\t\t\t\theight: \"54px\",\r\n\t\t\t\t\t\twidth:\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Text\" ||\r\n\t\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher.type === \"Icon+Txt\"\r\n\t\t\t\t\t\t\t\t? `auto`\r\n\t\t\t\t\t\t\t\t: \"54px\",\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\tboxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",\r\n\t\t\t\t\t\ttransition: \"all 0.2s ease\",\r\n\t\t\t\t\t\tborder: \"none\",\r\n\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\tposition: \"relative\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.type === \"Icon\" && (\r\n\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\tsrc={modifiedIcon}\r\n\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Text Only */}\r\n\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.type === \"Text\" && checklistGuideMetaData[0]?.launcher?.text && (\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.launcher?.textColor,\r\n\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\twhiteSpace: \"nowrap\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{checklistGuideMetaData[0].launcher.text}\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Icon + Text */}\r\n\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.type === \"Icon+Txt\" &&\r\n\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher?.text &&\r\n\t\t\t\t\t\tchecklistGuideMetaData[0]?.launcher?.icon && (\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\tgap: \"8px\",\r\n\t\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.launcher?.textColor,\r\n\t\t\t\t\t\t\t\t\tfontSize: \"16px\",\r\n\t\t\t\t\t\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={modifiedIcon}\r\n\t\t\t\t\t\t\t\t\talt=\"icon\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ width: \"20px\", height: \"20px\" }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.text}\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Notification Badge */}\r\n\t\t\t\t\t{checklistGuideMetaData[0]?.launcher?.notificationBadge && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\t\t\ttop: \"-8px\",\r\n\t\t\t\t\t\t\t\tright: \"-8px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: checklistGuideMetaData[0]?.launcher?.notificationBadgeColor,\r\n\t\t\t\t\t\t\t\tcolor: checklistGuideMetaData[0]?.launcher?.notificationTextColor,\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"9999px\",\r\n\t\t\t\t\t\t\t\theight: \"24px\",\r\n\t\t\t\t\t\t\t\twidth: \"24px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{remainingCount}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</button>\r\n\t\t\t</div>\r\n\r\n\t\t\t{showLauncherSettings && (\r\n\t\t\t\t<>\r\n\t\t\t\t\t<LauncherSettings />\r\n\t\t\t\t</>\r\n\t\t\t)}\r\n\r\n\t\t\t{/* Popup component */}\r\n\t\t\t<ChecklistPopup\r\n\t\t\t\tdata={\"\"}\r\n\t\t\t\tguideDetails={\"\"}\r\n\t\t\t\tisOpen={isOpen}\r\n\t\t\t\tonClose={() => setIsOpen(true)}\r\n\t\t\t\tonRemainingCountUpdate={handleRemainingCountUpdate}\r\n\t\t\t\tsetopenWarning={setOpenWarning}\r\n\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t/>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default ChecklistApp;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAWC,QAAQ,QAAQ,OAAO;AAC3D,OAAOC,eAAe,MAAM,sBAAsB;AAClD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,mBAAmB,QAAQ,cAAc;AAElD,SAAoBC,eAAe,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,UAAU,QAAQ,0BAA0B;AACpG,OAAOC,UAAU,MAAM,sBAAsB;AAC7C,OAAO,8BAA8B;AACrC,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAc/C;AACA,MAAMC,cAAc,GAAGA,CAACC,SAAc,EAAEC,KAAU,KAAK;EACtD,IAAI,CAACD,SAAS,EAAE;IACf,OAAO,EAAE;EACV;EAEA,IAAI;IACH;IACA,IAAI,CAACA,SAAS,CAACE,QAAQ,CAAC,4BAA4B,CAAC,EAAE;MACtD,OAAOF,SAAS,CAAC,CAAC;IACnB;IAEA,MAAMG,UAAU,GAAGC,IAAI,CAACJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhD;IACA,MAAMC,SAAS,GAAGH,UAAU,CAACD,QAAQ,CAAC,UAAU,CAAC;IACjD,MAAMK,cAAc,GAAG,uBAAuB,CAACC,IAAI,CAACL,UAAU,CAAC;IAE/D,IAAIM,WAAW,GAAGN,UAAU;IAE5B,IAAIG,SAAS,IAAI,CAACC,cAAc,EAAE;MACjC;MACAE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,iBAAiB,EAAE,WAAWT,KAAK,GAAG,CAAC;IAC1E,CAAC,MAAM,IAAIM,cAAc,EAAE;MAC1B;MACAE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,uBAAuB,EAAE,SAAST,KAAK,GAAG,CAAC;IAC9E,CAAC,MAAM;MACN;MACAQ,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,sBAAsB,EAAE,eAAeT,KAAK,GAAG,CAAC;MAClFQ,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,qBAAqB,EAAE,cAAcT,KAAK,GAAG,CAAC;IACjF;IAEA,MAAMU,cAAc,GAAG,6BAA6BC,IAAI,CAACH,WAAW,CAAC,EAAE;IACvE,OAAOE,cAAc;EACtB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAOb,SAAS,CAAC,CAAC;EACnB;AACD,CAAC;AAED,MAAMe,cAA6C,GAAGA,CAAC;EACtDC,MAAM;EACNC,OAAO;EACPC,sBAAsB;EACtBC,IAAI;EACJC,YAAY;EACZC,cAAc;EACdC;AACD,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACL,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGjF,cAAc,CAAC,CAAC;EACzC,MAAM;IAAEkF,sBAAsB;IAAEC,oBAAoB;IAAEC,gBAAgB;IAAEC;EAAY,CAAC,GAAG/F,cAAc,CACpGgG,KAAU,IAAKA,KACjB,CAAC;EAED,MAAMC,sBAAkD,GAAG,CAAC,CAAC;;EAE7D;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrG,QAAQ,CAA6B,CAAC,CAAC,CAAC;;EAEtF;EACA,MAAMsG,mBAAmB,GACxB,EAAA5D,qBAAA,GAAAoD,sBAAsB,CAAC,CAAC,CAAC,cAAApD,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA2B6D,WAAW,cAAA5D,sBAAA,wBAAAC,sBAAA,GAAtCD,sBAAA,CAAwC6D,eAAe,cAAA5D,sBAAA,uBAAvDA,sBAAA,CAAyD6D,GAAG,CAAC,CAACC,UAAe,EAAEC,KAAa,MAAM;IACjG,GAAGD,UAAU;IACbE,SAAS,EAAED,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG;EACjC,CAAC,CAAC,CAAC,KAAI,EAAE;EAEV,MAAM,CAACE,cAAc,EAAEC,iBAAiB,CAAC,GAAG9G,QAAQ,CAACsG,mBAAmB,CAAC;EACzE,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGhH,QAAQ,CAC3CsG,mBAAmB,CAACW,MAAM,GAAG,CAAC,IAAI,CAAClB,oBAAoB,IAAAlD,qBAAA,GAAGyD,mBAAmB,CAAC,CAAC,CAAC,cAAAzD,qBAAA,uBAAtBA,qBAAA,CAAwBqE,EAAE,GAAG,qBACxF,CAAC;EACD,MAAMC,cAAc,IAAArE,sBAAA,GAAGgD,sBAAsB,CAAC,CAAC,CAAC,cAAAhD,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BsE,MAAM,cAAArE,sBAAA,uBAAjCA,sBAAA,CAAmCsE,YAAY;EAEvE,SAASC,yBAAyBA,CAACC,EAAO,EAAE3B,CAAM,EAAE;IACjD,MAAM4B,YAAY,GAAG,iBAAiB;IACtC,MAAMC,eAAe,GAAG,oFAAoF;IAC5G,OAAO;MACLC,KAAK,EACH,EAACH,EAAE,aAAFA,EAAE,eAAFA,EAAE,CAAEG,KAAK,KAAIH,EAAE,CAACG,KAAK,KAAKF,YAAY,GACnC5B,CAAC,CAAC4B,YAAY,EAAE;QAAEG,YAAY,EAAEH;MAAa,CAAC,CAAC,GAC/CD,EAAE,CAACG,KAAK;MACdE,QAAQ,EACN,EAACL,EAAE,aAAFA,EAAE,eAAFA,EAAE,CAAEK,QAAQ,KAAIL,EAAE,CAACK,QAAQ,KAAKH,eAAe,GAC5C7B,CAAC,CAAC6B,eAAe,EAAE;QAAEE,YAAY,EAAEF;MAAgB,CAAC,CAAC,GACrDF,EAAE,CAACK;IACX,CAAC;EACH;EAEA,MAAMC,sBAAsB,GAAGP,yBAAyB,EAAAtE,sBAAA,GAAC8C,sBAAsB,CAAC,CAAC,CAAC,cAAA9C,sBAAA,uBAAzBA,sBAAA,CAA2B8E,aAAa,EAAEjC,SAAS,CAAC;EAAE,CAAC;;EAEhH;EACA9F,SAAS,CAAC,MAAM;IACf,IAAIuG,mBAAmB,CAACW,MAAM,GAAG,CAAC,EAAE;MACnC,MAAMd,sBAAkD,GAAG,CAAC,CAAC;MAE7DG,mBAAmB,CAACyB,OAAO,CAAC,CAACC,IAAS,EAAErB,KAAa,KAAK;QACzD;QACAR,sBAAsB,CAAC6B,IAAI,CAACd,EAAE,CAAC,GAAGP,KAAK,KAAK,CAAC;MAC9C,CAAC,CAAC;;MAEF;MACAN,kBAAkB,CAACF,sBAAsB,CAAC;;MAE1C;MACA,MAAM8B,cAAc,GAAG3B,mBAAmB,CAACW,MAAM,GAAG,CAAC,CAAC,CAAC;MACvD,MAAMiB,cAAc,GAAGD,cAAc,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACjE;;MAEA;MACA,IAAIC,MAAM,CAACC,YAAY,EAAE;QACxBD,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEL,cAAc,CAAC;MAC9D;;MAEA;MACA,IAAI9F,sBAAsB,EAAE;QAC3BA,sBAAsB,CAAC8F,cAAc,CAAC;MACvC;IACD;EACD,CAAC,EAAE,CAAC5B,mBAAmB,EAAElE,sBAAsB,EAACgE,eAAe,CAAC,CAAC;EACjErG,SAAS,CAAC,MAAM;IACfyI,QAAQ,CAACC,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,YAAY,EAAExB,cAAc,CAAC;EACzE,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpBpH,SAAS,CAAC,MAAM;IACf,IAAIuG,mBAAmB,CAACW,MAAM,KAAK,CAAC,EAAE;MACrCD,aAAa,CAAC,qBAAqB,CAAC;IACrC,CAAC,MAAM;MAAA,IAAA4B,sBAAA;MACN5B,aAAa,EAAA4B,sBAAA,GAACtC,mBAAmB,CAAC,CAAC,CAAC,cAAAsC,sBAAA,uBAAtBA,sBAAA,CAAwB1B,EAAE,CAAC;IAC1C;EACD,CAAC,EAAE,CAACZ,mBAAmB,CAACW,MAAM,IAAI,CAAC,CAAC,CAAC;EAErC,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG9I,QAAQ,CAAQ,CACzC;IACCkH,EAAE,EAAE,CAAC;IACL6B,SAAS,eACRjI,OAAA;MACCkI,uBAAuB,EAAE;QAAEC,MAAM,EAAExI;MAAQ,CAAE;MAC7CiI,KAAK,EAAE;QAAEQ,IAAI,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACD;IACDC,QAAQ,EAAE;EACX,CAAC,CACD,CAAC;EACF,MAAMC,cAAc,GAAIC,SAAiB,IAAK;IAC7C,OAAO,6BAA6B5H,IAAI,CAAC4H,SAAS,CAAC,EAAE;EACtD,CAAC;EACD,IAAIC,UAAe;EAEnB,MAAMC,mBAAmB,GAAGf,KAAK,CAACgB,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACN,QAAQ,CAAC;EAC/D,IAAII,mBAAmB,IAAItD,mBAAmB,CAACW,MAAM,IAAI,CAAC,EAAE;IAAA,IAAA8C,qBAAA;IAC3D,MAAMC,UAAU,IAAAD,qBAAA,GAAGH,mBAAmB,CAACb,SAAS,CAACkB,KAAK,CAACjB,uBAAuB,cAAAe,qBAAA,uBAA3DA,qBAAA,CAA6Dd,MAAM;IAEtF,IAAIe,UAAU,EAAE;MACfL,UAAU,GAAGF,cAAc,CAACO,UAAU,CAAC;IACxC;EACD;EACA,MAAME,SAAS,GAAG,EAAAjH,sBAAA,GAAA6C,sBAAsB,CAAC,CAAC,CAAC,cAAA7C,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA2BkH,QAAQ,cAAAjH,sBAAA,uBAAnCA,sBAAA,CAAqCgH,SAAS,KAAI,MAAM,CAAC,CAAC;EAC5E,MAAME,eAAe,GAAG,EAAAjH,sBAAA,GAAA2C,sBAAsB,CAAC,CAAC,CAAC,cAAA3C,sBAAA,wBAAAC,uBAAA,GAAzBD,sBAAA,CAA2BgH,QAAQ,cAAA/G,uBAAA,uBAAnCA,uBAAA,CAAqC0G,IAAI,KAAIH,UAAU;EAE/E,MAAMU,UAAU,GAAG/D,mBAAmB,CAACW,MAAM,IAAI,CAAC;EAClD,MAAMqD,QAAQ,GAAG,CAAC;;EAElB;EACAvK,SAAS,CAAC,MAAM;IACf,IAAIuG,mBAAmB,CAACW,MAAM,GAAG,CAAC,IAAIsD,MAAM,CAACC,IAAI,CAACpE,eAAe,CAAC,CAACa,MAAM,GAAG,CAAC,EAAE;MAC9E;MACA,MAAMgB,cAAc,GACnB3B,mBAAmB,CAACW,MAAM,GAAGsD,MAAM,CAACE,MAAM,CAACrE,eAAe,CAAC,CAACsE,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAAC,CAAC1D,MAAM;MAC9F;MACA,MAAMiB,cAAc,GAAGD,cAAc,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACjE;;MAEA;MACA,IAAIhG,sBAAsB,EAAE;QAC3BA,sBAAsB,CAAC8F,cAAc,CAAC;MACvC;;MAEA;MACA,IAAIG,MAAM,CAACC,YAAY,EAAE;QACxBD,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEL,cAAc,CAAC;MAC9D;IACD;EACD,CAAC,EAAE,CAAC9B,eAAe,EAAEE,mBAAmB,EAAElE,sBAAsB,CAAC,CAAC;EAElE,MAAMwI,oBAAoB,GAAI1D,EAAU,IAAK;IAC5Cb,kBAAkB,CAAEwE,UAAU,IAAK;MAClC,MAAMC,SAAS,GAAG;QACjB,GAAGD,UAAU;QACb,CAAC3D,EAAE,GAAG,CAAC2D,UAAU,CAAC3D,EAAE;MACrB,CAAC;;MAED;MACA,IAAIZ,mBAAmB,CAACW,MAAM,GAAG,CAAC,EAAE;QACnC,MAAMgB,cAAc,GAAG3B,mBAAmB,CAACW,MAAM,GAAGsD,MAAM,CAACE,MAAM,CAACK,SAAS,CAAC,CAACJ,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAAC,CAAC1D,MAAM;QAC9G,MAAMiB,cAAc,GAAGD,cAAc,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACjEpG,OAAO,CAAC+I,GAAG,CAAC,yCAAyC,EAAE7C,cAAc,CAAC;;QAEtE;QACA,IAAIG,MAAM,CAACC,YAAY,EAAE;UACxBD,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEL,cAAc,CAAC;QAC9D;;QAEA;QACA,IAAI9F,sBAAsB,EAAE;UAC3BA,sBAAsB,CAAC8F,cAAc,CAAC;QACvC;MACD;MAEA,OAAO4C,SAAS;IACjB,CAAC,CAAC;EACH,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAAA,KAAM;IACnC,IAAIjE,UAAU,IAAIA,UAAU,KAAK,qBAAqB,EAAE;MACvDV,kBAAkB,CAAEwE,UAAU,IAAK;QAClC,MAAMC,SAAS,GAAG;UACjB,GAAGD,UAAU;UACb,CAAC9D,UAAU,GAAG;QACf,CAAC;;QAED;QACA,IAAIT,mBAAmB,CAACW,MAAM,GAAG,CAAC,EAAE;UACnC,MAAMgB,cAAc,GACnB3B,mBAAmB,CAACW,MAAM,GAAGsD,MAAM,CAACE,MAAM,CAACK,SAAS,CAAC,CAACJ,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAAC,CAAC1D,MAAM;UACxF,MAAMiB,cAAc,GAAGD,cAAc,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;UACjEpG,OAAO,CAAC+I,GAAG,CAAC,0CAA0C,EAAE7C,cAAc,CAAC;;UAEvE;UACA,IAAIG,MAAM,CAACC,YAAY,EAAE;YACxBD,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEL,cAAc,CAAC;UAC9D;;UAEA;UACA,IAAI9F,sBAAsB,EAAE;YAC3BA,sBAAsB,CAAC8F,cAAc,CAAC;UACvC;QACD;QAEAlG,OAAO,CAAC+I,GAAG,CAAC,2BAA2B,EAAEhE,UAAU,CAAC;QACpD,OAAO+D,SAAS;MACjB,CAAC,CAAC;IACH;EACD,CAAC;EAED,MAAMG,YAAY,GAAI/D,EAAO,IAAK;IACjCF,aAAa,CAACE,EAAE,CAAC;EAClB,CAAC;EAED,MAAMgE,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA;IACzBhJ,OAAO,CAAC,CAAC;IACT6E,aAAa,EAAAmE,sBAAA,GAAC7E,mBAAmB,CAAC,CAAC,CAAC,cAAA6E,sBAAA,uBAAtBA,sBAAA,CAAwBjE,EAAE,CAAC;EAC1C,CAAC;EAED,IAAI,CAAChF,MAAM,EAAE,OAAO,IAAI;EAExB,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,MAAMkJ,YAAY,GACjB9E,mBAAmB,CAACW,MAAM,GAAG,CAAC,GAC3BX,mBAAmB,CAACuD,IAAI,CAAE7B,IAAS,IACnCjC,oBAAoB,GAAGiC,IAAI,CAACd,EAAE,KAAK5G,mBAAmB,GAAG0H,IAAI,CAACd,EAAE,KAAKH,UACrE,CAAC,IAAI;IACLG,EAAE,EAAE,qBAAqB;IAC1BQ,KAAK,EAAE7B,SAAS,CAAC,YAAY,EAAE;MAAE8B,YAAY,EAAE;IAAa,CAAC,CAAC;IAC9D0D,WAAW,EAAExF,SAAS,CAAC,kBAAkB,EAAE;MAAE8B,YAAY,EAAE;IAAmB,CAAC,CAAC;IAC/EmC,IAAI,EAAEH,UAAU;IACjB2B,UAAU,EAAEzF,SAAS,CAAC,aAAa,EAAE;MAAE8B,YAAY,EAAE;IAAc,CAAC,CAAC;IACrE4D,gBAAgB,EAAE1F,SAAS,CAAC,mBAAmB,EAAE;MAAE8B,YAAY,EAAE;IAAoB,CAAC,CAAC;IACtF6D,eAAe,EAAE;EACjB,CAAC,GACD;IACAtE,EAAE,EAAE,qBAAqB;IAC1BQ,KAAK,EAAE7B,SAAS,CAAC,YAAY,EAAE;MAAE8B,YAAY,EAAE;IAAa,CAAC,CAAC;IAC9D0D,WAAW,EAAExF,SAAS,CAAC,kBAAkB,EAAE;MAAE8B,YAAY,EAAE;IAAmB,CAAC,CAAC;IAC/EmC,IAAI,EAAEH,UAAU;IACjB2B,UAAU,EAAEzF,SAAS,CAAC,aAAa,EAAE;MAAE8B,YAAY,EAAE;IAAc,CAAC,CAAC;IACrE4D,gBAAgB,EAAE1F,SAAS,CAAC,mBAAmB,EAAE;MAAE8B,YAAY,EAAE;IAAoB,CAAC,CAAC;IACtF6D,eAAe,EAAE;EACjB,CAAC;EAEL,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC5B;EAAA,CACA;EACD,MAAMC,KAAK,GACVlD,QAAQ,CAACC,eAAe,CAACkD,YAAY,CAAC,KAAK,CAAC,KAAK,KAAK,IACtDnD,QAAQ,CAACoD,IAAI,CAACD,YAAY,CAAC,KAAK,CAAC,KAAK,KAAK;EAE5C,oBACC7K,OAAA,CAAAE,SAAA;IAAA6K,QAAA,GACE7F,gBAAgB,IAAIC,WAAW,iBAC/BnF,OAAA,CAACH,UAAU;MACVsF,WAAW,EAAEA,WAAY;MACzB1D,cAAc,EAAEA,cAAe;MAC/BC,WAAW,EAAEA;IAAY;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CACD,eACDzI,OAAA;MACC4H,KAAK,EAAE;QACNoD,QAAQ,EAAE,OAAO;QACjBC,KAAK,EAAE,CAAC;QACR5C,OAAO,EAAE,MAAM;QACf6C,UAAU,EAAE,QAAQ;QACpB;QACAC,MAAM,EAAE;MAAU,CAAE;MAAAJ,QAAA,gBAErB/K,OAAA;QACC4H,KAAK,EAAE;UACNoD,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,CAAC;UACRG,eAAe,EAAE;QAClB,CAAE;QACFC,OAAO,EAAEjB;MAAY;QAAA9B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAEPzI,OAAA;QAECsL,SAAS,EAAC,gBAAgB;QAAAP,QAAA,eAE1B/K,OAAA;UACC4H,KAAK,EAAE;YACNwD,eAAe,GAAA7I,uBAAA,GAAEyC,sBAAsB,CAAC,CAAC,CAAC,cAAAzC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B+D,MAAM,cAAA9D,uBAAA,uBAAjCA,uBAAA,CAAmC4I,eAAe;YACnEG,MAAM,EAAE,IAAA9I,uBAAA,GAAGuC,sBAAsB,CAAC,CAAC,CAAC,cAAAvC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B6D,MAAM,cAAA5D,uBAAA,uBAAjCA,uBAAA,CAAmC8I,WAAW,aAAA7I,uBAAA,GAAYqC,sBAAsB,CAAC,CAAC,CAAC,cAAArC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B2D,MAAM,cAAA1D,uBAAA,uBAAjCA,uBAAA,CAAmC6I,WAAW,EAAE;YACrHC,YAAY,EAAE,IAAA7I,uBAAA,GAAGmC,sBAAsB,CAAC,CAAC,CAAC,cAAAnC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2ByD,MAAM,cAAAxD,uBAAA,uBAAjCA,uBAAA,CAAmC6I,YAAY,IAAI;YACpEC,KAAK,EAAE,GAAG,EAAA7I,uBAAA,GAAAiC,sBAAsB,CAAC,CAAC,CAAC,cAAAjC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BuD,MAAM,cAAAtD,uBAAA,uBAAjCA,uBAAA,CAAmC4I,KAAK,KAAI,GAAG,IAAI;YAC7DC,MAAM,EAAE,GAAG,EAAA5I,uBAAA,GAAA+B,sBAAsB,CAAC,CAAC,CAAC,cAAA/B,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BqD,MAAM,cAAApD,uBAAA,uBAAjCA,uBAAA,CAAmC2I,MAAM,KAAI,GAAG;UAC5D,CAAE;UAAAd,QAAA,eAEF/K,OAAA;YACC4H,KAAK,EAAE;cACNS,OAAO,EAAE,MAAM;cACfwD,MAAM,EAAE,MAAM;cACdD,KAAK,EAAE,MAAM;cACbE,QAAQ,EAAE;YACX,CAAE;YACFR,SAAS,EAAC,kBAAkB;YAAAP,QAAA,gBAG5B/K,OAAA;cAECsL,SAAS,EAAC,cAAc;cAAAP,QAAA,gBAExB/K,OAAA;gBACC4H,KAAK,EAAE;kBACNS,OAAO,EAAE,MAAM;kBACf0D,aAAa,EAAE,QAAQ;kBACvBC,GAAG,EAAE,MAAM;kBACXC,YAAY,EAAE,mBAAmB;kBACjCC,OAAO,EAAE,qBAAqB;kBAC9BC,SAAS,EAAGvB,KAAK,GAAG,OAAO,GAAG;gBAE/B,CAAE;gBAAAG,QAAA,gBAEF/K,OAAA;kBACC4H,KAAK,EAAE;oBACNS,OAAO,EAAE,MAAM;oBACf0D,aAAa,EAAE,QAAQ;oBACvBC,GAAG,EAAE;kBACN,CAAE;kBAAAjB,QAAA,gBAEF/K,OAAA;oBACC4H,KAAK,EAAE;sBACNwE,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,CAAAlJ,uBAAA,GAAA6B,sBAAsB,CAAC,CAAC,CAAC,cAAA7B,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B6D,aAAa,cAAA5D,uBAAA,eAAxCA,uBAAA,CAA0CkJ,SAAS,GAAG,MAAM,GAAG,QAAQ;sBACnFC,SAAS,EAAE,CAAAlJ,uBAAA,GAAA2B,sBAAsB,CAAC,CAAC,CAAC,cAAA3B,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B2D,aAAa,cAAA1D,uBAAA,eAAxCA,uBAAA,CAA0CkJ,WAAW,GAAG,QAAQ,GAAG,QAAQ;sBACtFnM,KAAK,EAAE,EAAAkD,uBAAA,GAAAyB,sBAAsB,CAAC,CAAC,CAAC,cAAAzB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2ByD,aAAa,cAAAxD,uBAAA,uBAAxCA,uBAAA,CAA0CiJ,UAAU,KAAI,MAAM;sBACrEpE,OAAO,EAAE,OAAO;sBAChBqE,YAAY,EAAE,UAAU;sBACxBC,UAAU,EAAE,QAAQ;sBACpBC,SAAS,EAAE,YAAY;sBACvBd,QAAQ,EAAE;oBACX,CAAE;oBAAAf,QAAA,EAEDhE,sBAAsB,CAACH;kBAAK;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eAENzI,OAAA;oBACC4H,KAAK,EAAE;sBACNwE,QAAQ,EAAE,MAAM;sBAChBC,UAAU,EAAE,CAAA5I,uBAAA,GAAAuB,sBAAsB,CAAC,CAAC,CAAC,cAAAvB,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BuD,aAAa,cAAAtD,uBAAA,eAAxCA,uBAAA,CAA0CmJ,YAAY,GAAG,MAAM,GAAG,QAAQ;sBACtFN,SAAS,EAAE,CAAA5I,uBAAA,GAAAqB,sBAAsB,CAAC,CAAC,CAAC,cAAArB,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BqD,aAAa,cAAApD,uBAAA,eAAxCA,uBAAA,CAA0CkJ,cAAc,GAAG,QAAQ,GAAG,QAAQ;sBACzFzM,KAAK,EAAE,EAAAwD,uBAAA,GAAAmB,sBAAsB,CAAC,CAAC,CAAC,cAAAnB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BmD,aAAa,cAAAlD,uBAAA,uBAAxCA,uBAAA,CAA0CiJ,aAAa,KAAI;oBACnE,CAAE;oBACFzB,SAAS,EAAC,aAAa;oBAAAP,QAAA,EAEtBhE,sBAAsB,CAACD;kBAAQ;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAENzI,OAAA;kBAAA+K,QAAA,gBACC/K,OAAA;oBACC4H,KAAK,EAAE;sBACNS,OAAO,EAAE,MAAM;sBACf6C,UAAU,EAAE,QAAQ;sBACpB8B,cAAc,EAAE,eAAe;sBAC/BC,YAAY,EAAE;oBACf,CAAE;oBAAAlC,QAAA,eAEF/K,OAAA;sBAAM4H,KAAK,EAAE;wBAAEwE,QAAQ,EAAE,MAAM;wBAAE/L,KAAK,EAAE;sBAAU,CAAE;sBAAA0K,QAAA,GAClDvB,QAAQ,EAAC,GAAC,EAACD,UAAU;oBAAA;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNzI,OAAA;oBACC4H,KAAK,EAAE;sBACNiE,MAAM,EAAE,KAAK;sBACbT,eAAe,EAAE,SAAS;sBAC1BM,YAAY,EAAE,QAAQ;sBACtBI,QAAQ,EAAE;oBACX,CAAE;oBAAAf,QAAA,eAEF/K,OAAA;sBACC4H,KAAK,EAAE;wBACNiE,MAAM,EAAE,MAAM;wBACdT,eAAe,GAAArH,uBAAA,GAAEiB,sBAAsB,CAAC,CAAC,CAAC,cAAAjB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BuC,MAAM,cAAAtC,uBAAA,uBAAjCA,uBAAA,CAAmCuC,YAAY;wBAChEmF,YAAY,EAAE,QAAQ;wBACtBE,KAAK,EAAE,GAAIpC,QAAQ,GAAGD,UAAU,GAAI,GAAG;sBACxC;oBAAE;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAENzI,OAAA;gBAAK4H,KAAK,EAAE;kBAAEkE,QAAQ,EAAE,MAAM;kBAAEoB,SAAS,EAAG,QAAQ,EAAAjJ,uBAAA,GAAAe,sBAAsB,CAAC,CAAC,CAAC,cAAAf,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BqC,MAAM,cAAApC,uBAAA,uBAAjCA,uBAAA,CAAmC2H,MAAM,KAAI,GAAG;gBAAa,CAAE;gBAAAd,QAAA,EACjHvF,mBAAmB,CAACW,MAAM,KAAK,CAAC,gBAChCnG,OAAA;kBAAKsL,SAAS,EAAE,GAAGrF,UAAU,KAAK,qBAAqB,GAAG,cAAc,GAAG,EAAE,EAAG;kBAAA8E,QAAA,eAC/E/K,OAAA;oBAECsL,SAAS,EAAC,iBAAiB;oBAAAP,QAAA,gBAG3B/K,OAAA;sBACC4H,KAAK,EAAE;wBACNS,OAAO,EAAE,MAAM;wBACf6C,UAAU,EAAE,QAAQ;wBACpB8B,cAAc,EAAE,eAAe;wBAC/BpB,KAAK,EAAE;sBACR,CAAE;sBAAAb,QAAA,gBAEF/K,OAAA;wBAAK4H,KAAK,EAAE;0BAAES,OAAO,EAAE,MAAM;0BAAE6C,UAAU,EAAE,QAAQ;0BAAEc,GAAG,EAAE;wBAAM,CAAE;wBAAAjB,QAAA,gBAEjE/K,OAAA;0BACCmN,GAAG,EAAEtE,UAAW;0BAChBuE,GAAG,EAAC,MAAM;0BACVxF,KAAK,EAAE;4BAAEgE,KAAK,EAAE,MAAM;4BAAEC,MAAM,EAAE;0BAAO;wBAAE;0BAAAvD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,eACFzI,OAAA;0BAAM4H,KAAK,EAAE;4BAAEvH,KAAK,GAAA8D,uBAAA,GAAEa,sBAAsB,CAAC,CAAC,CAAC,cAAAb,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BsB,WAAW,cAAArB,uBAAA,uBAAtCA,uBAAA,CAAwCiJ;0BAAiB,CAAE;0BAAAtC,QAAA,EAC/EhG,SAAS,CAAC,YAAY,EAAE;4BAAE8B,YAAY,EAAE;0BAAa,CAAC;wBAAC;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNzI,OAAA;wBAAA+K,QAAA,eACC/K,OAAA,CAACb,eAAe;0BAEf2G,SAAS,EAAE,IAAK;0BAChBuF,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE;0BAClBiC,IAAI,EAAC;wBAAI,GAHL,SAAS;0BAAAhF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAIb;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC,eACNzI,OAAA;sBAAK4H,KAAK,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAE6C,UAAU,EAAE,QAAQ;wBAAEqC,SAAS,EAAE,KAAK;wBAAEvB,GAAG,EAAE;sBAAM,CAAE;sBAAAjB,QAAA,eACnF/K,OAAA;wBACC4H,KAAK,EAAE;0BACNwE,QAAQ,EAAE,MAAM;0BAChB/L,KAAK,GAAAgE,uBAAA,GAAEW,sBAAsB,CAAC,CAAC,CAAC,cAAAX,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BoB,WAAW,cAAAnB,uBAAA,uBAAtCA,uBAAA,CAAwCkJ,sBAAsB;0BACrEC,UAAU,EAAE,KAAK;0BACjBC,MAAM,EAAE,CAAC;0BACTf,UAAU,EAAE,QAAQ;0BACpBC,SAAS,EAAE;wBACZ,CAAE;wBAAA7B,QAAA,EAEDhG,SAAS,CAAC,kBAAkB,EAAE;0BAAE8B,YAAY,EAAE;wBAAmB,CAAC;sBAAC;wBAAAyB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,GAENjD,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEG,GAAG,CAAEuB,IAAS;kBAAA,IAAAyG,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;kBAAA,oBAClChO,OAAA;oBACCsL,SAAS,EAAE,GACV,CAACrG,oBAAoB,GAAGzF,mBAAmB,KAAK0H,IAAI,CAACd,EAAE,GAAGH,UAAU,KAAKiB,IAAI,CAACd,EAAE,IAC7E,cAAc,GACd,EAAE,EACH;oBAAA2E,QAAA,eAEH/K,OAAA;sBAECsL,SAAS,EAAC,iBAAiB;sBAC3BD,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAACjD,IAAI,CAACd,EAAE,CAAE;sBAAA2E,QAAA,eAGrC/K,OAAA;wBAAK4H,KAAK,EAAE;0BAAEqG,WAAW,EAAE,MAAM;0BAAE5F,OAAO,EAAE,MAAM;0BAAE2D,GAAG,EAAE,KAAK;0BAAED,aAAa,EAAE;wBAAS,CAAE;wBAAAhB,QAAA,gBACzF/K,OAAA;0BACC4H,KAAK,EAAE;4BACNS,OAAO,EAAE,MAAM;4BACf6C,UAAU,EAAE,QAAQ;4BACpB8B,cAAc,EAAE,eAAe;4BAC/BpB,KAAK,EAAE;0BACR,CAAE;0BAAAb,QAAA,gBAEF/K,OAAA;4BACC4H,KAAK,EAAE;8BACNS,OAAO,EAAE,MAAM;8BACf6C,UAAU,EAAE,QAAQ;8BACpBc,GAAG,EAAE,MAAM;8BACXD,aAAa,EAAE,KAAK;8BACpBH,KAAK,EAAE;4BACR,CAAE;4BAAAb,QAAA,GAED7D,IAAI,CAAC8B,IAAI,IAAI,OAAO9B,IAAI,CAAC8B,IAAI,KAAK,QAAQ,gBAC1ChJ,OAAA;8BACCmN,GAAG,EAAEhN,cAAc,CAAC+G,IAAI,CAAC8B,IAAI,EAAE,EAAA2E,uBAAA,GAAA3I,sBAAsB,CAAC,CAAC,CAAC,cAAA2I,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BlI,WAAW,cAAAmI,uBAAA,uBAAtCA,uBAAA,CAAwCM,gBAAgB,KAAI,MAAM,CAAE;8BACnGd,GAAG,EAAC,MAAM;8BACVxF,KAAK,EAAE;gCAAEgE,KAAK,EAAE,MAAM;gCAAEC,MAAM,EAAE;8BAAO;4BAAE;8BAAAvD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzC,CAAC,gBAEFzI,OAAA;8BACC4H,KAAK,EAAE;gCACNgE,KAAK,EAAE,MAAM;gCACbC,MAAM,EAAE,MAAM;gCACdxD,OAAO,EAAE,MAAM;gCACf6C,UAAU,EAAE,QAAQ;gCACpB8B,cAAc,EAAE;8BACjB,CAAE;8BAAAjC,QAAA,eAEF/K,OAAA;gCAAM4H,KAAK,EAAE;kCAAEgE,KAAK,EAAE,MAAM;kCAAEC,MAAM,EAAE;gCAAO;8BAAE;gCAAAvD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACnD,CACL,eAEDzI,OAAA;8BACC4H,KAAK,EAAE;gCACNvH,KAAK,EAAE,EAAAwN,uBAAA,GAAA7I,sBAAsB,CAAC,CAAC,CAAC,cAAA6I,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BpI,WAAW,cAAAqI,uBAAA,uBAAtCA,uBAAA,CAAwCT,gBAAgB,KAAI,MAAM;gCACzEvB,QAAQ,EAAE,QAAQ;gCAClBY,YAAY,EAAE,UAAU;gCACxBC,UAAU,EAAE,QAAQ;gCACpBC,SAAS,EAAE;8BACZ,CAAE;8BAAA7B,QAAA,EAED7D,IAAI,CAACN;4BAAK;8BAAA0B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAEH,CAAC,eACNzI,OAAA;4BAAA+K,QAAA,eACC/K,OAAA,CAACb,eAAe;8BAEf2G,SAAS,EAAER,eAAe,CAAC4B,IAAI,CAACd,EAAE,CAAC,IAAI,KAAM;8BAC7CiF,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAAC5C,IAAI,CAACd,EAAE,CAAE;8BAC7CkH,IAAI,EAAC;4BAAI,GAHJpG,IAAI,CAACd,EAAE;8BAAAkC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAIZ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF,CAAC,eACNzI,OAAA;0BAAA+K,QAAA,eACC/K,OAAA;4BACC4H,KAAK,EAAE;8BACNwE,QAAQ,EAAE,MAAM;8BAChB/L,KAAK,GAAA0N,uBAAA,GAAE/I,sBAAsB,CAAC,CAAC,CAAC,cAAA+I,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BtI,WAAW,cAAAuI,uBAAA,uBAAtCA,uBAAA,CAAwCR;4BAChD,CAAE;4BACFlC,SAAS,EAAC,kBAAkB;4BAAAP,QAAA,EAE3B7D,IAAI,CAACqD;0BAAW;4BAAAjC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACf;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACA,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC,GA5EDvB,IAAI,CAACd,EAAE;sBAAAkC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA6ER;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA,CACN;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAGNzI,OAAA;cACC4H,KAAK,EAAE;gBACNgE,KAAK,EAAE,KAAK;gBACZM,OAAO,EAAE;cACV,CAAE;cACFZ,SAAS,EAAC,cAAc;cAAAP,QAAA,gBAExB/K,OAAA;gBACE4H,KAAK,EAAE;kBACNS,OAAO,EAAE,MAAM;kBACf6C,UAAU,EAAE,QAAQ;kBACpBiD,YAAY,EAAE,KAAK;kBACnBvC,KAAK,EAAE,MAAM;kBACbI,GAAG,EAAE;gBACN,CAAE;gBAAAjB,QAAA,gBAEF/K,OAAA;kBACCkI,uBAAuB,EAAE;oBAAEC,MAAM,EAAEzI;kBAAS,CAAE;kBAC9CkI,KAAK,EAAE;oBACNwG,UAAU,EAAE,SAAS;oBACrB1C,YAAY,EAAE,KAAK;oBACnBQ,OAAO,EAAE,KAAK;oBACd7D,OAAO,EAAE,MAAM;oBACfgG,MAAM,EAAE;kBACT;gBAAE;kBAAA/F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACFzI,OAAA;kBACCkI,uBAAuB,EAAE;oBAAEC,MAAM,EAAE1I;kBAAgB,CAAE;kBACrDmI,KAAK,EAAE;oBACNwG,UAAU,EAAE,SAAS;oBACrB1C,YAAY,EAAE,KAAK;oBACnBQ,OAAO,EAAE,KAAK;oBACd7D,OAAO,EAAE,MAAM;oBACfgG,MAAM,EAAE;kBACT;gBAAE;kBAAA/F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACPzI,OAAA;gBACC4H,KAAK,EAAE;kBACNS,OAAO,EAAE,MAAM;kBACf6C,UAAU,EAAE,QAAQ;kBACpBa,aAAa,EAAE,QAAQ;kBACvBC,GAAG,EAAE,MAAM;kBACPH,MAAM,EAAE;gBACb,CAAE;gBAAAd,QAAA,eAIF/K,OAAA;kBAAK4H,KAAK,EAAE;oBACRkE,QAAQ,EAAE,aAAa;oBAACzD,OAAO,EAAE,MAAM;oBAC1C6C,UAAU,EAAE,QAAQ;oBACpBa,aAAa,EAAE,QAAQ;oBAACH,KAAK,EAAC;kBAAwB,CAAE;kBAAAb,QAAA,GAExD,CAAAT,YAAY,aAAZA,YAAY,wBAAA/F,qBAAA,GAAZ+F,YAAY,CAAEI,eAAe,cAAAnG,qBAAA,uBAA7BA,qBAAA,CAA+B4B,MAAM,IAAG,CAAC,iBACzCnG,OAAA,CAAAE,SAAA;oBAAA6K,QAAA,GACET,YAAY,CAACI,eAAe,CAAC4D,IAAI,CAAEC,IAAS;sBAAA,IAAAC,UAAA;sBAAA,OAAKD,IAAI,aAAJA,IAAI,wBAAAC,UAAA,GAAJD,IAAI,CAAEE,MAAM,cAAAD,UAAA,uBAAZA,UAAA,CAAcE,UAAU,CAAC,YAAY,CAAC;oBAAA,EAAC,iBACxF1O,OAAA,CAACV,aAAa;sBACbgL,YAAY,EAAEA,YAAa;sBAC3BrE,UAAU,EAAEA,UAAW;sBACvB0I,MAAM,EAAErE,YAAY,CAACI,eAAe,CAClCd,MAAM,CAAE2E,IAAS;wBAAA,IAAAK,WAAA;wBAAA,OAAKL,IAAI,aAAJA,IAAI,wBAAAK,WAAA,GAAJL,IAAI,CAAEE,MAAM,cAAAG,WAAA,uBAAZA,WAAA,CAAcF,UAAU,CAAC,YAAY,CAAC;sBAAA,EAAC,CAC7D/I,GAAG,CAAE4I,IAAS,IAAKA,IAAI,CAACE,MAAM,CAAE;sBAClCI,WAAW,EAAE;oBAAG;sBAAAvG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CACD,EAEA6B,YAAY,CAACI,eAAe,CAAC4D,IAAI,CAAEC,IAAS;sBAAA,IAAAO,WAAA;sBAAA,OAAKP,IAAI,aAAJA,IAAI,wBAAAO,WAAA,GAAJP,IAAI,CAAEE,MAAM,cAAAK,WAAA,uBAAZA,WAAA,CAAcJ,UAAU,CAAC,YAAY,CAAC;oBAAA,EAAC,IACxFpE,YAAY,CAACI,eAAe,CAC1Bd,MAAM,CAAE2E,IAAS;sBAAA,IAAAQ,WAAA;sBAAA,OAAKR,IAAI,aAAJA,IAAI,wBAAAQ,WAAA,GAAJR,IAAI,CAAEE,MAAM,cAAAM,WAAA,uBAAZA,WAAA,CAAcL,UAAU,CAAC,YAAY,CAAC;oBAAA,EAAC,CAC7D/I,GAAG,CAAC,CAAC4I,IAAS,EAAE1I,KAAa,kBAC7B7F,OAAA,CAACT,WAAW;sBAEXyP,SAAS,EAAET,IAAI,CAACE,MAAO;sBACvBI,WAAW,EAAE;oBAAG,GAFXhJ,KAAK;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACD,CAAC;kBAAA,eACH,CACF,EACA,CAAC,CAAA6B,YAAY,aAAZA,YAAY,wBAAA9F,sBAAA,GAAZ8F,YAAY,CAAEI,eAAe,cAAAlG,sBAAA,uBAA7BA,sBAAA,CAA+B2B,MAAM,MAAK,CAAC,IAAI,EAACmE,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEI,eAAe,mBAC9E1K,OAAA;oBAAK4H,KAAK,EAAE;sBAAEgE,KAAK,EAAE,MAAM;sBAAEC,MAAM,EAAE;oBAAQ,CAAE;oBAAAd,QAAA,gBAC9C/K,OAAA;sBAAMkI,uBAAuB,EAAE;wBAAEC,MAAM,EAAEvI;sBAAW;oBAAE;sBAAA0I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzDzI,OAAA;sBAAK4H,KAAK,EAAE;wBAAEvH,KAAK,EAAE;sBAAU,CAAE;sBAAA0K,QAAA,EAAEhG,SAAS,CAAC,iDAAiD,EAAE;wBAAE8B,YAAY,EAAE;sBAAkD,CAAC;oBAAC;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvK,CACL,eAEDzI,OAAA;oBACC4H,KAAK,EAAE;sBAAEgE,KAAK,EAAE,MAAM;sBAAE2B,SAAS,EAAE;oBAAO,CAAE;oBAC5CjC,SAAS,EAAC,eAAe;oBAAAP,QAAA,EAExBT,YAAY,iBACZtK,OAAA;sBAAK4H,KAAK,EAAE;wBAAEiE,MAAM,EAAE,MAAM;wBAAExD,OAAO,EAAE,MAAM;wBAAE0D,aAAa,EAAE;sBAAS,CAAE;sBAAAhB,QAAA,eACxE/K,OAAA;wBACC4H,KAAK,EAAE;0BACNuE,SAAS,EAAEvB,KAAK,GAAG,OAAO,GAAE,MAAM;0BAClCvC,OAAO,EAAE,MAAM;0BACf0D,aAAa,EAAE,QAAQ;0BACvBC,GAAG,EAAE;wBACN,CAAE;wBAAAjB,QAAA,gBAEF/K,OAAA;0BACC4H,KAAK,EAAE;4BACNwE,QAAQ,EAAE,MAAM;4BAChBC,UAAU,EAAE,GAAG;4BACfhM,KAAK,EAAE,MAAM;4BACbyL,QAAQ,EAAE,QAAQ;4BAClBY,YAAY,EAAE,UAAU;4BACxBC,UAAU,EAAE,QAAQ;4BACpBC,SAAS,EAAE;0BACZ,CAAE;0BAAA7B,QAAA,EAEDT,YAAY,CAACE;wBAAU;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpB,CAAC,eAENzI,OAAA;0BACCsL,SAAS,EAAC,YAAY;0BACtB1D,KAAK,EAAE;4BAAEvH,KAAK,EAAE;0BAAU,CAAE;0BAAA0K,QAAA,EAE3BT,YAAY,CAACG;wBAAgB;0BAAAnC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBACL;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzI,OAAA;gBACC4H,KAAK,EAAE;kBACNS,OAAO,EAAE,MAAM;kBACf2D,GAAG,EAAE,MAAM;kBACXd,UAAU,EAAE,QAAQ;kBACpBiD,YAAY,EAAE,KAAK;kBACnBc,aAAa,EAAE;gBAChB,CAAE;gBACF3D,SAAS,EAAC,cAAc;gBAAAP,QAAA,gBAExB/K,OAAA;kBACC4H,KAAK,EAAE;oBACNwD,eAAe,GAAA3G,uBAAA,GAAEO,sBAAsB,CAAC,CAAC,CAAC,cAAAP,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B6B,MAAM,cAAA5B,uBAAA,uBAAjCA,uBAAA,CAAmC6B,YAAY;oBAChEmF,YAAY,EAAE,MAAM;oBACpBQ,OAAO,EAAE,UAAU;oBACnB7L,KAAK,EAAE,MAAM;oBACbkL,MAAM,EAAE,MAAM;oBACd8C,MAAM,EAAE;kBACT,CAAE;kBAAAtD,QAAA,EAEDhG,SAAS,CAAC,WAAW,EAAE;oBAAE8B,YAAY,EAAE;kBAAY,CAAC;gBAAC;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,EACR,CAAA6B,YAAY,aAAZA,YAAY,wBAAA3F,sBAAA,GAAZ2F,YAAY,CAAEI,eAAe,cAAA/F,sBAAA,uBAA7BA,sBAAA,CAA+BwB,MAAM,IAAG,CAAC,iBACzCnG,OAAA;kBACC4H,KAAK,EAAE;oBACN8D,YAAY,EAAE,MAAM;oBACpBQ,OAAO,EAAE,UAAU;oBACnB7L,KAAK,GAAAuE,uBAAA,GAAEI,sBAAsB,CAAC,CAAC,CAAC,cAAAJ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B0B,MAAM,cAAAzB,uBAAA,uBAAjCA,uBAAA,CAAmC0B,YAAY;oBACtDgF,MAAM,EAAE,MAAM;oBACd6C,UAAU,EAAE,SAAS;oBACrBC,MAAM,EAAE;kBACT,CAAE;kBACFhD,OAAO,EAAEnB,qBAAsB;kBAAAa,QAAA,EAE9BhG,SAAS,CAAC,mBAAmB,EAAE;oBAAE8B,YAAY,EAAE;kBAAoB,CAAC;gBAAC;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA,eACL,CAAC;AAEL,CAAC;AAAC9G,EAAA,CA1rBIR,cAA6C;EAAA,QASzBrB,cAAc,EACiDV,cAAc;AAAA;AAAA8P,EAAA,GAVjG/N,cAA6C;AA4rBnD,MAAMgO,YAAY,GAAGA,CAAC;EAAE1N,cAAc;EAAEC;AAAuD,CAAC,KAAK;EAAA0N,GAAA;EAAA,IAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACpG,MAAM;IAAErM,sBAAsB;IAAEsM,uBAAuB;IAAEC,oBAAoB;IAAEC;EAAe,CAAC,GAAGpS,cAAc,CAC9GgG,KAAU,IAAKA,KACjB,CAAC;EACD,IAAIyD,UAAe;EACnB,MAAM,CAACd,KAAK,EAAEC,QAAQ,CAAC,GAAG9I,QAAQ,CAAQ,CACzC;IACCkH,EAAE,EAAE,CAAC;IACL6B,SAAS,eACRjI,OAAA;MACCkI,uBAAuB,EAAE;QAAEC,MAAM,EAAExI;MAAQ,CAAE;MAC7CiI,KAAK,EAAE;QAAEQ,IAAI,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACD;IACDC,QAAQ,EAAE;EACX,CAAC,CACD,CAAC;EACF,MAAMC,cAAc,GAAIC,SAAiB,IAAK;IAC7C,OAAO,6BAA6B5H,IAAI,CAAC4H,SAAS,CAAC,EAAE;EACtD,CAAC;EACD,MAAMQ,SAAS,GAAG,EAAAiG,uBAAA,GAAArK,sBAAsB,CAAC,CAAC,CAAC,cAAAqK,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BhG,QAAQ,cAAAiG,uBAAA,uBAAnCA,uBAAA,CAAqClG,SAAS,KAAI,MAAM,CAAC,CAAC;EAC5E,MAAME,eAAe,IAAAiG,uBAAA,GAAGvK,sBAAsB,CAAC,CAAC,CAAC,cAAAuK,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BlG,QAAQ,cAAAmG,uBAAA,uBAAnCA,uBAAA,CAAqCxG,IAAI;EAEjE,MAAMF,mBAAmB,GAAGf,KAAK,CAACgB,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACN,QAAQ,CAAC;EAC/D,IAAII,mBAAmB,EAAE;IAAA,IAAA2I,sBAAA;IACxB,MAAMvI,UAAU,IAAAuI,sBAAA,GAAG3I,mBAAmB,CAACb,SAAS,CAACkB,KAAK,CAACjB,uBAAuB,cAAAuJ,sBAAA,uBAA3DA,sBAAA,CAA6DtJ,MAAM;IAEtF,IAAIe,UAAU,EAAE;MACfL,UAAU,GAAGF,cAAc,CAACO,UAAU,CAAC;IACxC;EACD;EACA;EACA,MAAMwI,YAAY,GAAGvR,cAAc,CAACmJ,eAAe,IAAIT,UAAU,EAAEO,SAAS,CAAC;EAC7E,MAAM,CAAChI,MAAM,EAAEuQ,SAAS,CAAC,GAAGzS,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC0S,cAAc,EAAEC,iBAAiB,CAAC,GAAG3S,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAM4S,0BAA0B,GAAI1K,cAAsB,IAAK;IAC9DyK,iBAAiB,CAACzK,cAAc,CAAC;EAClC,CAAC;EAED,MAAM,CAAC2K,QAAQ,EAAEC,WAAW,CAAC,GAAG9S,QAAQ,CAAqBwI,QAAQ,CAACoD,IAAI,CAAC;EAC3E,MAAM,CAACmH,WAAW,EAAEC,cAAc,CAAC,GAAGhT,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACiT,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGlT,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmT,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpT,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAMqT,UAAU,GAAGhL,MAAM,CAACiL,QAAQ,CAACC,IAAI;EAEvC,oBACCzS,OAAA;IAAA+K,QAAA,gBAIC/K,OAAA;MACDsL,SAAS,EAAC,iBAAiB;MAAAP,QAAA,eAEzB/K,OAAA;QACCqL,OAAO,EAAEA,CAAA,KAAM;UACdsG,SAAS,CAAC,IAAI,CAAC;UACfL,uBAAuB,CAAC,IAAI,CAAC;QAC9B,CAAE;QACF1J,KAAK,EAAE;UACNwD,eAAe,GAAAqE,uBAAA,GAAEzK,sBAAsB,CAAC,CAAC,CAAC,cAAAyK,uBAAA,uBAAzBA,uBAAA,CAA2BpG,QAAQ,CAACqJ,aAAa;UAClErS,KAAK,EAAE,OAAO;UACdqL,YAAY,EACX,EAAAgE,uBAAA,GAAA1K,sBAAsB,CAAC,CAAC,CAAC,cAAA0K,uBAAA,uBAAzBA,uBAAA,CAA2BrG,QAAQ,CAACsJ,IAAI,MAAK,MAAM,IACnD,EAAAhD,uBAAA,GAAA3K,sBAAsB,CAAC,CAAC,CAAC,cAAA2K,uBAAA,uBAAzBA,uBAAA,CAA2BtG,QAAQ,CAACsJ,IAAI,MAAK,UAAU,GACpD,MAAM,GACN,KAAK;UACT9G,MAAM,EAAE,MAAM;UACdD,KAAK,EACJ,EAAAgE,uBAAA,GAAA5K,sBAAsB,CAAC,CAAC,CAAC,cAAA4K,uBAAA,uBAAzBA,uBAAA,CAA2BvG,QAAQ,CAACsJ,IAAI,MAAK,MAAM,IACnD,EAAA9C,uBAAA,GAAA7K,sBAAsB,CAAC,CAAC,CAAC,cAAA6K,uBAAA,uBAAzBA,uBAAA,CAA2BxG,QAAQ,CAACsJ,IAAI,MAAK,UAAU,GACpD,MAAM,GACN,MAAM;UACVtK,OAAO,EAAE,MAAM;UACf6C,UAAU,EAAE,QAAQ;UACpB8B,cAAc,EAAE,QAAQ;UACxBd,OAAO,EAAE,KAAK;UACd0G,SAAS,EAAE,yEAAyE;UACpFC,UAAU,EAAE,eAAe;UAC3BtH,MAAM,EAAE,MAAM;UACd8C,MAAM,EAAE,SAAS;UACjBrD,QAAQ,EAAE;QACX,CAAE;QAAAD,QAAA,GAED,EAAA+E,uBAAA,GAAA9K,sBAAsB,CAAC,CAAC,CAAC,cAAA8K,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BzG,QAAQ,cAAA0G,uBAAA,uBAAnCA,uBAAA,CAAqC4C,IAAI,MAAK,MAAM,iBACpD3S,OAAA;UACCmN,GAAG,EAAEuE,YAAa;UAClBtE,GAAG,EAAC,MAAM;UACVxF,KAAK,EAAE;YAAEgE,KAAK,EAAE,MAAM;YAAEC,MAAM,EAAE;UAAO;QAAE;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CACD,EAGA,EAAAuH,uBAAA,GAAAhL,sBAAsB,CAAC,CAAC,CAAC,cAAAgL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B3G,QAAQ,cAAA4G,uBAAA,uBAAnCA,uBAAA,CAAqC0C,IAAI,MAAK,MAAM,MAAAzC,uBAAA,GAAIlL,sBAAsB,CAAC,CAAC,CAAC,cAAAkL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B7G,QAAQ,cAAA8G,uBAAA,uBAAnCA,uBAAA,CAAqC2C,IAAI,kBACjG9S,OAAA;UACC4H,KAAK,EAAE;YACNwE,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,MAAM;YAClBhM,KAAK,GAAA+P,uBAAA,GAAEpL,sBAAsB,CAAC,CAAC,CAAC,cAAAoL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B/G,QAAQ,cAAAgH,uBAAA,uBAAnCA,uBAAA,CAAqC0C,SAAS;YACrD7G,OAAO,EAAE,KAAK;YACdS,UAAU,EAAE;UACb,CAAE;UAAA5B,QAAA,EAED/F,sBAAsB,CAAC,CAAC,CAAC,CAACqE,QAAQ,CAACyJ;QAAI;UAAAxK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACN,EAGA,EAAA6H,uBAAA,GAAAtL,sBAAsB,CAAC,CAAC,CAAC,cAAAsL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BjH,QAAQ,cAAAkH,uBAAA,uBAAnCA,uBAAA,CAAqCoC,IAAI,MAAK,UAAU,MAAAnC,uBAAA,GACxDxL,sBAAsB,CAAC,CAAC,CAAC,cAAAwL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BnH,QAAQ,cAAAoH,uBAAA,uBAAnCA,uBAAA,CAAqCqC,IAAI,OAAApC,uBAAA,GACzC1L,sBAAsB,CAAC,CAAC,CAAC,cAAA0L,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BrH,QAAQ,cAAAsH,uBAAA,uBAAnCA,uBAAA,CAAqC3H,IAAI,kBACxChJ,OAAA;UACC4H,KAAK,EAAE;YACNS,OAAO,EAAE,MAAM;YACf6C,UAAU,EAAE,QAAQ;YACpBc,GAAG,EAAE,KAAK;YACV3L,KAAK,GAAAuQ,uBAAA,GAAE5L,sBAAsB,CAAC,CAAC,CAAC,cAAA4L,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BvH,QAAQ,cAAAwH,uBAAA,uBAAnCA,uBAAA,CAAqCkC,SAAS;YACrD3G,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,MAAM;YAClBH,OAAO,EAAE;UACV,CAAE;UAAAnB,QAAA,gBAEF/K,OAAA;YACCmN,GAAG,EAAEuE,YAAa;YAClBtE,GAAG,EAAC,MAAM;YACVxF,KAAK,EAAE;cAAEgE,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,GAAAqI,uBAAA,GACD9L,sBAAsB,CAAC,CAAC,CAAC,cAAA8L,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2BzH,QAAQ,cAAA0H,uBAAA,uBAAnCA,uBAAA,CAAqC+B,IAAI;QAAA;UAAAxK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACN,EAGD,EAAAuI,uBAAA,GAAAhM,sBAAsB,CAAC,CAAC,CAAC,cAAAgM,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B3H,QAAQ,cAAA4H,uBAAA,uBAAnCA,uBAAA,CAAqC+B,iBAAiB,kBACtDhT,OAAA;UACC4H,KAAK,EAAE;YACNoD,QAAQ,EAAE,UAAU;YACpBiI,GAAG,EAAE,MAAM;YACXC,KAAK,EAAE,MAAM;YACb9H,eAAe,GAAA8F,uBAAA,GAAElM,sBAAsB,CAAC,CAAC,CAAC,cAAAkM,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B7H,QAAQ,cAAA8H,uBAAA,uBAAnCA,uBAAA,CAAqCgC,sBAAsB;YAC5E9S,KAAK,GAAA+Q,uBAAA,GAAEpM,sBAAsB,CAAC,CAAC,CAAC,cAAAoM,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA2B/H,QAAQ,cAAAgI,uBAAA,uBAAnCA,uBAAA,CAAqC+B,qBAAqB;YACjEhH,QAAQ,EAAE,MAAM;YAChBV,YAAY,EAAE,QAAQ;YACtBG,MAAM,EAAE,MAAM;YACdD,KAAK,EAAE,MAAM;YACbvD,OAAO,EAAE,MAAM;YACf6C,UAAU,EAAE,QAAQ;YACpB8B,cAAc,EAAE;UACjB,CAAE;UAAAjC,QAAA,EAED6G;QAAc;UAAAtJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEL8I,oBAAoB,iBACpBvR,OAAA,CAAAE,SAAA;MAAA6K,QAAA,eACC/K,OAAA,CAACX,gBAAgB;QAAAiJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC,gBACnB,CACF,eAGDzI,OAAA,CAACmB,cAAc;MACdI,IAAI,EAAE,EAAG;MACTC,YAAY,EAAE,EAAG;MACjBJ,MAAM,EAAEA,MAAO;MACfC,OAAO,EAAEA,CAAA,KAAMsQ,SAAS,CAAC,IAAI,CAAE;MAC/BrQ,sBAAsB,EAAEwQ,0BAA2B;MACnDrQ,cAAc,EAAE+P,cAAe;MAC/B9P,WAAW,EAAEA;IAAY;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAER,CAAC;AAAC2G,GAAA,CA7KID,YAAY;EAAA,QACiF/P,cAAc;AAAA;AAAAiU,GAAA,GAD3GlE,YAAY;AA+KlB,eAAeA,YAAY;AAAC,IAAAD,EAAA,EAAAmE,GAAA;AAAAC,YAAA,CAAApE,EAAA;AAAAoE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}