{"ast": null, "code": "import { Fragment, isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { isObject, isString, warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nexport const nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (isString(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if (isValidElement(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && isString(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      warn(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if (isObject(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      warn(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    warn(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, knownComponentsMap, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !knownComponentsMap && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = knownComponentsMap ?? {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (isString(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if (isObject(child) && !isValidElement(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return createElement(c.type, {\n          ...props,\n          key: i,\n          ref: c.props.ref ?? c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (!tmp && knownComponentsMap) tmp = knownComponentsMap[node.name];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && isObject(child) && child.dummy && !isElement;\n        const isKnownComponent = isObject(knownComponentsMap) && Object.hasOwnProperty.call(knownComponentsMap, node.name);\n        if (isString(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (isObject(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = cloneElement(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return createElement(Fragment, null, comp);\n  }\n  return createElement(Componentized, {\n    key: componentKey\n  });\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if (isObject(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  warnOnce(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nconst isComponentsMap = object => {\n  if (!isObject(object)) return false;\n  if (Array.isArray(object)) return false;\n  return Object.keys(object).reduce((acc, key) => acc && Number.isNaN(Number.parseFloat(key)), true);\n};\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  let indexedChildren = generatedComponents || children;\n  let componentsMap = null;\n  if (isComponentsMap(generatedComponents)) {\n    componentsMap = generatedComponents;\n    indexedChildren = children;\n  }\n  const content = renderNodes(indexedChildren, componentsMap, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}", "map": {"version": 3, "names": ["Fragment", "isValidElement", "cloneElement", "createElement", "Children", "HTML", "isObject", "isString", "warn", "warnOnce", "getDefaults", "getI18n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "checkLength", "base", "props", "children", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18nIsDynamicList", "getAsArray", "hasValidReactChildren", "Array", "isArray", "every", "data", "mergeProps", "source", "target", "newTarget", "Object", "assign", "nodesToString", "i18nOptions", "i18n", "i18nKey", "stringNode", "childrenA<PERSON>y", "keepArray", "transSupportBasicHtmlNodes", "transKeepBasicHtmlNodesFor", "for<PERSON>ach", "child", "childIndex", "type", "childPropsCount", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "format", "clone", "value", "renderNodes", "knownComponentsMap", "targetString", "combinedTOpts", "shouldUnescape", "emptyChildrenButNeedsHandling", "RegExp", "map", "keep", "join", "test", "getData", "childs", "ast", "parse", "opts", "renderInner", "rootReactNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapAST", "pushTranslatedJSX", "inner", "mem", "i", "isVoid", "dummy", "push", "key", "undefined", "c", "ref", "reactNode", "astNode", "reactNodes", "astNodes", "reduce", "translationContent", "services", "interpolator", "interpolate", "language", "tmp", "parseInt", "name", "attrs", "isElement", "isValidTranslationWithChildren", "voidElement", "isEmptyTransWithHTML", "isKnownComponent", "hasOwnProperty", "call", "Number", "isNaN", "parseFloat", "wrapTextNodes", "transWrapTextNodes", "unescape", "result", "fixComponentProps", "component", "index", "translation", "componentKey", "comp", "Componentized", "generateArrayComponents", "components", "generateObjectComponents", "componentMap", "generateComponents", "isComponentsMap", "object", "acc", "Trans", "count", "parent", "context", "tOptions", "values", "defaults", "ns", "i18nFromProps", "t", "tFromProps", "additionalProps", "bind", "k", "reactI18nextOptions", "options", "react", "namespaces", "defaultNS", "nodeAsString", "defaultValue", "transEmptyNodeValue", "hashTransKey", "interpolation", "defaultVariables", "interpolationOverride", "alwaysFormat", "prefix", "suffix", "generatedComponents", "indexedChildren", "componentsMap", "useAsParent", "defaultTransParent"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/node_modules/react-i18next/dist/es/TransWithoutContext.js"], "sourcesContent": ["import { Fragment, isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { isObject, isString, warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nexport const nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (isString(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if (isValidElement(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && isString(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      warn(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if (isObject(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      warn(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    warn(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, knownComponentsMap, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !knownComponentsMap && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = knownComponentsMap ?? {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (isString(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if (isObject(child) && !isValidElement(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return createElement(c.type, {\n          ...props,\n          key: i,\n          ref: c.props.ref ?? c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (!tmp && knownComponentsMap) tmp = knownComponentsMap[node.name];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && isObject(child) && child.dummy && !isElement;\n        const isKnownComponent = isObject(knownComponentsMap) && Object.hasOwnProperty.call(knownComponentsMap, node.name);\n        if (isString(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (isObject(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = cloneElement(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return createElement(Fragment, null, comp);\n  }\n  return createElement(Componentized, {\n    key: componentKey\n  });\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if (isObject(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  warnOnce(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nconst isComponentsMap = object => {\n  if (!isObject(object)) return false;\n  if (Array.isArray(object)) return false;\n  return Object.keys(object).reduce((acc, key) => acc && Number.isNaN(Number.parseFloat(key)), true);\n};\nexport function Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = isString(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  let indexedChildren = generatedComponents || children;\n  let componentsMap = null;\n  if (isComponentsMap(generatedComponents)) {\n    componentsMap = generatedComponents;\n    indexedChildren = children;\n  }\n  const content = renderNodes(indexedChildren, componentsMap, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AACvF,OAAOC,IAAI,MAAM,sBAAsB;AACvC,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,YAAY;AAC/D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,MAAMC,WAAW,GAAGA,CAACC,IAAI,EAAEC,WAAW,KAAK;EACzC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EACvB,MAAME,IAAI,GAAGF,IAAI,CAACG,KAAK,EAAEC,QAAQ,IAAIJ,IAAI,CAACI,QAAQ;EAClD,IAAIH,WAAW,EAAE,OAAOC,IAAI,CAACG,MAAM,GAAG,CAAC;EACvC,OAAO,CAAC,CAACH,IAAI;AACf,CAAC;AACD,MAAMI,WAAW,GAAGN,IAAI,IAAI;EAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,MAAMI,QAAQ,GAAGJ,IAAI,CAACG,KAAK,EAAEC,QAAQ,IAAIJ,IAAI,CAACI,QAAQ;EACtD,OAAOJ,IAAI,CAACG,KAAK,EAAEI,iBAAiB,GAAGC,UAAU,CAACJ,QAAQ,CAAC,GAAGA,QAAQ;AACxE,CAAC;AACD,MAAMK,qBAAqB,GAAGL,QAAQ,IAAIM,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,IAAIA,QAAQ,CAACQ,KAAK,CAACxB,cAAc,CAAC;AACnG,MAAMoB,UAAU,GAAGK,IAAI,IAAIH,KAAK,CAACC,OAAO,CAACE,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;AAC9D,MAAMC,UAAU,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACrC,MAAMC,SAAS,GAAG;IAChB,GAAGD;EACL,CAAC;EACDC,SAAS,CAACd,KAAK,GAAGe,MAAM,CAACC,MAAM,CAACJ,MAAM,CAACZ,KAAK,EAAEa,MAAM,CAACb,KAAK,CAAC;EAC3D,OAAOc,SAAS;AAClB,CAAC;AACD,OAAO,MAAMG,aAAa,GAAGA,CAAChB,QAAQ,EAAEiB,WAAW,EAAEC,IAAI,EAAEC,OAAO,KAAK;EACrE,IAAI,CAACnB,QAAQ,EAAE,OAAO,EAAE;EACxB,IAAIoB,UAAU,GAAG,EAAE;EACnB,MAAMC,aAAa,GAAGjB,UAAU,CAACJ,QAAQ,CAAC;EAC1C,MAAMsB,SAAS,GAAGL,WAAW,EAAEM,0BAA0B,GAAGN,WAAW,CAACO,0BAA0B,IAAI,EAAE,GAAG,EAAE;EAC7GH,aAAa,CAACI,OAAO,CAAC,CAACC,KAAK,EAAEC,UAAU,KAAK;IAC3C,IAAIrC,QAAQ,CAACoC,KAAK,CAAC,EAAE;MACnBN,UAAU,IAAI,GAAGM,KAAK,EAAE;MACxB;IACF;IACA,IAAI1C,cAAc,CAAC0C,KAAK,CAAC,EAAE;MACzB,MAAM;QACJ3B,KAAK;QACL6B;MACF,CAAC,GAAGF,KAAK;MACT,MAAMG,eAAe,GAAGf,MAAM,CAACgB,IAAI,CAAC/B,KAAK,CAAC,CAACE,MAAM;MACjD,MAAM8B,eAAe,GAAGT,SAAS,CAACU,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC;MACpD,MAAMK,aAAa,GAAGlC,KAAK,CAACC,QAAQ;MACpC,IAAI,CAACiC,aAAa,IAAIF,eAAe,IAAI,CAACF,eAAe,EAAE;QACzDT,UAAU,IAAI,IAAIQ,IAAI,IAAI;QAC1B;MACF;MACA,IAAI,CAACK,aAAa,KAAK,CAACF,eAAe,IAAIF,eAAe,CAAC,IAAI9B,KAAK,CAACI,iBAAiB,EAAE;QACtFiB,UAAU,IAAI,IAAIO,UAAU,MAAMA,UAAU,GAAG;QAC/C;MACF;MACA,IAAII,eAAe,IAAIF,eAAe,KAAK,CAAC,IAAIvC,QAAQ,CAAC2C,aAAa,CAAC,EAAE;QACvEb,UAAU,IAAI,IAAIQ,IAAI,IAAIK,aAAa,KAAKL,IAAI,GAAG;QACnD;MACF;MACA,MAAMM,OAAO,GAAGlB,aAAa,CAACiB,aAAa,EAAEhB,WAAW,EAAEC,IAAI,EAAEC,OAAO,CAAC;MACxEC,UAAU,IAAI,IAAIO,UAAU,IAAIO,OAAO,KAAKP,UAAU,GAAG;MACzD;IACF;IACA,IAAID,KAAK,KAAK,IAAI,EAAE;MAClBnC,IAAI,CAAC2B,IAAI,EAAE,kBAAkB,EAAE,iCAAiC,EAAE;QAChEC;MACF,CAAC,CAAC;MACF;IACF;IACA,IAAI9B,QAAQ,CAACqC,KAAK,CAAC,EAAE;MACnB,MAAM;QACJS,MAAM;QACN,GAAGC;MACL,CAAC,GAAGV,KAAK;MACT,MAAMI,IAAI,GAAGhB,MAAM,CAACgB,IAAI,CAACM,KAAK,CAAC;MAC/B,IAAIN,IAAI,CAAC7B,MAAM,KAAK,CAAC,EAAE;QACrB,MAAMoC,KAAK,GAAGF,MAAM,GAAG,GAAGL,IAAI,CAAC,CAAC,CAAC,KAAKK,MAAM,EAAE,GAAGL,IAAI,CAAC,CAAC,CAAC;QACxDV,UAAU,IAAI,KAAKiB,KAAK,IAAI;QAC5B;MACF;MACA9C,IAAI,CAAC2B,IAAI,EAAE,mBAAmB,EAAE,wFAAwF,EAAE;QACxHC,OAAO;QACPO;MACF,CAAC,CAAC;MACF;IACF;IACAnC,IAAI,CAAC2B,IAAI,EAAE,mBAAmB,EAAE,wGAAwG,EAAE;MACxIC,OAAO;MACPO;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAON,UAAU;AACnB,CAAC;AACD,MAAMkB,WAAW,GAAGA,CAACtC,QAAQ,EAAEuC,kBAAkB,EAAEC,YAAY,EAAEtB,IAAI,EAAED,WAAW,EAAEwB,aAAa,EAAEC,cAAc,KAAK;EACpH,IAAIF,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE;EAClC,MAAMlB,SAAS,GAAGL,WAAW,CAACO,0BAA0B,IAAI,EAAE;EAC9D,MAAMmB,6BAA6B,GAAGH,YAAY,IAAI,IAAII,MAAM,CAACtB,SAAS,CAACuB,GAAG,CAACC,IAAI,IAAI,IAAIA,IAAI,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACC,IAAI,CAACR,YAAY,CAAC;EAChI,IAAI,CAACxC,QAAQ,IAAI,CAACuC,kBAAkB,IAAI,CAACI,6BAA6B,IAAI,CAACD,cAAc,EAAE,OAAO,CAACF,YAAY,CAAC;EAChH,MAAM/B,IAAI,GAAG8B,kBAAkB,IAAI,CAAC,CAAC;EACrC,MAAMU,OAAO,GAAGC,MAAM,IAAI;IACxB,MAAM7B,aAAa,GAAGjB,UAAU,CAAC8C,MAAM,CAAC;IACxC7B,aAAa,CAACI,OAAO,CAACC,KAAK,IAAI;MAC7B,IAAIpC,QAAQ,CAACoC,KAAK,CAAC,EAAE;MACrB,IAAI/B,WAAW,CAAC+B,KAAK,CAAC,EAAEuB,OAAO,CAAC/C,WAAW,CAACwB,KAAK,CAAC,CAAC,CAAC,KAAK,IAAIrC,QAAQ,CAACqC,KAAK,CAAC,IAAI,CAAC1C,cAAc,CAAC0C,KAAK,CAAC,EAAEZ,MAAM,CAACC,MAAM,CAACN,IAAI,EAAEiB,KAAK,CAAC;IACpI,CAAC,CAAC;EACJ,CAAC;EACDuB,OAAO,CAACjD,QAAQ,CAAC;EACjB,MAAMmD,GAAG,GAAG/D,IAAI,CAACgE,KAAK,CAAC,MAAMZ,YAAY,MAAM,CAAC;EAChD,MAAMa,IAAI,GAAG;IACX,GAAG5C,IAAI;IACP,GAAGgC;EACL,CAAC;EACD,MAAMa,WAAW,GAAGA,CAAC5B,KAAK,EAAE9B,IAAI,EAAE2D,aAAa,KAAK;IAClD,MAAML,MAAM,GAAGhD,WAAW,CAACwB,KAAK,CAAC;IACjC,MAAM8B,cAAc,GAAGC,MAAM,CAACP,MAAM,EAAEtD,IAAI,CAACI,QAAQ,EAAEuD,aAAa,CAAC;IACnE,OAAOlD,qBAAqB,CAAC6C,MAAM,CAAC,IAAIM,cAAc,CAACvD,MAAM,KAAK,CAAC,IAAIyB,KAAK,CAAC3B,KAAK,EAAEI,iBAAiB,GAAG+C,MAAM,GAAGM,cAAc;EACjI,CAAC;EACD,MAAME,iBAAiB,GAAGA,CAAChC,KAAK,EAAEiC,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAEC,MAAM,KAAK;IAC1D,IAAIpC,KAAK,CAACqC,KAAK,EAAE;MACfrC,KAAK,CAAC1B,QAAQ,GAAG2D,KAAK;MACtBC,GAAG,CAACI,IAAI,CAAC/E,YAAY,CAACyC,KAAK,EAAE;QAC3BuC,GAAG,EAAEJ;MACP,CAAC,EAAEC,MAAM,GAAGI,SAAS,GAAGP,KAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACLC,GAAG,CAACI,IAAI,CAAC,GAAG7E,QAAQ,CAAC0D,GAAG,CAAC,CAACnB,KAAK,CAAC,EAAEyC,CAAC,IAAI;QACrC,MAAMpE,KAAK,GAAG;UACZ,GAAGoE,CAAC,CAACpE;QACP,CAAC;QACD,OAAOA,KAAK,CAACI,iBAAiB;QAC9B,OAAOjB,aAAa,CAACiF,CAAC,CAACvC,IAAI,EAAE;UAC3B,GAAG7B,KAAK;UACRkE,GAAG,EAAEJ,CAAC;UACNO,GAAG,EAAED,CAAC,CAACpE,KAAK,CAACqE,GAAG,IAAID,CAAC,CAACC;QACxB,CAAC,EAAEN,MAAM,GAAG,IAAI,GAAGH,KAAK,CAAC;MAC3B,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACD,MAAMF,MAAM,GAAGA,CAACY,SAAS,EAAEC,OAAO,EAAEf,aAAa,KAAK;IACpD,MAAMgB,UAAU,GAAGnE,UAAU,CAACiE,SAAS,CAAC;IACxC,MAAMG,QAAQ,GAAGpE,UAAU,CAACkE,OAAO,CAAC;IACpC,OAAOE,QAAQ,CAACC,MAAM,CAAC,CAACb,GAAG,EAAEhE,IAAI,EAAEiE,CAAC,KAAK;MACvC,MAAMa,kBAAkB,GAAG9E,IAAI,CAACI,QAAQ,GAAG,CAAC,CAAC,EAAEkC,OAAO,IAAIhB,IAAI,CAACyD,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACjF,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACkC,OAAO,EAAEmB,IAAI,EAAEnC,IAAI,CAAC4D,QAAQ,CAAC;MAC/I,IAAIlF,IAAI,CAACgC,IAAI,KAAK,KAAK,EAAE;QACvB,IAAImD,GAAG,GAAGR,UAAU,CAACS,QAAQ,CAACpF,IAAI,CAACqF,IAAI,EAAE,EAAE,CAAC,CAAC;QAC7C,IAAI,CAACF,GAAG,IAAIxC,kBAAkB,EAAEwC,GAAG,GAAGxC,kBAAkB,CAAC3C,IAAI,CAACqF,IAAI,CAAC;QACnE,IAAI1B,aAAa,CAACtD,MAAM,KAAK,CAAC,IAAI,CAAC8E,GAAG,EAAEA,GAAG,GAAGxB,aAAa,CAAC,CAAC,CAAC,CAAC3D,IAAI,CAACqF,IAAI,CAAC;QACzE,IAAI,CAACF,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;QAClB,MAAMrD,KAAK,GAAGZ,MAAM,CAACgB,IAAI,CAAClC,IAAI,CAACsF,KAAK,CAAC,CAACjF,MAAM,KAAK,CAAC,GAAGS,UAAU,CAAC;UAC9DX,KAAK,EAAEH,IAAI,CAACsF;QACd,CAAC,EAAEH,GAAG,CAAC,GAAGA,GAAG;QACb,MAAMI,SAAS,GAAGnG,cAAc,CAAC0C,KAAK,CAAC;QACvC,MAAM0D,8BAA8B,GAAGD,SAAS,IAAIxF,WAAW,CAACC,IAAI,EAAE,IAAI,CAAC,IAAI,CAACA,IAAI,CAACyF,WAAW;QAChG,MAAMC,oBAAoB,GAAG3C,6BAA6B,IAAItD,QAAQ,CAACqC,KAAK,CAAC,IAAIA,KAAK,CAACqC,KAAK,IAAI,CAACoB,SAAS;QAC1G,MAAMI,gBAAgB,GAAGlG,QAAQ,CAACkD,kBAAkB,CAAC,IAAIzB,MAAM,CAAC0E,cAAc,CAACC,IAAI,CAAClD,kBAAkB,EAAE3C,IAAI,CAACqF,IAAI,CAAC;QAClH,IAAI3F,QAAQ,CAACoC,KAAK,CAAC,EAAE;UACnB,MAAMW,KAAK,GAAGnB,IAAI,CAACyD,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACnD,KAAK,EAAE2B,IAAI,EAAEnC,IAAI,CAAC4D,QAAQ,CAAC;UAChFlB,GAAG,CAACI,IAAI,CAAC3B,KAAK,CAAC;QACjB,CAAC,MAAM,IAAI1C,WAAW,CAAC+B,KAAK,CAAC,IAAI0D,8BAA8B,EAAE;UAC/D,MAAMzB,KAAK,GAAGL,WAAW,CAAC5B,KAAK,EAAE9B,IAAI,EAAE2D,aAAa,CAAC;UACrDG,iBAAiB,CAAChC,KAAK,EAAEiC,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAIyB,oBAAoB,EAAE;UAC/B,MAAM3B,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAE3E,IAAI,CAACI,QAAQ,EAAEuD,aAAa,CAAC;UAC9DG,iBAAiB,CAAChC,KAAK,EAAEiC,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAI6B,MAAM,CAACC,KAAK,CAACC,UAAU,CAAChG,IAAI,CAACqF,IAAI,CAAC,CAAC,EAAE;UAC9C,IAAIM,gBAAgB,EAAE;YACpB,MAAM5B,KAAK,GAAGL,WAAW,CAAC5B,KAAK,EAAE9B,IAAI,EAAE2D,aAAa,CAAC;YACrDG,iBAAiB,CAAChC,KAAK,EAAEiC,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAEjE,IAAI,CAACyF,WAAW,CAAC;UAC3D,CAAC,MAAM,IAAIpE,WAAW,CAACM,0BAA0B,IAAID,SAAS,CAACU,OAAO,CAACpC,IAAI,CAACqF,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YACtF,IAAIrF,IAAI,CAACyF,WAAW,EAAE;cACpBzB,GAAG,CAACI,IAAI,CAAC9E,aAAa,CAACU,IAAI,CAACqF,IAAI,EAAE;gBAChChB,GAAG,EAAE,GAAGrE,IAAI,CAACqF,IAAI,IAAIpB,CAAC;cACxB,CAAC,CAAC,CAAC;YACL,CAAC,MAAM;cACL,MAAMF,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAE3E,IAAI,CAACI,QAAQ,EAAEuD,aAAa,CAAC;cAC9DK,GAAG,CAACI,IAAI,CAAC9E,aAAa,CAACU,IAAI,CAACqF,IAAI,EAAE;gBAChChB,GAAG,EAAE,GAAGrE,IAAI,CAACqF,IAAI,IAAIpB,CAAC;cACxB,CAAC,EAAEF,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,MAAM,IAAI/D,IAAI,CAACyF,WAAW,EAAE;YAC3BzB,GAAG,CAACI,IAAI,CAAC,IAAIpE,IAAI,CAACqF,IAAI,KAAK,CAAC;UAC9B,CAAC,MAAM;YACL,MAAMtB,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAE3E,IAAI,CAACI,QAAQ,EAAEuD,aAAa,CAAC;YAC9DK,GAAG,CAACI,IAAI,CAAC,IAAIpE,IAAI,CAACqF,IAAI,IAAItB,KAAK,KAAK/D,IAAI,CAACqF,IAAI,GAAG,CAAC;UACnD;QACF,CAAC,MAAM,IAAI5F,QAAQ,CAACqC,KAAK,CAAC,IAAI,CAACyD,SAAS,EAAE;UACxC,MAAMjD,OAAO,GAAGtC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAG0E,kBAAkB,GAAG,IAAI;UAC5D,IAAIxC,OAAO,EAAE0B,GAAG,CAACI,IAAI,CAAC9B,OAAO,CAAC;QAChC,CAAC,MAAM;UACLwB,iBAAiB,CAAChC,KAAK,EAAEgD,kBAAkB,EAAEd,GAAG,EAAEC,CAAC,EAAEjE,IAAI,CAACI,QAAQ,CAACC,MAAM,KAAK,CAAC,IAAI,CAACyE,kBAAkB,CAAC;QACzG;MACF,CAAC,MAAM,IAAI9E,IAAI,CAACgC,IAAI,KAAK,MAAM,EAAE;QAC/B,MAAMiE,aAAa,GAAG5E,WAAW,CAAC6E,kBAAkB;QACpD,MAAM5D,OAAO,GAAGQ,cAAc,GAAGzB,WAAW,CAAC8E,QAAQ,CAAC7E,IAAI,CAACyD,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACjF,IAAI,CAACsC,OAAO,EAAEmB,IAAI,EAAEnC,IAAI,CAAC4D,QAAQ,CAAC,CAAC,GAAG5D,IAAI,CAACyD,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACjF,IAAI,CAACsC,OAAO,EAAEmB,IAAI,EAAEnC,IAAI,CAAC4D,QAAQ,CAAC;QAC5M,IAAIe,aAAa,EAAE;UACjBjC,GAAG,CAACI,IAAI,CAAC9E,aAAa,CAAC2G,aAAa,EAAE;YACpC5B,GAAG,EAAE,GAAGrE,IAAI,CAACqF,IAAI,IAAIpB,CAAC;UACxB,CAAC,EAAE3B,OAAO,CAAC,CAAC;QACd,CAAC,MAAM;UACL0B,GAAG,CAACI,IAAI,CAAC9B,OAAO,CAAC;QACnB;MACF;MACA,OAAO0B,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACD,MAAMoC,MAAM,GAAGvC,MAAM,CAAC,CAAC;IACrBM,KAAK,EAAE,IAAI;IACX/D,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC,CAAC,EAAEmD,GAAG,EAAE/C,UAAU,CAACJ,QAAQ,IAAI,EAAE,CAAC,CAAC;EACpC,OAAOE,WAAW,CAAC8F,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC;AACD,MAAMC,iBAAiB,GAAGA,CAACC,SAAS,EAAEC,KAAK,EAAEC,WAAW,KAAK;EAC3D,MAAMC,YAAY,GAAGH,SAAS,CAACjC,GAAG,IAAIkC,KAAK;EAC3C,MAAMG,IAAI,GAAGrH,YAAY,CAACiH,SAAS,EAAE;IACnCjC,GAAG,EAAEoC;EACP,CAAC,CAAC;EACF,IAAI,CAACC,IAAI,CAACvG,KAAK,IAAI,CAACuG,IAAI,CAACvG,KAAK,CAACC,QAAQ,IAAIoG,WAAW,CAACpE,OAAO,CAAC,GAAGmE,KAAK,IAAI,CAAC,GAAG,CAAC,IAAIC,WAAW,CAACpE,OAAO,CAAC,GAAGmE,KAAK,KAAK,CAAC,GAAG,CAAC,EAAE;IAC1H,OAAOG,IAAI;EACb;EACA,SAASC,aAAaA,CAAA,EAAG;IACvB,OAAOrH,aAAa,CAACH,QAAQ,EAAE,IAAI,EAAEuH,IAAI,CAAC;EAC5C;EACA,OAAOpH,aAAa,CAACqH,aAAa,EAAE;IAClCtC,GAAG,EAAEoC;EACP,CAAC,CAAC;AACJ,CAAC;AACD,MAAMG,uBAAuB,GAAGA,CAACC,UAAU,EAAEL,WAAW,KAAKK,UAAU,CAAC5D,GAAG,CAAC,CAACsB,CAAC,EAAEgC,KAAK,KAAKF,iBAAiB,CAAC9B,CAAC,EAAEgC,KAAK,EAAEC,WAAW,CAAC,CAAC;AACnI,MAAMM,wBAAwB,GAAGA,CAACD,UAAU,EAAEL,WAAW,KAAK;EAC5D,MAAMO,YAAY,GAAG,CAAC,CAAC;EACvB7F,MAAM,CAACgB,IAAI,CAAC2E,UAAU,CAAC,CAAChF,OAAO,CAAC0C,CAAC,IAAI;IACnCrD,MAAM,CAACC,MAAM,CAAC4F,YAAY,EAAE;MAC1B,CAACxC,CAAC,GAAG8B,iBAAiB,CAACQ,UAAU,CAACtC,CAAC,CAAC,EAAEA,CAAC,EAAEiC,WAAW;IACtD,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOO,YAAY;AACrB,CAAC;AACD,MAAMC,kBAAkB,GAAGA,CAACH,UAAU,EAAEL,WAAW,EAAElF,IAAI,EAAEC,OAAO,KAAK;EACrE,IAAI,CAACsF,UAAU,EAAE,OAAO,IAAI;EAC5B,IAAInG,KAAK,CAACC,OAAO,CAACkG,UAAU,CAAC,EAAE;IAC7B,OAAOD,uBAAuB,CAACC,UAAU,EAAEL,WAAW,CAAC;EACzD;EACA,IAAI/G,QAAQ,CAACoH,UAAU,CAAC,EAAE;IACxB,OAAOC,wBAAwB,CAACD,UAAU,EAAEL,WAAW,CAAC;EAC1D;EACA5G,QAAQ,CAAC0B,IAAI,EAAE,0BAA0B,EAAE,wDAAwD,EAAE;IACnGC;EACF,CAAC,CAAC;EACF,OAAO,IAAI;AACb,CAAC;AACD,MAAM0F,eAAe,GAAGC,MAAM,IAAI;EAChC,IAAI,CAACzH,QAAQ,CAACyH,MAAM,CAAC,EAAE,OAAO,KAAK;EACnC,IAAIxG,KAAK,CAACC,OAAO,CAACuG,MAAM,CAAC,EAAE,OAAO,KAAK;EACvC,OAAOhG,MAAM,CAACgB,IAAI,CAACgF,MAAM,CAAC,CAACrC,MAAM,CAAC,CAACsC,GAAG,EAAE9C,GAAG,KAAK8C,GAAG,IAAIrB,MAAM,CAACC,KAAK,CAACD,MAAM,CAACE,UAAU,CAAC3B,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AACpG,CAAC;AACD,OAAO,SAAS+C,KAAKA,CAAC;EACpBhH,QAAQ;EACRiH,KAAK;EACLC,MAAM;EACN/F,OAAO;EACPgG,OAAO;EACPC,QAAQ,GAAG,CAAC,CAAC;EACbC,MAAM;EACNC,QAAQ;EACRb,UAAU;EACVc,EAAE;EACFrG,IAAI,EAAEsG,aAAa;EACnBC,CAAC,EAAEC,UAAU;EACbhF,cAAc;EACd,GAAGiF;AACL,CAAC,EAAE;EACD,MAAMzG,IAAI,GAAGsG,aAAa,IAAI9H,OAAO,CAAC,CAAC;EACvC,IAAI,CAACwB,IAAI,EAAE;IACT1B,QAAQ,CAAC0B,IAAI,EAAE,qBAAqB,EAAE,yEAAyE,EAAE;MAC/GC;IACF,CAAC,CAAC;IACF,OAAOnB,QAAQ;EACjB;EACA,MAAMyH,CAAC,GAAGC,UAAU,IAAIxG,IAAI,CAACuG,CAAC,CAACG,IAAI,CAAC1G,IAAI,CAAC,KAAK2G,CAAC,IAAIA,CAAC,CAAC;EACrD,MAAMC,mBAAmB,GAAG;IAC1B,GAAGrI,WAAW,CAAC,CAAC;IAChB,GAAGyB,IAAI,CAAC6G,OAAO,EAAEC;EACnB,CAAC;EACD,IAAIC,UAAU,GAAGV,EAAE,IAAIE,CAAC,CAACF,EAAE,IAAIrG,IAAI,CAAC6G,OAAO,EAAEG,SAAS;EACtDD,UAAU,GAAG3I,QAAQ,CAAC2I,UAAU,CAAC,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU,IAAI,CAAC,aAAa,CAAC;EAChF,MAAME,YAAY,GAAGnH,aAAa,CAAChB,QAAQ,EAAE8H,mBAAmB,EAAE5G,IAAI,EAAEC,OAAO,CAAC;EAChF,MAAMiH,YAAY,GAAGd,QAAQ,IAAIa,YAAY,IAAIL,mBAAmB,CAACO,mBAAmB,IAAIlH,OAAO;EACnG,MAAM;IACJmH;EACF,CAAC,GAAGR,mBAAmB;EACvB,MAAM7D,GAAG,GAAG9C,OAAO,KAAKmH,YAAY,GAAGA,YAAY,CAACH,YAAY,IAAIC,YAAY,CAAC,GAAGD,YAAY,IAAIC,YAAY,CAAC;EACjH,IAAIlH,IAAI,CAAC6G,OAAO,EAAEQ,aAAa,EAAEC,gBAAgB,EAAE;IACjDnB,MAAM,GAAGA,MAAM,IAAIvG,MAAM,CAACgB,IAAI,CAACuF,MAAM,CAAC,CAACpH,MAAM,GAAG,CAAC,GAAG;MAClD,GAAGoH,MAAM;MACT,GAAGnG,IAAI,CAAC6G,OAAO,CAACQ,aAAa,CAACC;IAChC,CAAC,GAAG;MACF,GAAGtH,IAAI,CAAC6G,OAAO,CAACQ,aAAa,CAACC;IAChC,CAAC;EACH;EACA,MAAMC,qBAAqB,GAAGpB,MAAM,IAAIJ,KAAK,KAAK/C,SAAS,IAAI,CAAChD,IAAI,CAAC6G,OAAO,EAAEQ,aAAa,EAAEG,YAAY,IAAI,CAAC1I,QAAQ,GAAGoH,QAAQ,CAACmB,aAAa,GAAG;IAChJA,aAAa,EAAE;MACb,GAAGnB,QAAQ,CAACmB,aAAa;MACzBI,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;IACV;EACF,CAAC;EACD,MAAMnG,aAAa,GAAG;IACpB,GAAG2E,QAAQ;IACXD,OAAO,EAAEA,OAAO,IAAIC,QAAQ,CAACD,OAAO;IACpCF,KAAK;IACL,GAAGI,MAAM;IACT,GAAGoB,qBAAqB;IACxBL,YAAY;IACZb,EAAE,EAAEU;EACN,CAAC;EACD,MAAM7B,WAAW,GAAGnC,GAAG,GAAGwD,CAAC,CAACxD,GAAG,EAAExB,aAAa,CAAC,GAAG2F,YAAY;EAC9D,MAAMS,mBAAmB,GAAGjC,kBAAkB,CAACH,UAAU,EAAEL,WAAW,EAAElF,IAAI,EAAEC,OAAO,CAAC;EACtF,IAAI2H,eAAe,GAAGD,mBAAmB,IAAI7I,QAAQ;EACrD,IAAI+I,aAAa,GAAG,IAAI;EACxB,IAAIlC,eAAe,CAACgC,mBAAmB,CAAC,EAAE;IACxCE,aAAa,GAAGF,mBAAmB;IACnCC,eAAe,GAAG9I,QAAQ;EAC5B;EACA,MAAMkC,OAAO,GAAGI,WAAW,CAACwG,eAAe,EAAEC,aAAa,EAAE3C,WAAW,EAAElF,IAAI,EAAE4G,mBAAmB,EAAErF,aAAa,EAAEC,cAAc,CAAC;EAClI,MAAMsG,WAAW,GAAG9B,MAAM,IAAIY,mBAAmB,CAACmB,kBAAkB;EACpE,OAAOD,WAAW,GAAG9J,aAAa,CAAC8J,WAAW,EAAErB,eAAe,EAAEzF,OAAO,CAAC,GAAGA,OAAO;AACrF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}