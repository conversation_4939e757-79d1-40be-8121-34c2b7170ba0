{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideBanners\\\\Banners.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from \"react\";\nimport { IconButton, Box, Typography, MobileStepper, Button, LinearProgress } from \"@mui/material\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\nimport { Link } from \"@mui/icons-material\";\nimport RTEsection from \"../guideSetting/PopupSections/RTEsection\";\nimport ImageSectionField from \"./selectedpopupfields/ImageSectionField\";\nimport ButtonSection from \"../guideSetting/PopupSections/Button\";\nimport HtmlSection from \"../guideSetting/PopupSections/HtmlSection\";\nimport EmojiPicker from \"emoji-picker-react\";\nimport \"./guideBanner.css\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport ButtonSettings from \"./selectedpopupfields/ButtonSettings\";\nimport Tooltip from \"@mui/material/Tooltip\";\nimport AlertPopup from \"../drawer/AlertPopup\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Banner = ({\n  setImageSrc,\n  imageSrc,\n  textBoxRef,\n  setHtmlContent,\n  htmlContent,\n  buttonColor,\n  setButtonColor,\n  setImageName,\n  imageName,\n  alignment,\n  setAlignment,\n  textvalue,\n  setTextvalue,\n  isBanner,\n  overlays,\n  setOverLays,\n  Bposition,\n  setBposition,\n  bpadding,\n  setbPadding,\n  isUnSavedChanges,\n  openWarning,\n  setopenWarning,\n  handleLeave,\n  savedGuideData,\n  Progress\n}) => {\n  _s();\n  var _savedGuideData$Guide, _savedGuideData$Guide2, _savedGuideData$Guide3, _bannerJson$GuideStep;\n  const guidePopUpRef = useRef(null);\n  const {\n    dismissData,\n    sectionColor,\n    setSectionColor,\n    buttonProperty,\n    setButtonProperty,\n    BborderSize,\n    Bbordercolor,\n    backgroundC,\n    textArray,\n    setBannerButtonSelected,\n    setTextArray,\n    preview,\n    setPreview,\n    bannerButtonSelected,\n    clearGuideDetails,\n    btnBgColor,\n    btnTextColor,\n    btnBorderColor,\n    buttonsContainer,\n    clearBannerButtonDetials,\n    setRTEAnchorEl,\n    deleteRTEContainer,\n    rteAnchorEl,\n    bannerJson,\n    currentStep,\n    selectedOption,\n    ProgressColor,\n    setProgressColor,\n    steps,\n    progress,\n    createWithAI,\n    selectedTemplate,\n    syncAIAnnouncementDataForPreview,\n    rtesContainer\n  } = useDrawerStore(state => state);\n  const {\n    t: translate\n  } = useTranslation();\n  const [textAreas, setTextAreas] = useState([[{\n    name: \"Rich Text\",\n    value: /*#__PURE__*/_jsxDEV(RTEsection, {\n      textBoxRef: textBoxRef,\n      isBanner: true,\n      index: 0,\n      handleDeleteRTESection: () => {}\n      // @ts-ignore\n      ,\n      ref: textBoxRef,\n      guidePopUpRef: guidePopUpRef\n    }, 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 6\n    }, this)\n  }]]);\n\n  // Effect to restore textAreas state from persisted store data on component mount\n  useEffect(() => {\n    // Only run this restoration logic once on component mount\n    const restoreTextAreasFromStore = () => {\n      const rowIndex = 0;\n      let restoredTextAreas = [...textAreas];\n      let hasChanges = false;\n\n      // Check if we need to restore button state from the store\n      if (bannerButtonSelected && !textAreas[rowIndex].some(item => item.name === \"Button\")) {\n        const newButton = {\n          name: \"Button\",\n          value: /*#__PURE__*/_jsxDEV(ButtonSection, {\n            buttonColor: buttonColor,\n            setButtonColor: setButtonColor,\n            isBanner: true\n          }, 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 7\n          }, this)\n        };\n        restoredTextAreas[rowIndex] = [...restoredTextAreas[rowIndex], newButton];\n        hasChanges = true;\n      }\n\n      // Check if RTE content exists in the store and needs to be restored\n      // For banners, RTE content is stored in rtesContainer\n      const hasRTEContent = rtesContainer && rtesContainer.length > 0 && rtesContainer[0].rtes && rtesContainer[0].rtes.length > 0 && rtesContainer[0].rtes[0].text && rtesContainer[0].rtes[0].text.trim() !== \"\";\n\n      // If there's RTE content in the store but the banner is not visible,\n      // it means we need to restore the textAreas properly\n      if (hasRTEContent) {\n        // console.log(\"Banner: Restoring RTE content from store\", {\n        // \trteContent: rtesContainer[0].rtes[0].text.substring(0, 50) + \"...\",\n        // \tcurrentTextAreas: textAreas.length\n        // });\n\n        // Ensure the RTE section is properly initialized with the stored content\n        // The RTEsection component will automatically pick up the content from rtesContainer\n        // We just need to make sure the textAreas structure is correct\n        const hasRTEInTextAreas = restoredTextAreas[rowIndex].some(item => item.name === \"Rich Text\");\n        if (!hasRTEInTextAreas) {\n          // This shouldn't happen normally, but if it does, recreate the RTE section\n          const rteSection = {\n            name: \"Rich Text\",\n            value: /*#__PURE__*/_jsxDEV(RTEsection, {\n              textBoxRef: textBoxRef,\n              isBanner: true,\n              index: 0,\n              handleDeleteRTESection: () => {}\n              // @ts-ignore\n              ,\n              ref: textBoxRef,\n              guidePopUpRef: guidePopUpRef\n            }, 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 8\n            }, this)\n          };\n          restoredTextAreas[rowIndex] = [rteSection, ...restoredTextAreas[rowIndex].filter(item => item.name !== \"Rich Text\")];\n          hasChanges = true;\n        }\n      }\n\n      // Update textAreas if changes were made\n      if (hasChanges) {\n        setTextAreas(restoredTextAreas);\n      }\n    };\n\n    // Run restoration logic after a small delay to ensure store is fully loaded\n    const timeoutId = setTimeout(restoreTextAreasFromStore, 100);\n    return () => clearTimeout(timeoutId);\n  }, []); // Empty dependency array - only run once on mount\n\n  // UseEffect to update textAreas when setBannerButtonSelected changes\n  useEffect(() => {\n    const rowIndex = 0; // Assuming we want to update the first row\n    const existingRow = textAreas[rowIndex];\n    if (bannerButtonSelected) {\n      // Check if Button already exists in the first row\n\n      // If Button is not already in the row, add it\n      if (!existingRow.some(item => item.name === \"Button\")) {\n        const newButton = {\n          name: \"Button\",\n          value: /*#__PURE__*/_jsxDEV(ButtonSection, {\n            buttonColor: buttonColor,\n            setButtonColor: setButtonColor,\n            isBanner: true\n          }, 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 5\n          }, this)\n        };\n        const updatedTextAreas = [...textAreas];\n        updatedTextAreas[rowIndex] = [...existingRow, newButton];\n        setTextAreas(updatedTextAreas);\n      }\n    } else {\n      // Find the button index and remove it\n      const buttonIndex = existingRow.findIndex(item => item.name === \"Button\");\n      if (buttonIndex !== -1) {\n        removeTextArea(rowIndex, buttonIndex);\n      }\n    }\n  }, [bannerButtonSelected]); // Trigger when setBannerButtonSelected changes\n\n  // Update textArray whenever textAreas changes\n  useEffect(() => {\n    setTextArray(textAreas);\n  }, [textAreas]);\n\n  // Sync AI announcement data on component mount to ensure progress bar is visible\n  useEffect(() => {\n    // Only run this for AI-created announcements\n    if (!createWithAI || selectedTemplate !== \"Announcement\") return;\n    console.log(\"Banner component mounted for AI announcement - syncing data\");\n\n    // Synchronize AI announcement data to ensure progress bar and other settings are properly initialized\n    syncAIAnnouncementDataForPreview(true); // Preserve global state during banner initialization\n  }, [createWithAI, selectedTemplate, syncAIAnnouncementDataForPreview]);\n\n  // Sync textAreas with AI-created guide data on mount and when relevant data changes\n  useEffect(() => {\n    // Only run this for AI-created guides\n    if (!createWithAI) return;\n    const rowIndex = 0;\n    const existingRow = textAreas[rowIndex];\n\n    // Check if we need to add a button based on the AI guide data\n    const hasButtonInData = bannerButtonSelected;\n    const hasButtonInTextAreas = existingRow.some(item => item.name === \"Button\");\n    if (hasButtonInData && !hasButtonInTextAreas) {\n      // Add button to textAreas if it exists in AI data but not in local state\n      const newButton = {\n        name: \"Button\",\n        value: /*#__PURE__*/_jsxDEV(ButtonSection, {\n          buttonColor: buttonColor,\n          setButtonColor: setButtonColor,\n          isBanner: true\n        }, 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 5\n        }, this)\n      };\n      const updatedTextAreas = [...textAreas];\n      updatedTextAreas[rowIndex] = [...existingRow, newButton];\n      setTextAreas(updatedTextAreas);\n    }\n  }, [createWithAI, bannerButtonSelected, buttonColor, setButtonColor]); // Dependencies for AI guide sync\n\n  // Additional effect to ensure button state is preserved for AI guides\n  useEffect(() => {\n    if (!createWithAI) return;\n\n    // Check if we have button data in the store but not in textAreas\n    const rowIndex = 0;\n    const existingRow = textAreas[rowIndex];\n    const hasButtonInTextAreas = existingRow.some(item => item.name === \"Button\");\n\n    // If buttonsContainer has buttons but textAreas doesn't, restore the button\n    if (buttonsContainer.length > 0 && !hasButtonInTextAreas && !bannerButtonSelected) {\n      setBannerButtonSelected(true);\n    }\n  }, [createWithAI, buttonsContainer, textAreas, bannerButtonSelected, setBannerButtonSelected]); // Monitor button container changes\n\n  // Handle overflow behavior based on banner position in creation mode\n  useEffect(() => {\n    // Use a small delay to ensure this runs after resetHeightofBanner\n    const timeoutId = setTimeout(() => {\n      // Only apply overflow hidden in creation/edit mode when position is \"Cover Top\"\n      // This component is only rendered in creation mode (when !showBannerenduser)\n      // Preview mode is handled by separate preview components\n      if (Bposition === \"Cover Top\") {\n        document.body.style.setProperty(\"overflow\", \"hidden\", \"important\");\n      } else {\n        // Restore normal scrolling for other positions\n        document.body.style.removeProperty(\"overflow\");\n      }\n    }, 100); // Small delay to ensure it runs after resetHeightofBanner\n\n    // Cleanup function to restore overflow when component unmounts\n    return () => {\n      clearTimeout(timeoutId);\n      document.body.style.removeProperty(\"overflow\");\n    };\n  }, [Bposition]); // Re-run when position changes\n\n  const overlayEnabled = useDrawerStore(state => state.overlayEnabled);\n  const [showOptions, setShowOptions] = useState(false);\n  const [focusedRowIndex, setFocusedRowIndex] = useState(null);\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n  const handleDeleteRTESection = index => {\n    const newTextAreas = [...textAreas];\n    newTextAreas.splice(index, 1);\n    setTextAreas(newTextAreas);\n    setTextArray(newTextAreas);\n  };\n  const addTextAreaInSameRow = (rowIndex, option) => {\n    if (!textAreas[rowIndex]) {\n      textAreas[rowIndex] = [];\n    }\n    const existingRow = textAreas[rowIndex];\n    // Check if a Rich Text or Button already exists in the row\n    if (option === \"richText\" && existingRow.some(item => item.name === \"Rich Text\") || option === \"button\" && existingRow.some(item => item.name === \"Button\")) {\n      alert(`Only one ${option === \"richText\" ? \"Rich Text\" : \"Button\"} is allowed per row.`);\n      return;\n    }\n    let newValue;\n    let newName = \"\";\n    switch (option) {\n      case \"image\":\n        newValue = /*#__PURE__*/_jsxDEV(ImageSectionField, {\n          setImageSrc: setImageSrc,\n          imageSrc: imageSrc,\n          setImageName: setImageName\n        }, rowIndex, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 6\n        }, this);\n        newName = \"Image\";\n        break;\n      case \"richText\":\n        // setBannerButtonSelected(false);\n\n        newValue = /*#__PURE__*/_jsxDEV(RTEsection, {\n          textBoxRef: textBoxRef,\n          isBanner: true,\n          index: rowIndex,\n          handleDeleteRTESection: handleDeleteRTESection\n          // @ts-ignore\n          ,\n          ref: textBoxRef,\n          guidePopUpRef: guidePopUpRef\n        }, rowIndex, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 6\n        }, this);\n        newName = \"Rich Text\";\n        break;\n      case \"button\":\n        setBannerButtonSelected(true);\n        newValue = /*#__PURE__*/_jsxDEV(ButtonSection, {\n          buttonColor: buttonColor,\n          setButtonColor: setButtonColor,\n          isBanner: true\n        }, rowIndex, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 6\n        }, this);\n        newName = \"Button\";\n        break;\n      case \"html\":\n        newValue = /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"htmlbanner\",\n          children: /*#__PURE__*/_jsxDEV(HtmlSection, {\n            htmlContent: htmlContent,\n            setHtmlContent: setHtmlContent,\n            isBanner: true\n          }, rowIndex, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 6\n        }, this);\n        newName = \"HTML\";\n        break;\n      default:\n        newValue = \"\";\n        newName = \"Text\";\n    }\n    const newTextAreas = [...textAreas];\n    newTextAreas[rowIndex] = [...existingRow, {\n      name: newName,\n      value: newValue\n    }];\n    setTextAreas(newTextAreas);\n    setTextArray(newTextAreas);\n  };\n  const removeTextArea = (rowIndex, textAreaIndex) => {\n    const updatedTextAreas = textAreas.map((row, index) => {\n      if (index === rowIndex) {\n        const filteredRow = row.filter((_, idx) => idx !== textAreaIndex);\n\n        // Check if there are any buttons remaining in the row after removal\n        const hasButtonInRow = filteredRow.some(t => t.name === \"Button\");\n        setBannerButtonSelected(hasButtonInRow);\n        return filteredRow;\n      }\n      return row;\n    });\n    if (textAreaIndex === 1) {\n      clearBannerButtonDetials();\n    } else if (textAreaIndex === 0) {\n      setRTEAnchorEl({\n        rteId: \"\",\n        containerId: \"\",\n        // @ts-ignore\n        value: null\n      });\n      deleteRTEContainer(rteAnchorEl.containerId);\n    }\n    setTextAreas(updatedTextAreas);\n    setTextArray(updatedTextAreas);\n  };\n  const enableProgress = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : (_savedGuideData$Guide2 = _savedGuideData$Guide[0]) === null || _savedGuideData$Guide2 === void 0 ? void 0 : (_savedGuideData$Guide3 = _savedGuideData$Guide2.Tooltip) === null || _savedGuideData$Guide3 === void 0 ? void 0 : _savedGuideData$Guide3.EnableProgress) || false;\n  function getProgressTemplate(selectedOption) {\n    var _savedGuideData$Guide4, _savedGuideData$Guide5, _savedGuideData$Guide6;\n    if (selectedOption === 1) {\n      return \"dots\";\n    } else if (selectedOption === 2) {\n      return \"linear\";\n    } else if (selectedOption === 3) {\n      return \"BreadCrumbs\";\n    } else if (selectedOption === 4) {\n      return \"breadcrumbs\";\n    }\n    return (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide4 = savedGuideData.GuideStep) === null || _savedGuideData$Guide4 === void 0 ? void 0 : (_savedGuideData$Guide5 = _savedGuideData$Guide4[0]) === null || _savedGuideData$Guide5 === void 0 ? void 0 : (_savedGuideData$Guide6 = _savedGuideData$Guide5.Tooltip) === null || _savedGuideData$Guide6 === void 0 ? void 0 : _savedGuideData$Guide6.ProgressTemplate) || \"dots\";\n  }\n  const progressTemplate = getProgressTemplate(selectedOption);\n  const renderProgress = () => {\n    var _savedGuideData$Guide7, _savedGuideData$Guide8, _savedGuideData$Guide9;\n    // Check both the global progress state and the saved guide data for progress settings\n    const enableProgressFromData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide7 = savedGuideData.GuideStep) === null || _savedGuideData$Guide7 === void 0 ? void 0 : (_savedGuideData$Guide8 = _savedGuideData$Guide7[0]) === null || _savedGuideData$Guide8 === void 0 ? void 0 : (_savedGuideData$Guide9 = _savedGuideData$Guide8.Tooltip) === null || _savedGuideData$Guide9 === void 0 ? void 0 : _savedGuideData$Guide9.EnableProgress;\n    const shouldShowProgress = progress || enableProgressFromData;\n    if (!shouldShowProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: steps.length,\n        position: \"static\",\n        activeStep: currentStep - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          padding: \"8px 0 0 0 !important\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 18\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          padding: \"8px 0 0 0 !important\",\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"5px\"\n        },\n        children: Array.from({\n          length: steps.length\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '14px',\n            height: '4px',\n            backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0',\n            // Active color and inactive color\n            borderRadius: '100px'\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 23\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 17\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          padding: \"8px 0 0 0 !important\",\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"flex-start\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: \"12px\",\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStep, \" of \", steps.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: \"calc(50% - 410px)\",\n          padding: \"8px 0 0 0\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: Progress,\n            sx: {\n              '& .MuiLinearProgress-bar': {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  const style = (_bannerJson$GuideStep = bannerJson.GuideStep.find(step => step.stepName === currentStep)) === null || _bannerJson$GuideStep === void 0 ? void 0 : _bannerJson$GuideStep.Canvas;\n  // Apply overflow hidden to body when canvas position is \"Cover Top\" in creation mode\n  useEffect(() => {\n    if ((style === null || style === void 0 ? void 0 : style.Position) === \"Cover Top\") {\n      document.body.style.overflow = \"hidden\";\n    } else {\n      document.body.style.overflow = \"\";\n    }\n\n    // Cleanup function to restore overflow when component unmounts\n    return () => {\n      document.body.style.overflow = \"\";\n    };\n  }, [style === null || style === void 0 ? void 0 : style.Position]); // Re-run when canvas position changes\n\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-container creation\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: \"qadpt-box\",\n        sx: {\n          padding: (style === null || style === void 0 ? void 0 : style.Padding) !== undefined && (style === null || style === void 0 ? void 0 : style.Padding) !== null ? `${style.Padding}px` : \"10px\",\n          boxShadow: style && (style === null || style === void 0 ? void 0 : style.Position) === \"Push Down\" ? \"none\" : \"0px 1px 15px rgba(0, 0, 0, 0.7)\",\n          borderTop: `${(style === null || style === void 0 ? void 0 : style.BorderSize) || 2}px solid ${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"#f0f0f0\"} !important`,\n          borderRight: `${(style === null || style === void 0 ? void 0 : style.BorderSize) || 2}px solid ${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"#f0f0f0\"} !important`,\n          borderLeft: `${(style === null || style === void 0 ? void 0 : style.BorderSize) || 2}px solid ${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"#f0f0f0\"} !important`,\n          borderBottom: `${(style === null || style === void 0 ? void 0 : style.BorderSize) || 2}px solid ${(style === null || style === void 0 ? void 0 : style.BorderColor) || \"#f0f0f0\"} !important`,\n          backgroundColor: `${style === null || style === void 0 ? void 0 : style.BackgroundColor} !important ` || \"#f0f0f0\",\n          position: (style === null || style === void 0 ? void 0 : style.Position) || \"Cover Top\",\n          zIndex: (style === null || style === void 0 ? void 0 : style.Zindex) || 9999\n        },\n        id: \"guide-popup\",\n        ref: guidePopUpRef,\n        children: [textArray.map((row, rowIndex) => /*#__PURE__*/_jsxDEV(Box, {\n          className: \"qadpt-row\",\n          children: [row.map((textArea, textAreaIndex) => /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-text-area-wrapper\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-text-area\",\n              style: {\n                backgroundColor: (textArea.name === \"RTE\" || textArea.name === \"Rich Text\") && sectionColor ? sectionColor : \"\"\n              },\n              children: textArea.value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 9\n            }, this), textArea.name === \"Button\" && /*#__PURE__*/_jsxDEV(IconButton, {\n              className: \"qadpt-add-btn\",\n              size: \"large\",\n              onClick: () => removeTextArea(rowIndex, textAreaIndex),\n              children: /*#__PURE__*/_jsxDEV(DeleteOutlineOutlinedIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 13\n            }, this)]\n          }, textAreaIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 7\n          }, this)), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            children: [row.some(item => item.name === \"Button\") ? /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: translate(\"Only one button is allowed\"),\n              placement: \"bottom\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: () => {\n                    setShowOptions(true);\n                    setFocusedRowIndex(rowIndex);\n                  },\n                  className: \"qadpt-add-btn\",\n                  size: \"small\",\n                  disabled: true,\n                  children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 15\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 13\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 5\n            }, this) : /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: () => {\n                setShowOptions(true);\n                setFocusedRowIndex(rowIndex);\n              },\n              className: \"qadpt-add-btn\",\n              size: \"small\",\n              disabled: row.some(item => item.name === \"Button\"),\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 5\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                // position: \"fixed\",\n                padding: \"3px\",\n                boxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\n                marginLeft: \"2px\",\n                background: \"#fff !important\",\n                border: \"1px solid #ccc\",\n                zIndex: \"999999\",\n                display: dismissData !== null && dismissData !== void 0 && dismissData.dismisssel ? \"flex\" : \"none\" // Show/hide based on dismisssel\n              },\n              children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n                sx: {\n                  zoom: \"1\",\n                  color: \"#000\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 6\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 7\n          }, this), isUnSavedChanges && openWarning && /*#__PURE__*/_jsxDEV(AlertPopup, {\n            openWarning: openWarning,\n            setopenWarning: setopenWarning,\n            handleLeave: handleLeave\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 4\n          }, this), showOptions && focusedRowIndex === rowIndex && /*#__PURE__*/_jsxDEV(Box, {\n            onMouseEnter: () => setShowOptions(true),\n            onMouseLeave: () => setShowOptions(false),\n            className: \"qadpt-options-menu\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-options-content\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                flexDirection: \"row\",\n                alignItems: \"center\",\n                sx: {\n                  cursor: \"pointer\",\n                  gap: \"10px\",\n                  placeContent: \"center\",\n                  width: \"100%\"\n                },\n                onClick: () => addTextAreaInSameRow(rowIndex, \"button\"),\n                children: [/*#__PURE__*/_jsxDEV(Link, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 708,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: translate(\"Button\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 709,\n                  columnNumber: 7\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 7\n          }, this)]\n        }, rowIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 3\n        }, this)), ((savedGuideData === null || savedGuideData === void 0 ? void 0 : savedGuideData.GuideType) === \"Tour\" || (savedGuideData === null || savedGuideData === void 0 ? void 0 : savedGuideData.GuideType) === \"Announcement\") && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ...(progressTemplate === \"linear\" && {\n              display: \"flex\",\n              placeContent: \"center\",\n              alignItems: \"center\"\n            })\n          },\n          children: steps.length >= 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: renderProgress()\n          }, void 0, false) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 1\n        }, this), showEmojiPicker && /*#__PURE__*/_jsxDEV(EmojiPicker, {\n          onEmojiClick: () => {}\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 26\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 588,\n      columnNumber: 4\n    }, this), buttonProperty && /*#__PURE__*/_jsxDEV(ButtonSettings, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 740,\n      columnNumber: 24\n    }, this)]\n  }, void 0, true);\n};\n_s(Banner, \"PG5d2iA3EqL05Rsgb1n+3GuwBzw=\", false, function () {\n  return [useDrawerStore, useTranslation, useDrawerStore];\n});\n_c = Banner;\nexport default Banner;\nvar _c;\n$RefreshReg$(_c, \"Banner\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "IconButton", "Box", "Typography", "MobileStepper", "<PERSON><PERSON>", "LinearProgress", "AddIcon", "DeleteOutlineOutlinedIcon", "Link", "RTEsection", "ImageSectionField", "ButtonSection", "HtmlSection", "EmojiPicker", "CloseIcon", "useDrawerStore", "ButtonSettings", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useTranslation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Banner", "setImageSrc", "imageSrc", "textBoxRef", "setHtmlContent", "htmlContent", "buttonColor", "setButtonColor", "setImageName", "imageName", "alignment", "setAlignment", "textvalue", "setTextvalue", "isBanner", "overlays", "setOverLays", "Bposition", "setBposition", "bpadding", "setbPadding", "isUnSavedChanges", "openWarning", "setopenWarning", "handleLeave", "savedGuideData", "Progress", "_s", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_savedGuideData$Guide3", "_bannerJson$GuideStep", "guidePopUpRef", "dismissData", "sectionColor", "setSectionColor", "buttonProperty", "setButtonProperty", "BborderSize", "Bbordercolor", "backgroundC", "textArray", "setBannerButtonSelected", "setTextArray", "preview", "setPreview", "bannerButtonSelected", "clearGuideDetails", "btnBgColor", "btnTextColor", "btnBorderColor", "buttonsContainer", "clearBannerButtonDetials", "setRTEAnchorEl", "deleteRTEContainer", "rteAnchorEl", "<PERSON><PERSON><PERSON>", "currentStep", "selectedOption", "ProgressColor", "setProgressColor", "steps", "progress", "createWithAI", "selectedTemplate", "syncAIAnnouncementDataForPreview", "rtesContainer", "state", "t", "translate", "textAreas", "setTextAreas", "name", "value", "index", "handleDeleteRTESection", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "restoreTextAreasFromStore", "rowIndex", "restoredTextAreas", "has<PERSON><PERSON><PERSON>", "some", "item", "newButton", "hasRTEContent", "length", "rtes", "text", "trim", "hasRTEInTextAreas", "rteSection", "filter", "timeoutId", "setTimeout", "clearTimeout", "existingRow", "updatedTextAreas", "buttonIndex", "findIndex", "removeTextArea", "console", "log", "hasButtonInData", "hasButtonInTextAreas", "document", "body", "style", "setProperty", "removeProperty", "overlayEnabled", "showOptions", "setShowOptions", "focusedRowIndex", "setFocusedRowIndex", "showEmojiPicker", "setShowEmojiPicker", "newTextAreas", "splice", "addTextAreaInSameRow", "option", "alert", "newValue", "newName", "className", "children", "textAreaIndex", "map", "row", "filteredRow", "_", "idx", "hasButtonInRow", "rteId", "containerId", "enableProgress", "GuideStep", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "ProgressTemplate", "progressTemplate", "renderProgress", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "enableProgressFromData", "shouldShowProgress", "variant", "position", "activeStep", "sx", "backgroundColor", "padding", "backButton", "visibility", "nextButton", "display", "alignItems", "place<PERSON><PERSON>nt", "gap", "Array", "from", "width", "height", "borderRadius", "fontSize", "color", "find", "step", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Position", "overflow", "Padding", "undefined", "boxShadow", "borderTop", "BorderSize", "BorderColor", "borderRight", "borderLeft", "borderBottom", "BackgroundColor", "zIndex", "Zindex", "id", "textArea", "size", "onClick", "title", "placement", "disabled", "marginLeft", "background", "border", "dismisssel", "zoom", "onMouseEnter", "onMouseLeave", "flexDirection", "cursor", "GuideType", "onEmojiClick", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/guideBanners/Banners.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from \"react\";\r\nimport { IconButton, Box, Typography, MobileStepper, Button, LinearProgress } from \"@mui/material\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\r\nimport { TextFormat, Link } from \"@mui/icons-material\";\r\nimport RTEsection from \"../guideSetting/PopupSections/RTEsection\";\r\nimport ImageSectionField from \"./selectedpopupfields/ImageSectionField\";\r\nimport ButtonSection from \"../guideSetting/PopupSections/Button\";\r\nimport HtmlSection from \"../guideSetting/PopupSections/HtmlSection\";\r\nimport EmojiPicker from \"emoji-picker-react\";\r\nimport \"./guideBanner.css\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport ButtonSettings from \"./selectedpopupfields/ButtonSettings\";\r\nimport { saveGuide } from \"../../services/GuideListServices\";\r\nimport Tooltip from \"@mui/material/Tooltip\";\r\nimport AlertPopup from \"../drawer/AlertPopup\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport { useTranslation } from \"react-i18next\";\r\nconst Banner = ({\r\n\tsetImageSrc,\r\n\timageSrc,\r\n\ttextBoxRef,\r\n\tsetHtmlContent,\r\n\thtmlContent,\r\n\tbuttonColor,\r\n\tsetButtonColor,\r\n\tsetImageName,\r\n\timageName,\r\n\talignment,\r\n\tsetAlignment,\r\n\ttextvalue,\r\n\tsetTextvalue,\r\n\tisBanner,\r\n\toverlays,\r\n\tsetOverLays,\r\n\tBposition,\r\n\tsetBposition,\r\n\tbpadding,\r\n\tsetbPadding,\r\n\tisUnSavedChanges,\r\n\topenWarning,\r\n\tsetopenWarning,\r\n\thandleLeave,\r\n\tsavedGuideData,\r\n\tProgress,\r\n}: {\r\n\tsetImageSrc: any;\r\n\timageSrc: any;\r\n\ttextBoxRef: any;\r\n\tsetHtmlContent: any;\r\n\thtmlContent: any;\r\n\tbuttonColor: any;\r\n\tsetButtonColor: any;\r\n\tsetImageName: any;\r\n\timageName: any;\r\n\talignment: any;\r\n\tsetAlignment: any;\r\n\ttextvalue: any;\r\n\tsetTextvalue: any;\r\n\tisBanner: boolean;\r\n\toverlays: boolean;\r\n\tsetOverLays: any;\r\n\tBposition: any;\r\n\tsetBposition: any;\r\n\tbpadding: any;\r\n\t\tsetbPadding: any;\r\n\t\tisUnSavedChanges: boolean;\r\n\t\topenWarning: boolean;\r\n\t\tsetopenWarning: (params: boolean) => void;\r\n\t\thandleLeave: () => void;\r\n\t\tsavedGuideData: GuideData | null;\r\n\t\tProgress: any;\r\n}) => {\r\n\tconst guidePopUpRef = useRef<HTMLDivElement | null>(null);\r\n\tconst {\r\n\t\tdismissData,\r\n\t\tsectionColor,\r\n\t\tsetSectionColor,\r\n\t\tbuttonProperty,\r\n\t\tsetButtonProperty,\r\n\t\tBborderSize,\r\n\t\tBbordercolor,\r\n\t\tbackgroundC,\r\n\t\ttextArray,\r\n\t\tsetBannerButtonSelected,\r\n\t\tsetTextArray,\r\n\t\tpreview,\r\n\t\tsetPreview,\r\n\t\tbannerButtonSelected,\r\n\t\tclearGuideDetails,\r\n\t\tbtnBgColor,\r\n\t\tbtnTextColor,\r\n\t\tbtnBorderColor,\r\n\t\tbuttonsContainer,\r\n\t\tclearBannerButtonDetials,\r\n\t\tsetRTEAnchorEl,\r\n\t\tdeleteRTEContainer,\r\n\t\trteAnchorEl,\r\n\t\tbannerJson,\r\n\t\tcurrentStep,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t\tsetProgressColor,\r\n\t\tsteps,\r\n\t\tprogress,\r\n\t\tcreateWithAI,\r\n\t\tselectedTemplate,\r\n\t\tsyncAIAnnouncementDataForPreview,\r\n\t\trtesContainer\r\n\t} = useDrawerStore((state) => state);\r\n\tconst { t: translate } = useTranslation()\r\n\tconst [textAreas, setTextAreas] = useState<{ name: string; value: string | JSX.Element }[][]>([\r\n\t\t[\r\n\t\t  {\r\n\t\t\tname: \"Rich Text\",\r\n\t\t\tvalue: (\r\n\t\t\t  <RTEsection\r\n\t\t\t\tkey={0}\r\n\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\tisBanner={true}\r\n\t\t\t\tindex={0}\r\n\t\t\t\thandleDeleteRTESection={() => {}}\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tref={textBoxRef}\r\n\t\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t  />\r\n\t\t\t),\r\n\t\t  },\r\n\t\t],\r\n\t  ]);\r\n\r\n\t  // Effect to restore textAreas state from persisted store data on component mount\r\n\t  useEffect(() => {\r\n\t\t// Only run this restoration logic once on component mount\r\n\t\tconst restoreTextAreasFromStore = () => {\r\n\t\t\tconst rowIndex = 0;\r\n\t\t\tlet restoredTextAreas = [...textAreas];\r\n\t\t\tlet hasChanges = false;\r\n\r\n\t\t\t// Check if we need to restore button state from the store\r\n\t\t\tif (bannerButtonSelected && !textAreas[rowIndex].some((item) => item.name === \"Button\")) {\r\n\t\t\t\tconst newButton = {\r\n\t\t\t\t\tname: \"Button\",\r\n\t\t\t\t\tvalue: (\r\n\t\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\t\tkey={0}\r\n\t\t\t\t\t\t\tbuttonColor={buttonColor}\r\n\t\t\t\t\t\t\tsetButtonColor={setButtonColor}\r\n\t\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t),\r\n\t\t\t\t};\r\n\t\t\t\trestoredTextAreas[rowIndex] = [...restoredTextAreas[rowIndex], newButton];\r\n\t\t\t\thasChanges = true;\r\n\t\t\t}\r\n\r\n\t\t\t// Check if RTE content exists in the store and needs to be restored\r\n\t\t\t// For banners, RTE content is stored in rtesContainer\r\n\t\t\tconst hasRTEContent = rtesContainer && rtesContainer.length > 0 &&\r\n\t\t\t\trtesContainer[0].rtes && rtesContainer[0].rtes.length > 0 &&\r\n\t\t\t\trtesContainer[0].rtes[0].text && rtesContainer[0].rtes[0].text.trim() !== \"\";\r\n\r\n\t\t\t// If there's RTE content in the store but the banner is not visible,\r\n\t\t\t// it means we need to restore the textAreas properly\r\n\t\t\tif (hasRTEContent) {\r\n\t\t\t\t// console.log(\"Banner: Restoring RTE content from store\", {\r\n\t\t\t\t// \trteContent: rtesContainer[0].rtes[0].text.substring(0, 50) + \"...\",\r\n\t\t\t\t// \tcurrentTextAreas: textAreas.length\r\n\t\t\t\t// });\r\n\r\n\t\t\t\t// Ensure the RTE section is properly initialized with the stored content\r\n\t\t\t\t// The RTEsection component will automatically pick up the content from rtesContainer\r\n\t\t\t\t// We just need to make sure the textAreas structure is correct\r\n\t\t\t\tconst hasRTEInTextAreas = restoredTextAreas[rowIndex].some((item) => item.name === \"Rich Text\");\r\n\r\n\t\t\t\tif (!hasRTEInTextAreas) {\r\n\t\t\t\t\t// This shouldn't happen normally, but if it does, recreate the RTE section\r\n\t\t\t\t\tconst rteSection = {\r\n\t\t\t\t\t\tname: \"Rich Text\",\r\n\t\t\t\t\t\tvalue: (\r\n\t\t\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\t\t\tkey={0}\r\n\t\t\t\t\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\t\t\tindex={0}\r\n\t\t\t\t\t\t\t\thandleDeleteRTESection={() => {}}\r\n\t\t\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\t\t\tref={textBoxRef}\r\n\t\t\t\t\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t),\r\n\t\t\t\t\t};\r\n\t\t\t\t\trestoredTextAreas[rowIndex] = [rteSection, ...restoredTextAreas[rowIndex].filter(item => item.name !== \"Rich Text\")];\r\n\t\t\t\t\thasChanges = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Update textAreas if changes were made\r\n\t\t\tif (hasChanges) {\r\n\t\t\t\tsetTextAreas(restoredTextAreas);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Run restoration logic after a small delay to ensure store is fully loaded\r\n\t\tconst timeoutId = setTimeout(restoreTextAreasFromStore, 100);\r\n\r\n\t\treturn () => clearTimeout(timeoutId);\r\n\t  }, []); // Empty dependency array - only run once on mount\r\n\r\n\t  // UseEffect to update textAreas when setBannerButtonSelected changes\r\n\tuseEffect(() => {\r\n\t\tconst rowIndex = 0; // Assuming we want to update the first row\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\t\tif (bannerButtonSelected) {\r\n\t\t  // Check if Button already exists in the first row\r\n\r\n\r\n\t\t  // If Button is not already in the row, add it\r\n\t\t  if (!existingRow.some((item) => item.name === \"Button\")) {\r\n\t\t\tconst newButton = {\r\n\t\t\t  name: \"Button\",\r\n\t\t\t  value: (\r\n\t\t\t\t<ButtonSection\r\n\t\t\t\t  key={0}\r\n\t\t\t\t  buttonColor={buttonColor}\r\n\t\t\t\t  setButtonColor={setButtonColor}\r\n\t\t\t\t  isBanner={true}\r\n\t\t\t\t/>\r\n\t\t\t  ),\r\n\t\t\t};\r\n\r\n\t\t\tconst updatedTextAreas = [...textAreas];\r\n\t\t\tupdatedTextAreas[rowIndex] = [...existingRow, newButton];\r\n\t\t\tsetTextAreas(updatedTextAreas);\r\n\t\t  }\r\n\t\t} else {\r\n\t\t\t// Find the button index and remove it\r\n\t\t\tconst buttonIndex = existingRow.findIndex((item) => item.name === \"Button\");\r\n\t\t\tif (buttonIndex !== -1) {\r\n\t\t\t\tremoveTextArea(rowIndex, buttonIndex);\r\n\t\t\t}\r\n\t\t}\r\n\t  }, [bannerButtonSelected]); // Trigger when setBannerButtonSelected changes\r\n\r\n\t  // Update textArray whenever textAreas changes\r\n\t  useEffect(() => {\r\n\t\tsetTextArray(textAreas);\r\n\t  }, [textAreas]);\r\n\r\n\t  // Sync AI announcement data on component mount to ensure progress bar is visible\r\n\t  useEffect(() => {\r\n\t\t// Only run this for AI-created announcements\r\n\t\tif (!createWithAI || selectedTemplate !== \"Announcement\") return;\r\n\r\n\t\tconsole.log(\"Banner component mounted for AI announcement - syncing data\");\r\n\r\n\t\t// Synchronize AI announcement data to ensure progress bar and other settings are properly initialized\r\n\t\tsyncAIAnnouncementDataForPreview(true); // Preserve global state during banner initialization\r\n\t  }, [createWithAI, selectedTemplate, syncAIAnnouncementDataForPreview]);\r\n\r\n\t  // Sync textAreas with AI-created guide data on mount and when relevant data changes\r\n\t  useEffect(() => {\r\n\t\t// Only run this for AI-created guides\r\n\t\tif (!createWithAI) return;\r\n\r\n\t\tconst rowIndex = 0;\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\r\n\t\t// Check if we need to add a button based on the AI guide data\r\n\t\tconst hasButtonInData = bannerButtonSelected;\r\n\t\tconst hasButtonInTextAreas = existingRow.some((item) => item.name === \"Button\");\r\n\r\n\t\tif (hasButtonInData && !hasButtonInTextAreas) {\r\n\t\t\t// Add button to textAreas if it exists in AI data but not in local state\r\n\t\t\tconst newButton = {\r\n\t\t\t  name: \"Button\",\r\n\t\t\t  value: (\r\n\t\t\t\t<ButtonSection\r\n\t\t\t\t  key={0}\r\n\t\t\t\t  buttonColor={buttonColor}\r\n\t\t\t\t  setButtonColor={setButtonColor}\r\n\t\t\t\t  isBanner={true}\r\n\t\t\t\t/>\r\n\t\t\t  ),\r\n\t\t\t};\r\n\r\n\t\t\tconst updatedTextAreas = [...textAreas];\r\n\t\t\tupdatedTextAreas[rowIndex] = [...existingRow, newButton];\r\n\t\t\tsetTextAreas(updatedTextAreas);\r\n\t\t}\r\n\t  }, [createWithAI, bannerButtonSelected, buttonColor, setButtonColor]); // Dependencies for AI guide sync\r\n\r\n\t  // Additional effect to ensure button state is preserved for AI guides\r\n\t  useEffect(() => {\r\n\t\tif (!createWithAI) return;\r\n\r\n\t\t// Check if we have button data in the store but not in textAreas\r\n\t\tconst rowIndex = 0;\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\t\tconst hasButtonInTextAreas = existingRow.some((item) => item.name === \"Button\");\r\n\r\n\t\t// If buttonsContainer has buttons but textAreas doesn't, restore the button\r\n\t\tif (buttonsContainer.length > 0 && !hasButtonInTextAreas && !bannerButtonSelected) {\r\n\t\t\tsetBannerButtonSelected(true);\r\n\t\t}\r\n\t  }, [createWithAI, buttonsContainer, textAreas, bannerButtonSelected, setBannerButtonSelected]); // Monitor button container changes\r\n\r\n\t// Handle overflow behavior based on banner position in creation mode\r\n\tuseEffect(() => {\r\n\t\t// Use a small delay to ensure this runs after resetHeightofBanner\r\n\t\tconst timeoutId = setTimeout(() => {\r\n\t\t\t// Only apply overflow hidden in creation/edit mode when position is \"Cover Top\"\r\n\t\t\t// This component is only rendered in creation mode (when !showBannerenduser)\r\n\t\t\t// Preview mode is handled by separate preview components\r\n\t\t\tif (Bposition === \"Cover Top\") {\r\n\t\t\t\tdocument.body.style.setProperty(\"overflow\", \"hidden\", \"important\");\r\n\t\t\t} else {\r\n\t\t\t\t// Restore normal scrolling for other positions\r\n\t\t\t\tdocument.body.style.removeProperty(\"overflow\");\r\n\t\t\t}\r\n\t\t}, 100); // Small delay to ensure it runs after resetHeightofBanner\r\n\r\n\t\t// Cleanup function to restore overflow when component unmounts\r\n\t\treturn () => {\r\n\t\t\tclearTimeout(timeoutId);\r\n\t\t\tdocument.body.style.removeProperty(\"overflow\");\r\n\t\t};\r\n\t}, [Bposition]); // Re-run when position changes\r\n\r\n\tconst overlayEnabled = useDrawerStore((state) => state.overlayEnabled);\r\n\tconst [showOptions, setShowOptions] = useState(false);\r\n\tconst [focusedRowIndex, setFocusedRowIndex] = useState<number | null>(null);\r\n\tconst [showEmojiPicker, setShowEmojiPicker] = useState(false);\r\n\r\n\tconst handleDeleteRTESection = (index: number) => {\r\n\t\tconst newTextAreas = [...textAreas];\r\n\t\tnewTextAreas.splice(index, 1);\r\n\t\tsetTextAreas(newTextAreas);\r\n\t\tsetTextArray(newTextAreas);\r\n\r\n\t};\r\n\r\n\tconst addTextAreaInSameRow = (rowIndex: number, option: string) => {\r\n\t\tif (!textAreas[rowIndex]) {\r\n\t\t\ttextAreas[rowIndex] = [];\r\n\t\t}\r\n\r\n\t\tconst existingRow = textAreas[rowIndex];\r\n\t\t// Check if a Rich Text or Button already exists in the row\r\n\t\tif (\r\n\t\t\t(option === \"richText\" && existingRow.some((item) => item.name === \"Rich Text\")) ||\r\n\t\t\t(option === \"button\" && existingRow.some((item) => item.name === \"Button\"))\r\n\t\t) {\r\n\t\t\talert(`Only one ${option === \"richText\" ? \"Rich Text\" : \"Button\"} is allowed per row.`);\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tlet newValue: JSX.Element | string;\r\n\t\tlet newName: string = \"\";\r\n\r\n\t\tswitch (option) {\r\n\t\t\tcase \"image\":\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<ImageSectionField\r\n\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\tsetImageSrc={setImageSrc}\r\n\t\t\t\t\t\timageSrc={imageSrc}\r\n\t\t\t\t\t\tsetImageName={setImageName}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"Image\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"richText\":\r\n\t\t\t\t// setBannerButtonSelected(false);\r\n\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<RTEsection\r\n\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\ttextBoxRef={textBoxRef}\r\n\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\tindex={rowIndex}\r\n\t\t\t\t\t\thandleDeleteRTESection={handleDeleteRTESection}\r\n\t\t\t\t\t\t// @ts-ignore\r\n\t\t\t\t\t\tref={textBoxRef}\r\n\t\t\t\t\t\tguidePopUpRef={guidePopUpRef}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"Rich Text\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"button\":\r\n\t\t\t\tsetBannerButtonSelected(true);\r\n\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<ButtonSection\r\n\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\tbuttonColor={buttonColor}\r\n\t\t\t\t\t\tsetButtonColor={setButtonColor}\r\n\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t/>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"Button\";\r\n\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"html\":\r\n\t\t\t\tnewValue = (\r\n\t\t\t\t\t<div className=\"htmlbanner\">\r\n\t\t\t\t\t\t<HtmlSection\r\n\t\t\t\t\t\t\tkey={rowIndex}\r\n\t\t\t\t\t\t\thtmlContent={htmlContent}\r\n\t\t\t\t\t\t\tsetHtmlContent={setHtmlContent}\r\n\t\t\t\t\t\t\tisBanner={true}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t);\r\n\t\t\t\tnewName = \"HTML\";\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tnewValue = \"\";\r\n\t\t\t\tnewName = \"Text\";\r\n\t\t}\r\n\r\n\r\n\t\tconst newTextAreas = [...textAreas];\r\n\r\n\t\tnewTextAreas[rowIndex] = [...existingRow, { name: newName, value: newValue }];\r\n\t\tsetTextAreas(newTextAreas);\r\n\t\tsetTextArray(newTextAreas);\r\n\t};\r\n\r\n\tconst removeTextArea = (rowIndex: number, textAreaIndex: number) => {\r\n\t\tconst updatedTextAreas = textAreas.map((row, index) => {\r\n\t\t\tif (index === rowIndex) {\r\n\t\t\t  const filteredRow = row.filter((_, idx) => idx !== textAreaIndex);\r\n\r\n\t\t\t  // Check if there are any buttons remaining in the row after removal\r\n\t\t\t  const hasButtonInRow = filteredRow.some((t) => t.name === \"Button\");\r\n\t\t\t  setBannerButtonSelected(hasButtonInRow);\r\n\r\n\t\t\t  return filteredRow;\r\n\t\t\t}\r\n\t\t\treturn row;\r\n\t\t});\r\n\t\tif (textAreaIndex === 1)\r\n\t\t{\r\n\t\t\tclearBannerButtonDetials();\r\n\t\t}\r\n\t\telse if (textAreaIndex === 0)\r\n\t\t{\r\n\t\t\tsetRTEAnchorEl({\r\n\t\t\t\trteId: \"\",\r\n\t\t\t\tcontainerId: \"\",\r\n\t\t\t\t// @ts-ignore\r\n\t\t\t\tvalue: null,\r\n\t\t\t});\r\n\r\n\t\t\tdeleteRTEContainer(rteAnchorEl.containerId);\r\n\t\t}\r\n\t\tsetTextAreas(updatedTextAreas);\r\n\t\tsetTextArray(updatedTextAreas);\r\n\r\n\t};\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t}\r\n        else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\t// Check both the global progress state and the saved guide data for progress settings\r\n\t\tconst enableProgressFromData = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress;\r\n\t\tconst shouldShowProgress = progress || enableProgressFromData;\r\n\r\n\t\tif(!shouldShowProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={steps.length}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{ backgroundColor: \"transparent\", padding:\"8px 0 0 0 !important\",  \"& .MuiMobileStepper-dotActive\": {\r\n                        backgroundColor: ProgressColor, // Active dot\r\n                      }, }}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n        if (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n                <Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\tplaceContent: \"center\",\r\n\t\t\t\t\tgap: \"5px\"}}>\r\n                  {/* Custom Step Indicators */}\r\n\r\n                    {Array.from({ length: steps.length }).map((_, index) => (\r\n                      <div\r\n                        key={index}\r\n                        style={{\r\n                          width: '14px',\r\n                          height: '4px',\r\n                          backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color\r\n                          borderRadius: '100px',\r\n                        }}\r\n                      />\r\n                    ))}\r\n\r\n                </Box>\r\n              );\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{padding:\"8px 0 0 0 !important\",display: \"flex\",\r\n\t\t\t\talignItems: \"center\",\r\n\t\t\t\tplaceContent: \"flex-start\",\r\n\t\t\t\t}}>\r\n\t\t\t\t\t<Typography sx={{fontSize:\"12px\", color: ProgressColor}} >\r\n\t\t\t\t\t\t Step {currentStep} of {steps.length}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{    width: \"calc(50% - 410px)\",\r\n\t\t\t\t\tpadding: \"8px 0 0 0\"}}>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={Progress}\r\n                            sx={{'& .MuiLinearProgress-bar': {\r\n                                backgroundColor: ProgressColor, // progress bar color\r\n                              },}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\tconst style = bannerJson.GuideStep.find((step:any) => step.stepName === currentStep)?.Canvas as\r\n\t\t| Record<string, unknown>\r\n\t\t| undefined;\r\n// Apply overflow hidden to body when canvas position is \"Cover Top\" in creation mode\r\nuseEffect(() => {\r\n\tif (style?.Position === \"Cover Top\") {\r\n\t\tdocument.body.style.overflow = \"hidden\";\r\n\t} else {\r\n\t\tdocument.body.style.overflow = \"\";\r\n\t}\r\n\r\n\t// Cleanup function to restore overflow when component unmounts\r\n\treturn () => {\r\n\t\tdocument.body.style.overflow = \"\";\r\n\t};\r\n}, [style?.Position]); // Re-run when canvas position changes\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{/* <Box\r\n\t\t\t\tsx={{\r\n\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\ttop: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\twidth: \"100vw\",\r\n\t\t\t\t\theight: \"17vh\",\r\n\t\t\t\t\tborderColor: (style?.BorderColor as string) || \"defaultColor\",\r\n    zIndex: overlays ? 1300 : \"\",\r\n    backgroundColor: style?.BackgroundColor as string | undefined,\r\n\t\t\t\t}}\r\n\t\t\t/> */}\r\n\r\n\t\t\t<div className=\"qadpt-container creation\">\r\n\t\t\t\t<Box\r\n\t\t\t\t\tclassName=\"qadpt-box\"\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tpadding: style?.Padding !== undefined && style?.Padding !== null ? `${style.Padding}px` : \"10px\",\r\n\t\t\t\t\t\tboxShadow: (style && (style?.Position === \"Push Down\")) ?  \"none\" : \"0px 1px 15px rgba(0, 0, 0, 0.7)\",\r\n\t\t\t\t\t\tborderTop:\r\n\t\t\t\t\t\t\t `${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\tborderRight:\r\n\t\t\t\t\t\t\t `${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important`\r\n\t\t\t\t\t\t\t\t,\r\n\t\t\t\t\t\t\tborderLeft:\r\n\t\t\t\t\t\t`${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important` ,\r\n\r\n\t\t\t\t\tborderBottom: `${style?.BorderSize || 2}px solid ${style?.BorderColor || \"#f0f0f0\"} !important`\r\n\t\t\t\t\t\t\t,\r\n\t\t\t\t\tbackgroundColor: `${style?.BackgroundColor} !important ` || \"#f0f0f0\",\r\n\t\t\t\t\tposition: style?.Position || \"Cover Top\",\r\n\t\t\t\t\tzIndex: style?.Zindex || 9999,\r\n\t\t\t\t}}\r\n\t\t\t\tid=\"guide-popup\"\r\n\t\t\t\tref={guidePopUpRef}\r\n\t\t\t>\r\n\r\n\r\n{(textArray ).map((row: any, rowIndex: number) => (\r\n  <Box key={rowIndex} className=\"qadpt-row\">\r\n    {row.map((textArea: any, textAreaIndex: number) => (\r\n      <Box key={textAreaIndex} className=\"qadpt-text-area-wrapper\">\r\n        <div\r\n          className=\"qadpt-text-area\"\r\n          style={{\r\n            backgroundColor:\r\n\t\t\t\t  (textArea.name === \"RTE\" || textArea.name === \"Rich Text\") && sectionColor ? sectionColor : \"\",\r\n          }}\r\n        >\r\n\t\t\t\t{textArea.value}\r\n        </div>\r\n          {textArea.name === \"Button\" && (\r\n            <IconButton\r\n              className=\"qadpt-add-btn\"\r\n              size=\"large\"\r\n              onClick={() => removeTextArea(rowIndex, textAreaIndex)}\r\n            >\r\n              <DeleteOutlineOutlinedIcon />\r\n            </IconButton>\r\n          )}\r\n      </Box>\r\n    ))}\r\n\r\n\r\n      <Box display=\"flex\" alignItems=\"center\">\r\n\t  {row.some((item: any) => item.name === \"Button\") ? (\r\n\t\t\t\t<Tooltip title={translate(\"Only one button is allowed\")} placement=\"bottom\">\r\n          <span>\r\n            <IconButton\r\n              onClick={() => {\r\n                setShowOptions(true);\r\n                setFocusedRowIndex(rowIndex);\r\n              }}\r\n              className=\"qadpt-add-btn\"\r\n              size=\"small\"\r\n\t\t\t  disabled={true}\r\n\t\t\t\t\t>\r\n              <AddIcon />\r\n            </IconButton>\r\n          </span>\r\n\t\t\t\t</Tooltip>\r\n\t\t\t) : (\r\n\t\t\t\t<IconButton\r\n\t\t\t\tonClick={() => {\r\n\t\t\t\t  setShowOptions(true);\r\n\t\t\t\t  setFocusedRowIndex(rowIndex);\r\n\t\t\t\t}}\r\n\t\t\t\tclassName=\"qadpt-add-btn\"\r\n\t\t\t\tsize=\"small\"\r\n\t\t\t\tdisabled={row.some((item: any) => item.name === \"Button\")}\r\n\t\t\t  >\r\n\t\t\t\t<AddIcon />\r\n\t\t\t  </IconButton>\r\n\t\t\t)}\r\n        {/* Always render the dismiss icon, but conditionally show/hide it based on dismissData.dismisssel */}\r\n        <IconButton\r\n          sx={{\r\n\t\t\t\t\t\t// position: \"fixed\",\r\n\t\t\t\t\t\tpadding:\"3px\",\r\n            boxShadow: \"rgba(0, 0, 0, 0.15) 0px 4px 8px\",\r\n            marginLeft: \"2px\",\r\n            background: \"#fff !important\",\r\n            border: \"1px solid #ccc\",\r\n            zIndex:\"999999\",\r\n            display: dismissData?.dismisssel ? \"flex\" : \"none\", // Show/hide based on dismisssel\r\n          }}\r\n        >\r\n\t\t\t\t\t<CloseIcon  sx={{zoom:\"1\",color:\"#000\" }}   />\r\n        </IconButton>\r\n      </Box>\r\n\t  {isUnSavedChanges && openWarning &&(\r\n\t\t\t<AlertPopup\r\n\t\t\t\t\topenWarning={openWarning}\r\n\t\t\t\t\tsetopenWarning={setopenWarning}\r\n\t\t\t\t\thandleLeave={handleLeave}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n    {showOptions && focusedRowIndex === rowIndex && (\r\n      <Box\r\n        onMouseEnter={() => setShowOptions(true)}\r\n        onMouseLeave={() => setShowOptions(false)}\r\n        className=\"qadpt-options-menu\"\r\n      >\r\n        <Box className=\"qadpt-options-content\">\r\n          <Box\r\n            display=\"flex\"\r\n            flexDirection=\"row\"\r\n            alignItems=\"center\"\r\n            sx={{ cursor: \"pointer\",gap : \"10px\",placeContent:\"center\",width:\"100%\" }}\r\n\r\n            onClick={() => addTextAreaInSameRow(rowIndex, \"button\")}\r\n\t\t\t\t\t>\r\n\t\t\t\t<Link />\r\n\t\t\t\t\t\t<Typography variant=\"caption\">{translate(\"Button\")}</Typography>\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n    )}\r\n  </Box>\r\n))}\r\n{(savedGuideData?.GuideType === \"Tour\" || savedGuideData?.GuideType === \"Announcement\") &&\r\n\r\n<Box\r\nsx={{\r\n  ...(progressTemplate === \"linear\" && {\r\n\tdisplay: \"flex\",\r\n\tplaceContent: \"center\",\r\n\talignItems:\"center\"\r\n  }),\r\n}}\r\n>\r\n\t\t{steps.length >= 1 ? (\r\n                    <>\r\n                        {renderProgress()}\r\n                    </>\r\n                ) : (\r\n                    null\r\n                )}\r\n\t\t\t\t</Box>\r\n\r\n}\r\n\t\t\t\t\t{showEmojiPicker && <EmojiPicker onEmojiClick={() => {}} />}\r\n\t\t\t\t</Box>\r\n\t\t\t</div>\r\n\t\t\t{buttonProperty  && <ButtonSettings />}\r\n\r\n\t\t\t{/* {dismissData?.dismisssel && (\r\n\t\t\t\t<IconButton\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tposition: \"absolute\",\r\n\t\t\t\t\t\ttop: \"8%\",\r\n\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\tcolor: \"red\",\r\n\t\t\t\t\t\tborderRadius: \"2px\",\r\n\t\t\t\t\t\t//padding: \"5px\",\r\n\t\t\t\t\t\tborderColor: \"var(--border-color)\",\r\n\t\t\t\t\t\tzIndex: \"999\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t</IconButton>\r\n\t\t\t)} */}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default Banner;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,aAAa,EAAEC,MAAM,EAAEC,cAAc,QAAQ,eAAe;AAClG,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,yBAAyB,MAAM,2CAA2C;AACjF,SAAqBC,IAAI,QAAQ,qBAAqB;AACtD,OAAOC,UAAU,MAAM,0CAA0C;AACjE,OAAOC,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,WAAW,MAAM,2CAA2C;AACnE,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAO,mBAAmB;AAC1B,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAiC,yBAAyB;AAC/E,OAAOC,cAAc,MAAM,sCAAsC;AAEjE,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,sBAAsB;AAE7C,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC/C,MAAMC,MAAM,GAAGA,CAAC;EACfC,WAAW;EACXC,QAAQ;EACRC,UAAU;EACVC,cAAc;EACdC,WAAW;EACXC,WAAW;EACXC,cAAc;EACdC,YAAY;EACZC,SAAS;EACTC,SAAS;EACTC,YAAY;EACZC,SAAS;EACTC,YAAY;EACZC,QAAQ;EACRC,QAAQ;EACRC,WAAW;EACXC,SAAS;EACTC,YAAY;EACZC,QAAQ;EACRC,WAAW;EACXC,gBAAgB;EAChBC,WAAW;EACXC,cAAc;EACdC,WAAW;EACXC,cAAc;EACdC;AA4BD,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EACL,MAAMC,aAAa,GAAG1D,MAAM,CAAwB,IAAI,CAAC;EACzD,MAAM;IACL2D,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,cAAc;IACdC,iBAAiB;IACjBC,WAAW;IACXC,YAAY;IACZC,WAAW;IACXC,SAAS;IACTC,uBAAuB;IACvBC,YAAY;IACZC,OAAO;IACPC,UAAU;IACVC,oBAAoB;IACpBC,iBAAiB;IACjBC,UAAU;IACVC,YAAY;IACZC,cAAc;IACdC,gBAAgB;IAChBC,wBAAwB;IACxBC,cAAc;IACdC,kBAAkB;IAClBC,WAAW;IACXC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,aAAa;IACbC,gBAAgB;IAChBC,KAAK;IACLC,QAAQ;IACRC,YAAY;IACZC,gBAAgB;IAChBC,gCAAgC;IAChCC;EACD,CAAC,GAAG3E,cAAc,CAAE4E,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAG1E,cAAc,CAAC,CAAC;EACzC,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAGlG,QAAQ,CAAoD,CAC7F,CACE;IACDmG,IAAI,EAAE,WAAW;IACjBC,KAAK,eACH5E,OAAA,CAACZ,UAAU;MAEZkB,UAAU,EAAEA,UAAW;MACvBW,QAAQ,EAAE,IAAK;MACf4D,KAAK,EAAE,CAAE;MACTC,sBAAsB,EAAEA,CAAA,KAAM,CAAC;MAC/B;MAAA;MACAC,GAAG,EAAEzE,UAAW;MACf6B,aAAa,EAAEA;IAAc,GAPzB,CAAC;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAQJ;EAEF,CAAC,CACF,CACC,CAAC;;EAEF;EACAzG,SAAS,CAAC,MAAM;IACjB;IACA,MAAM0G,yBAAyB,GAAGA,CAAA,KAAM;MACvC,MAAMC,QAAQ,GAAG,CAAC;MAClB,IAAIC,iBAAiB,GAAG,CAAC,GAAGb,SAAS,CAAC;MACtC,IAAIc,UAAU,GAAG,KAAK;;MAEtB;MACA,IAAItC,oBAAoB,IAAI,CAACwB,SAAS,CAACY,QAAQ,CAAC,CAACG,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC,EAAE;QACxF,MAAMe,SAAS,GAAG;UACjBf,IAAI,EAAE,QAAQ;UACdC,KAAK,eACJ5E,OAAA,CAACV,aAAa;YAEbmB,WAAW,EAAEA,WAAY;YACzBC,cAAc,EAAEA,cAAe;YAC/BO,QAAQ,EAAE;UAAK,GAHV,CAAC;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIN;QAEH,CAAC;QACDG,iBAAiB,CAACD,QAAQ,CAAC,GAAG,CAAC,GAAGC,iBAAiB,CAACD,QAAQ,CAAC,EAAEK,SAAS,CAAC;QACzEH,UAAU,GAAG,IAAI;MAClB;;MAEA;MACA;MACA,MAAMI,aAAa,GAAGtB,aAAa,IAAIA,aAAa,CAACuB,MAAM,GAAG,CAAC,IAC9DvB,aAAa,CAAC,CAAC,CAAC,CAACwB,IAAI,IAAIxB,aAAa,CAAC,CAAC,CAAC,CAACwB,IAAI,CAACD,MAAM,GAAG,CAAC,IACzDvB,aAAa,CAAC,CAAC,CAAC,CAACwB,IAAI,CAAC,CAAC,CAAC,CAACC,IAAI,IAAIzB,aAAa,CAAC,CAAC,CAAC,CAACwB,IAAI,CAAC,CAAC,CAAC,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE;;MAE7E;MACA;MACA,IAAIJ,aAAa,EAAE;QAClB;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA,MAAMK,iBAAiB,GAAGV,iBAAiB,CAACD,QAAQ,CAAC,CAACG,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,WAAW,CAAC;QAE/F,IAAI,CAACqB,iBAAiB,EAAE;UACvB;UACA,MAAMC,UAAU,GAAG;YAClBtB,IAAI,EAAE,WAAW;YACjBC,KAAK,eACJ5E,OAAA,CAACZ,UAAU;cAEVkB,UAAU,EAAEA,UAAW;cACvBW,QAAQ,EAAE,IAAK;cACf4D,KAAK,EAAE,CAAE;cACTC,sBAAsB,EAAEA,CAAA,KAAM,CAAC;cAC/B;cAAA;cACAC,GAAG,EAAEzE,UAAW;cAChB6B,aAAa,EAAEA;YAAc,GAPxB,CAAC;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQN;UAEH,CAAC;UACDG,iBAAiB,CAACD,QAAQ,CAAC,GAAG,CAACY,UAAU,EAAE,GAAGX,iBAAiB,CAACD,QAAQ,CAAC,CAACa,MAAM,CAACT,IAAI,IAAIA,IAAI,CAACd,IAAI,KAAK,WAAW,CAAC,CAAC;UACpHY,UAAU,GAAG,IAAI;QAClB;MACD;;MAEA;MACA,IAAIA,UAAU,EAAE;QACfb,YAAY,CAACY,iBAAiB,CAAC;MAChC;IACD,CAAC;;IAED;IACA,MAAMa,SAAS,GAAGC,UAAU,CAAChB,yBAAyB,EAAE,GAAG,CAAC;IAE5D,OAAO,MAAMiB,YAAY,CAACF,SAAS,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACFzH,SAAS,CAAC,MAAM;IACf,MAAM2G,QAAQ,GAAG,CAAC,CAAC,CAAC;IACpB,MAAMiB,WAAW,GAAG7B,SAAS,CAACY,QAAQ,CAAC;IACvC,IAAIpC,oBAAoB,EAAE;MACxB;;MAGA;MACA,IAAI,CAACqD,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC,EAAE;QAC1D,MAAMe,SAAS,GAAG;UAChBf,IAAI,EAAE,QAAQ;UACdC,KAAK,eACN5E,OAAA,CAACV,aAAa;YAEZmB,WAAW,EAAEA,WAAY;YACzBC,cAAc,EAAEA,cAAe;YAC/BO,QAAQ,EAAE;UAAK,GAHV,CAAC;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIP;QAEF,CAAC;QAED,MAAMoB,gBAAgB,GAAG,CAAC,GAAG9B,SAAS,CAAC;QACvC8B,gBAAgB,CAAClB,QAAQ,CAAC,GAAG,CAAC,GAAGiB,WAAW,EAAEZ,SAAS,CAAC;QACxDhB,YAAY,CAAC6B,gBAAgB,CAAC;MAC7B;IACF,CAAC,MAAM;MACN;MACA,MAAMC,WAAW,GAAGF,WAAW,CAACG,SAAS,CAAEhB,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC;MAC3E,IAAI6B,WAAW,KAAK,CAAC,CAAC,EAAE;QACvBE,cAAc,CAACrB,QAAQ,EAAEmB,WAAW,CAAC;MACtC;IACD;EACC,CAAC,EAAE,CAACvD,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAE5B;EACAvE,SAAS,CAAC,MAAM;IACjBoE,YAAY,CAAC2B,SAAS,CAAC;EACtB,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACA/F,SAAS,CAAC,MAAM;IACjB;IACA,IAAI,CAACwF,YAAY,IAAIC,gBAAgB,KAAK,cAAc,EAAE;IAE1DwC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;;IAE1E;IACAxC,gCAAgC,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,EAAE,CAACF,YAAY,EAAEC,gBAAgB,EAAEC,gCAAgC,CAAC,CAAC;;EAEtE;EACA1F,SAAS,CAAC,MAAM;IACjB;IACA,IAAI,CAACwF,YAAY,EAAE;IAEnB,MAAMmB,QAAQ,GAAG,CAAC;IAClB,MAAMiB,WAAW,GAAG7B,SAAS,CAACY,QAAQ,CAAC;;IAEvC;IACA,MAAMwB,eAAe,GAAG5D,oBAAoB;IAC5C,MAAM6D,oBAAoB,GAAGR,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC;IAE/E,IAAIkC,eAAe,IAAI,CAACC,oBAAoB,EAAE;MAC7C;MACA,MAAMpB,SAAS,GAAG;QAChBf,IAAI,EAAE,QAAQ;QACdC,KAAK,eACN5E,OAAA,CAACV,aAAa;UAEZmB,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/BO,QAAQ,EAAE;QAAK,GAHV,CAAC;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIP;MAEF,CAAC;MAED,MAAMoB,gBAAgB,GAAG,CAAC,GAAG9B,SAAS,CAAC;MACvC8B,gBAAgB,CAAClB,QAAQ,CAAC,GAAG,CAAC,GAAGiB,WAAW,EAAEZ,SAAS,CAAC;MACxDhB,YAAY,CAAC6B,gBAAgB,CAAC;IAC/B;EACC,CAAC,EAAE,CAACrC,YAAY,EAAEjB,oBAAoB,EAAExC,WAAW,EAAEC,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEvE;EACAhC,SAAS,CAAC,MAAM;IACjB,IAAI,CAACwF,YAAY,EAAE;;IAEnB;IACA,MAAMmB,QAAQ,GAAG,CAAC;IAClB,MAAMiB,WAAW,GAAG7B,SAAS,CAACY,QAAQ,CAAC;IACvC,MAAMyB,oBAAoB,GAAGR,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC;;IAE/E;IACA,IAAIrB,gBAAgB,CAACsC,MAAM,GAAG,CAAC,IAAI,CAACkB,oBAAoB,IAAI,CAAC7D,oBAAoB,EAAE;MAClFJ,uBAAuB,CAAC,IAAI,CAAC;IAC9B;EACC,CAAC,EAAE,CAACqB,YAAY,EAAEZ,gBAAgB,EAAEmB,SAAS,EAAExB,oBAAoB,EAAEJ,uBAAuB,CAAC,CAAC,CAAC,CAAC;;EAElG;EACAnE,SAAS,CAAC,MAAM;IACf;IACA,MAAMyH,SAAS,GAAGC,UAAU,CAAC,MAAM;MAClC;MACA;MACA;MACA,IAAIhF,SAAS,KAAK,WAAW,EAAE;QAC9B2F,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,WAAW,CAAC,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC;MACnE,CAAC,MAAM;QACN;QACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,cAAc,CAAC,UAAU,CAAC;MAC/C;IACD,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET;IACA,OAAO,MAAM;MACZd,YAAY,CAACF,SAAS,CAAC;MACvBY,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,cAAc,CAAC,UAAU,CAAC;IAC/C,CAAC;EACF,CAAC,EAAE,CAAC/F,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEjB,MAAMgG,cAAc,GAAG1H,cAAc,CAAE4E,KAAK,IAAKA,KAAK,CAAC8C,cAAc,CAAC;EACtE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9I,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+I,eAAe,EAAEC,kBAAkB,CAAC,GAAGhJ,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAACiJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGlJ,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAMsG,sBAAsB,GAAID,KAAa,IAAK;IACjD,MAAM8C,YAAY,GAAG,CAAC,GAAGlD,SAAS,CAAC;IACnCkD,YAAY,CAACC,MAAM,CAAC/C,KAAK,EAAE,CAAC,CAAC;IAC7BH,YAAY,CAACiD,YAAY,CAAC;IAC1B7E,YAAY,CAAC6E,YAAY,CAAC;EAE3B,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAACxC,QAAgB,EAAEyC,MAAc,KAAK;IAClE,IAAI,CAACrD,SAAS,CAACY,QAAQ,CAAC,EAAE;MACzBZ,SAAS,CAACY,QAAQ,CAAC,GAAG,EAAE;IACzB;IAEA,MAAMiB,WAAW,GAAG7B,SAAS,CAACY,QAAQ,CAAC;IACvC;IACA,IACEyC,MAAM,KAAK,UAAU,IAAIxB,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,WAAW,CAAC,IAC9EmD,MAAM,KAAK,QAAQ,IAAIxB,WAAW,CAACd,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAE,EAC1E;MACDoD,KAAK,CAAC,YAAYD,MAAM,KAAK,UAAU,GAAG,WAAW,GAAG,QAAQ,sBAAsB,CAAC;MACvF;IACD;IAEA,IAAIE,QAA8B;IAClC,IAAIC,OAAe,GAAG,EAAE;IAExB,QAAQH,MAAM;MACb,KAAK,OAAO;QACXE,QAAQ,gBACPhI,OAAA,CAACX,iBAAiB;UAEjBe,WAAW,EAAEA,WAAY;UACzBC,QAAQ,EAAEA,QAAS;UACnBM,YAAY,EAAEA;QAAa,GAHtB0E,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIb,CACD;QACD8C,OAAO,GAAG,OAAO;QACjB;MACD,KAAK,UAAU;QACd;;QAEAD,QAAQ,gBACPhI,OAAA,CAACZ,UAAU;UAEVkB,UAAU,EAAEA,UAAW;UACvBW,QAAQ,EAAE,IAAK;UACf4D,KAAK,EAAEQ,QAAS;UAChBP,sBAAsB,EAAEA;UACxB;UAAA;UACAC,GAAG,EAAEzE,UAAW;UAChB6B,aAAa,EAAEA;QAAc,GAPxBkD,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQb,CACD;QACD8C,OAAO,GAAG,WAAW;QACrB;MACD,KAAK,QAAQ;QACZpF,uBAAuB,CAAC,IAAI,CAAC;QAE7BmF,QAAQ,gBACPhI,OAAA,CAACV,aAAa;UAEbmB,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/BO,QAAQ,EAAE;QAAK,GAHVoE,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIb,CACD;QACD8C,OAAO,GAAG,QAAQ;QAElB;MACD,KAAK,MAAM;QACVD,QAAQ,gBACPhI,OAAA;UAAKkI,SAAS,EAAC,YAAY;UAAAC,QAAA,eAC1BnI,OAAA,CAACT,WAAW;YAEXiB,WAAW,EAAEA,WAAY;YACzBD,cAAc,EAAEA,cAAe;YAC/BU,QAAQ,EAAE;UAAK,GAHVoE,QAAQ;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACL;QACD8C,OAAO,GAAG,MAAM;QAChB;MACD;QACCD,QAAQ,GAAG,EAAE;QACbC,OAAO,GAAG,MAAM;IAClB;IAGA,MAAMN,YAAY,GAAG,CAAC,GAAGlD,SAAS,CAAC;IAEnCkD,YAAY,CAACtC,QAAQ,CAAC,GAAG,CAAC,GAAGiB,WAAW,EAAE;MAAE3B,IAAI,EAAEsD,OAAO;MAAErD,KAAK,EAAEoD;IAAS,CAAC,CAAC;IAC7EtD,YAAY,CAACiD,YAAY,CAAC;IAC1B7E,YAAY,CAAC6E,YAAY,CAAC;EAC3B,CAAC;EAED,MAAMjB,cAAc,GAAGA,CAACrB,QAAgB,EAAE+C,aAAqB,KAAK;IACnE,MAAM7B,gBAAgB,GAAG9B,SAAS,CAAC4D,GAAG,CAAC,CAACC,GAAG,EAAEzD,KAAK,KAAK;MACtD,IAAIA,KAAK,KAAKQ,QAAQ,EAAE;QACtB,MAAMkD,WAAW,GAAGD,GAAG,CAACpC,MAAM,CAAC,CAACsC,CAAC,EAAEC,GAAG,KAAKA,GAAG,KAAKL,aAAa,CAAC;;QAEjE;QACA,MAAMM,cAAc,GAAGH,WAAW,CAAC/C,IAAI,CAAEjB,CAAC,IAAKA,CAAC,CAACI,IAAI,KAAK,QAAQ,CAAC;QACnE9B,uBAAuB,CAAC6F,cAAc,CAAC;QAEvC,OAAOH,WAAW;MACpB;MACA,OAAOD,GAAG;IACX,CAAC,CAAC;IACF,IAAIF,aAAa,KAAK,CAAC,EACvB;MACC7E,wBAAwB,CAAC,CAAC;IAC3B,CAAC,MACI,IAAI6E,aAAa,KAAK,CAAC,EAC5B;MACC5E,cAAc,CAAC;QACdmF,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACf;QACAhE,KAAK,EAAE;MACR,CAAC,CAAC;MAEFnB,kBAAkB,CAACC,WAAW,CAACkF,WAAW,CAAC;IAC5C;IACAlE,YAAY,CAAC6B,gBAAgB,CAAC;IAC9BzD,YAAY,CAACyD,gBAAgB,CAAC;EAE/B,CAAC;EACD,MAAMsC,cAAc,GAAG,CAAAjH,cAAc,aAAdA,cAAc,wBAAAG,qBAAA,GAAdH,cAAc,CAAEkH,SAAS,cAAA/G,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCpC,OAAO,cAAAqC,sBAAA,uBAAvCA,sBAAA,CAAyC8G,cAAc,KAAI,KAAK;EACvF,SAASC,mBAAmBA,CAACnF,cAAmB,EAAE;IAAA,IAAAoF,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACjD,IAAItF,cAAc,KAAK,CAAC,EAAE;MACzB,OAAO,MAAM;IACd,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,QAAQ;IAChB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB,CAAC,MACU,IAAIA,cAAc,KAAK,CAAC,EAAE;MACpC,OAAO,aAAa;IACrB;IAEA,OAAO,CAAAjC,cAAc,aAAdA,cAAc,wBAAAqH,sBAAA,GAAdrH,cAAc,CAAEkH,SAAS,cAAAG,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgCtJ,OAAO,cAAAuJ,sBAAA,uBAAvCA,sBAAA,CAAyCC,gBAAgB,KAAI,MAAM;EAC3E;EACA,MAAMC,gBAAgB,GAAGL,mBAAmB,CAACnF,cAAc,CAAC;EAC5D,MAAMyF,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAC5B;IACA,MAAMC,sBAAsB,GAAG9H,cAAc,aAAdA,cAAc,wBAAA2H,sBAAA,GAAd3H,cAAc,CAAEkH,SAAS,cAAAS,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC5J,OAAO,cAAA6J,sBAAA,uBAAvCA,sBAAA,CAAyCV,cAAc;IACtF,MAAMY,kBAAkB,GAAG1F,QAAQ,IAAIyF,sBAAsB;IAE7D,IAAG,CAACC,kBAAkB,EAAE,OAAO,IAAI;IAEnC,IAAIN,gBAAgB,KAAK,MAAM,EAAE;MAChC,oBACCrJ,OAAA,CAAClB,aAAa;QACb8K,OAAO,EAAC,MAAM;QACd5F,KAAK,EAAEA,KAAK,CAAC4B,MAAO;QACpBiE,QAAQ,EAAC,QAAQ;QACjBC,UAAU,EAAElG,WAAW,GAAG,CAAE;QAC5BmG,EAAE,EAAE;UAAEC,eAAe,EAAE,aAAa;UAAEC,OAAO,EAAC,sBAAsB;UAAG,+BAA+B,EAAE;YACrFD,eAAe,EAAElG,aAAa,CAAE;UAClC;QAAG,CAAE;QACtBoG,UAAU,eAAElK,OAAA,CAACjB,MAAM;UAACkI,KAAK,EAAE;YAAEkD,UAAU,EAAE;UAAS;QAAE;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxDiF,UAAU,eAAEpK,OAAA,CAACjB,MAAM;UAACkI,KAAK,EAAE;YAAEkD,UAAU,EAAE;UAAS;QAAE;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEJ;IACM,IAAIkE,gBAAgB,KAAK,aAAa,EAAE;MAC7C,oBACarJ,OAAA,CAACpB,GAAG;QAACmL,EAAE,EAAE;UAACE,OAAO,EAAC,sBAAsB;UAACI,OAAO,EAAE,MAAM;UACnEC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE,QAAQ;UACtBC,GAAG,EAAE;QAAK,CAAE;QAAArC,QAAA,EAGIsC,KAAK,CAACC,IAAI,CAAC;UAAE9E,MAAM,EAAE5B,KAAK,CAAC4B;QAAO,CAAC,CAAC,CAACyC,GAAG,CAAC,CAACG,CAAC,EAAE3D,KAAK,kBACjD7E,OAAA;UAEEiH,KAAK,EAAE;YACL0D,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,KAAK;YACbZ,eAAe,EAAEnF,KAAK,KAAKjB,WAAW,GAAG,CAAC,GAAGE,aAAa,GAAG,SAAS;YAAE;YACxE+G,YAAY,EAAE;UAChB;QAAE,GANGhG,KAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAED,CAAC;IAEpB;IACA,IAAIkE,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACCrJ,OAAA,CAACpB,GAAG;QAACmL,EAAE,EAAE;UAACE,OAAO,EAAC,sBAAsB;UAACI,OAAO,EAAE,MAAM;UACxDC,UAAU,EAAE,QAAQ;UACpBC,YAAY,EAAE;QACd,CAAE;QAAApC,QAAA,eACDnI,OAAA,CAACnB,UAAU;UAACkL,EAAE,EAAE;YAACe,QAAQ,EAAC,MAAM;YAAEC,KAAK,EAAEjH;UAAa,CAAE;UAAAqE,QAAA,GAAE,OACnD,EAACvE,WAAW,EAAC,MAAI,EAACI,KAAK,CAAC4B,MAAM;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAIkE,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACCrJ,OAAA,CAACpB,GAAG;QAACmL,EAAE,EAAE;UAAKY,KAAK,EAAE,mBAAmB;UACvCV,OAAO,EAAE;QAAW,CAAE;QAAA9B,QAAA,eACtBnI,OAAA,CAACnB,UAAU;UAAC+K,OAAO,EAAC,OAAO;UAAAzB,QAAA,eAC1BnI,OAAA,CAAChB,cAAc;YACd4K,OAAO,EAAC,aAAa;YACrBhF,KAAK,EAAE/C,QAAS;YACKkI,EAAE,EAAE;cAAC,0BAA0B,EAAE;gBAC7BC,eAAe,EAAElG,aAAa,CAAE;cAClC;YAAE;UAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACZ,CAAC;EACD,MAAM8B,KAAK,IAAA/E,qBAAA,GAAGyB,UAAU,CAACmF,SAAS,CAACkC,IAAI,CAAEC,IAAQ,IAAKA,IAAI,CAACC,QAAQ,KAAKtH,WAAW,CAAC,cAAA1B,qBAAA,uBAAtEA,qBAAA,CAAwEiJ,MAE1E;EACb;EACAzM,SAAS,CAAC,MAAM;IACf,IAAI,CAAAuI,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmE,QAAQ,MAAK,WAAW,EAAE;MACpCrE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACoE,QAAQ,GAAG,QAAQ;IACxC,CAAC,MAAM;MACNtE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACoE,QAAQ,GAAG,EAAE;IAClC;;IAEA;IACA,OAAO,MAAM;MACZtE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACoE,QAAQ,GAAG,EAAE;IAClC,CAAC;EACF,CAAC,EAAE,CAACpE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtB,oBACCpL,OAAA,CAAAE,SAAA;IAAAiI,QAAA,gBAcCnI,OAAA;MAAKkI,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACxCnI,OAAA,CAACpB,GAAG;QACHsJ,SAAS,EAAC,WAAW;QACrB6B,EAAE,EAAE;UACHE,OAAO,EAAE,CAAAhD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqE,OAAO,MAAKC,SAAS,IAAI,CAAAtE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqE,OAAO,MAAK,IAAI,GAAG,GAAGrE,KAAK,CAACqE,OAAO,IAAI,GAAG,MAAM;UAChGE,SAAS,EAAGvE,KAAK,IAAK,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmE,QAAQ,MAAK,WAAY,GAAK,MAAM,GAAG,iCAAiC;UACrGK,SAAS,EACP,GAAG,CAAAxE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyE,UAAU,KAAI,CAAC,YAAY,CAAAzE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0E,WAAW,KAAI,SAAS,aAAa;UAElFC,WAAW,EACV,GAAG,CAAA3E,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyE,UAAU,KAAI,CAAC,YAAY,CAAAzE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0E,WAAW,KAAI,SAAS,aAAa;UAElFE,UAAU,EACX,GAAG,CAAA5E,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyE,UAAU,KAAI,CAAC,YAAY,CAAAzE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0E,WAAW,KAAI,SAAS,aAAa;UAElFG,YAAY,EAAE,GAAG,CAAA7E,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyE,UAAU,KAAI,CAAC,YAAY,CAAAzE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0E,WAAW,KAAI,SAAS,aAAa;UAE/F3B,eAAe,EAAE,GAAG/C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE8E,eAAe,cAAc,IAAI,SAAS;UACrElC,QAAQ,EAAE,CAAA5C,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmE,QAAQ,KAAI,WAAW;UACxCY,MAAM,EAAE,CAAA/E,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgF,MAAM,KAAI;QAC1B,CAAE;QACFC,EAAE,EAAC,aAAa;QAChBnH,GAAG,EAAE5C,aAAc;QAAAgG,QAAA,GAIrBvF,SAAS,CAAGyF,GAAG,CAAC,CAACC,GAAQ,EAAEjD,QAAgB,kBAC3CrF,OAAA,CAACpB,GAAG;UAAgBsJ,SAAS,EAAC,WAAW;UAAAC,QAAA,GACtCG,GAAG,CAACD,GAAG,CAAC,CAAC8D,QAAa,EAAE/D,aAAqB,kBAC5CpI,OAAA,CAACpB,GAAG;YAAqBsJ,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBAC1DnI,OAAA;cACEkI,SAAS,EAAC,iBAAiB;cAC3BjB,KAAK,EAAE;gBACL+C,eAAe,EACrB,CAACmC,QAAQ,CAACxH,IAAI,KAAK,KAAK,IAAIwH,QAAQ,CAACxH,IAAI,KAAK,WAAW,KAAKtC,YAAY,GAAGA,YAAY,GAAG;cACxF,CAAE;cAAA8F,QAAA,EAEPgE,QAAQ,CAACvH;YAAK;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACHgH,QAAQ,CAACxH,IAAI,KAAK,QAAQ,iBACzB3E,OAAA,CAACrB,UAAU;cACTuJ,SAAS,EAAC,eAAe;cACzBkE,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEA,CAAA,KAAM3F,cAAc,CAACrB,QAAQ,EAAE+C,aAAa,CAAE;cAAAD,QAAA,eAEvDnI,OAAA,CAACd,yBAAyB;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CACb;UAAA,GAlBKiD,aAAa;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBlB,CACN,CAAC,eAGAnF,OAAA,CAACpB,GAAG;YAACyL,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC,QAAQ;YAAAnC,QAAA,GACzCG,GAAG,CAAC9C,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAC,gBAC/C3E,OAAA,CAACJ,OAAO;cAAC0M,KAAK,EAAE9H,SAAS,CAAC,4BAA4B,CAAE;cAAC+H,SAAS,EAAC,QAAQ;cAAApE,QAAA,eACrEnI,OAAA;gBAAAmI,QAAA,eACEnI,OAAA,CAACrB,UAAU;kBACT0N,OAAO,EAAEA,CAAA,KAAM;oBACb/E,cAAc,CAAC,IAAI,CAAC;oBACpBE,kBAAkB,CAACnC,QAAQ,CAAC;kBAC9B,CAAE;kBACF6C,SAAS,EAAC,eAAe;kBACzBkE,IAAI,EAAC,OAAO;kBACrBI,QAAQ,EAAE,IAAK;kBAAArE,QAAA,eAENnI,OAAA,CAACf,OAAO;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,gBAEVnF,OAAA,CAACrB,UAAU;cACX0N,OAAO,EAAEA,CAAA,KAAM;gBACb/E,cAAc,CAAC,IAAI,CAAC;gBACpBE,kBAAkB,CAACnC,QAAQ,CAAC;cAC9B,CAAE;cACF6C,SAAS,EAAC,eAAe;cACzBkE,IAAI,EAAC,OAAO;cACZI,QAAQ,EAAElE,GAAG,CAAC9C,IAAI,CAAEC,IAAS,IAAKA,IAAI,CAACd,IAAI,KAAK,QAAQ,CAAE;cAAAwD,QAAA,eAE1DnI,OAAA,CAACf,OAAO;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACb,eAEInF,OAAA,CAACrB,UAAU;cACToL,EAAE,EAAE;gBACR;gBACAE,OAAO,EAAC,KAAK;gBACPuB,SAAS,EAAE,iCAAiC;gBAC5CiB,UAAU,EAAE,KAAK;gBACjBC,UAAU,EAAE,iBAAiB;gBAC7BC,MAAM,EAAE,gBAAgB;gBACxBX,MAAM,EAAC,QAAQ;gBACf3B,OAAO,EAAEjI,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEwK,UAAU,GAAG,MAAM,GAAG,MAAM,CAAE;cACtD,CAAE;cAAAzE,QAAA,eAEPnI,OAAA,CAACP,SAAS;gBAAEsK,EAAE,EAAE;kBAAC8C,IAAI,EAAC,GAAG;kBAAC9B,KAAK,EAAC;gBAAO;cAAE;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EACR3D,gBAAgB,IAAIC,WAAW,iBAChCzB,OAAA,CAACH,UAAU;YACT4B,WAAW,EAAEA,WAAY;YACzBC,cAAc,EAAEA,cAAe;YAC/BC,WAAW,EAAEA;UAAY;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CACD,EACCkC,WAAW,IAAIE,eAAe,KAAKlC,QAAQ,iBAC1CrF,OAAA,CAACpB,GAAG;YACFkO,YAAY,EAAEA,CAAA,KAAMxF,cAAc,CAAC,IAAI,CAAE;YACzCyF,YAAY,EAAEA,CAAA,KAAMzF,cAAc,CAAC,KAAK,CAAE;YAC1CY,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eAE9BnI,OAAA,CAACpB,GAAG;cAACsJ,SAAS,EAAC,uBAAuB;cAAAC,QAAA,eACpCnI,OAAA,CAACpB,GAAG;gBACFyL,OAAO,EAAC,MAAM;gBACd2C,aAAa,EAAC,KAAK;gBACnB1C,UAAU,EAAC,QAAQ;gBACnBP,EAAE,EAAE;kBAAEkD,MAAM,EAAE,SAAS;kBAACzC,GAAG,EAAG,MAAM;kBAACD,YAAY,EAAC,QAAQ;kBAACI,KAAK,EAAC;gBAAO,CAAE;gBAE1E0B,OAAO,EAAEA,CAAA,KAAMxE,oBAAoB,CAACxC,QAAQ,EAAE,QAAQ,CAAE;gBAAA8C,QAAA,gBAEhEnI,OAAA,CAACb,IAAI;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACNnF,OAAA,CAACnB,UAAU;kBAAC+K,OAAO,EAAC,SAAS;kBAAAzB,QAAA,EAAE3D,SAAS,CAAC,QAAQ;gBAAC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GAlGOE,QAAQ;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmGb,CACN,CAAC,EACD,CAAC,CAAAvD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEsL,SAAS,MAAK,MAAM,IAAI,CAAAtL,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEsL,SAAS,MAAK,cAAc,kBAEtFlN,OAAA,CAACpB,GAAG;UACJmL,EAAE,EAAE;YACF,IAAIV,gBAAgB,KAAK,QAAQ,IAAI;cACtCgB,OAAO,EAAE,MAAM;cACfE,YAAY,EAAE,QAAQ;cACtBD,UAAU,EAAC;YACV,CAAC;UACH,CAAE;UAAAnC,QAAA,EAECnE,KAAK,CAAC4B,MAAM,IAAI,CAAC,gBACA5F,OAAA,CAAAE,SAAA;YAAAiI,QAAA,EACKmB,cAAc,CAAC;UAAC,gBACnB,CAAC,GAEH;QACH;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGJsC,eAAe,iBAAIzH,OAAA,CAACR,WAAW;UAAC2N,YAAY,EAAEA,CAAA,KAAM,CAAC;QAAE;UAAAnI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EACL5C,cAAc,iBAAKvC,OAAA,CAACL,cAAc;MAAAqF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAmBrC,CAAC;AAEL,CAAC;AAACrD,EAAA,CAruBI3B,MAAM;EAAA,QA2FPT,cAAc,EACOI,cAAc,EA2NhBJ,cAAc;AAAA;AAAA0N,EAAA,GAvThCjN,MAAM;AAuuBZ,eAAeA,MAAM;AAAC,IAAAiN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}