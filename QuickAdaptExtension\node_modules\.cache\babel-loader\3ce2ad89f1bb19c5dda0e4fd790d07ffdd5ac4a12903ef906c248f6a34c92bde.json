{"ast": null, "code": "// ScrapingService.ts - Handles targeted click-based scraping functionality\n\nimport { NewAgentTraining } from '../services/AIService';\nimport i18n from '../multilinguial/i18n';\n\n// Get the translation function directly from i18next\nconst t = i18n.t.bind(i18n);\nlet _currentAgentData = undefined;\n\n// Global state to track if scraping is active\nlet _isScrapingActive = false;\nlet _scrapedData = [];\nlet _lastScrapedTimestamp = '';\nlet _elementMap = new Map(); // Map to track elements by XPath\nlet _clickListener = null;\nlet _highlightedElements = new Set(); // Track highlighted elements\nlet _overlayElements = new Set(); // Track overlay elements for click blocking\n// Interface for element data\n\n// Interface for scraped page data\n\n/**\r\n * Check if scraping is currently active\r\n */\nexport const isScrapingActive = () => {\n  return _isScrapingActive;\n};\n\n/**\r\n * Set the scraping active state\r\n */\nexport const setScrapingActive = active => {\n  _isScrapingActive = active;\n};\n\n/**\r\n * Generate XPath for an element\r\n */\n\nconst generateXPath = element => {\n  const tagName = element.tagName.toLowerCase();\n\n  // ✅ ejs-multiselect logic\n  if (tagName === 'ejs-multiselect') {\n    if (element.id) {\n      return `//*[@id=\"${element.id}\"]`;\n    }\n    const input = element.querySelector('input');\n    if (input && input.id) {\n      return `//*[@id=\"${input.id}\"]/ancestor::ejs-multiselect[1]`;\n    }\n    const dataId = element.getAttribute('data-id');\n    if (dataId) {\n      return `//ejs-multiselect[@data-id=\"${dataId}\"]`;\n    }\n    const className = element.className;\n    if (className) {\n      const uniqueClasses = className.split(' ').filter(cls => cls && !cls.startsWith('e-') && cls.length > 2);\n      if (uniqueClasses.length > 0) {\n        return `//ejs-multiselect[contains(@class, \"${uniqueClasses[0]}\")]`;\n      }\n    }\n    return generatePositionalXPath(element);\n  }\n\n  // ✅ ejs-dropdownlist logic (updated)\n  const dropdownContainer = tagName === 'ejs-dropdownlist' ? element : element.closest('ejs-dropdownlist');\n  if (dropdownContainer instanceof HTMLElement) {\n    const dropdownId = dropdownContainer.id;\n    const input = dropdownContainer.querySelector('input');\n    if (dropdownId && input) {\n      return `//ejs-dropdownlist[@id=\"${dropdownId}\"]/div/input`;\n    }\n    return generatePositionalXPath(dropdownContainer);\n  }\n  if (element.id) {\n    return `//*[@id=\"${element.id}\"]`;\n  }\n  return generatePositionalXPath(element);\n};\nconst generatePositionalXPath = element => {\n  const path = [];\n  let current = element;\n  while (current && current.nodeType === Node.ELEMENT_NODE) {\n    let selector = current.nodeName.toLowerCase();\n    if (current.id) {\n      selector += `[@id=\"${current.id}\"]`;\n      path.unshift(selector);\n      break;\n    } else {\n      let sibling = current.previousElementSibling;\n      let position = 1;\n      while (sibling) {\n        if (sibling.nodeName.toLowerCase() === selector) {\n          position++;\n        }\n        sibling = sibling.previousElementSibling;\n      }\n      if (position > 1) {\n        selector += `[${position}]`;\n      }\n      path.unshift(selector);\n    }\n    current = current.parentElement;\n  }\n  return '//' + path.join('/');\n};\n\n/**\r\n * Generate CSS selector for an element\r\n */\n\nconst generateCssSelector = el => {\n  if (!el) return '';\n  const path = [];\n  while (el && el.nodeType === Node.ELEMENT_NODE) {\n    let selector = el.tagName.toLowerCase();\n    if (el.id) {\n      // Check if ID starts with a number or contains special characters\n      const id = el.id;\n      if (/^[0-9]/.test(id) || /[^a-zA-Z0-9_-]/.test(id)) {\n        // Use attribute selector for IDs that start with numbers or have special chars\n        selector += `[id=\"${id}\"]`;\n      } else {\n        // Use standard ID selector for valid IDs\n        selector += `#${id}`;\n      }\n      path.unshift(selector);\n      break;\n    } else {\n      // Safely handle className - it might be a string or SVGAnimatedString\n      const className = getElementClassName(el);\n      if (className) {\n        selector += '.' + className.trim().replace(/\\s+/g, '.');\n      }\n      path.unshift(selector);\n      el = el.parentElement;\n    }\n  }\n  return path.join(' > ');\n};\n\n/**\r\n * Safely get className from an element (handles both HTML and SVG elements)\r\n */\nconst getElementClassName = el => {\n  if (!el) return '';\n\n  // For HTML elements, className is usually a string\n  if (typeof el.className === 'string') {\n    return el.className;\n  }\n\n  // For SVG elements, className might be an SVGAnimatedString\n  if (el.className && typeof el.className === 'object' && 'baseVal' in el.className) {\n    return el.className.baseVal || '';\n  }\n\n  // Fallback: try to get class attribute directly\n  return el.getAttribute('class') || '';\n};\n\n/**\r\n * Generate a labelName for an element that can be used for fallback identification\r\n */\nconst generateLabelName = element => {\n  // Try to find a meaningful label or name for the element\n\n  // 1. Check for explicit label element\n  if (element.id) {\n    var _label$textContent;\n    const label = document.querySelector(`label[for=\"${element.id}\"]`);\n    if (label && (_label$textContent = label.textContent) !== null && _label$textContent !== void 0 && _label$textContent.trim()) {\n      return label.textContent.trim();\n    }\n  }\n\n  // 2. Special handling for dropdown elements - prioritize text content over generic aria-labels\n  const tagName = element.tagName.toLowerCase();\n  const isDropdown = tagName === 'ejs-dropdownlist' || element.closest('ejs-dropdownlist') || element.getAttribute('role') === 'combobox' || element.className.includes('dropdown') || element.className.includes('select');\n  if (isDropdown) {\n    var _element$textContent;\n    // For dropdowns, prioritize visible text content over generic aria-labels\n    const textContent = (_element$textContent = element.textContent) === null || _element$textContent === void 0 ? void 0 : _element$textContent.trim();\n    if (textContent && textContent.length > 0) {\n      // Filter out generic dropdown text like \"dropdownlist\", \"combobox\", etc.\n      const genericTerms = ['dropdownlist', 'combobox', 'select', 'dropdown'];\n      const isGeneric = genericTerms.some(term => textContent.toLowerCase() === term.toLowerCase());\n      if (!isGeneric) {\n        return textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;\n      }\n    }\n  }\n\n  // 3. Check for aria-label (but not for dropdowns with generic values)\n  const ariaLabel = element.getAttribute('aria-label');\n  if (ariaLabel !== null && ariaLabel !== void 0 && ariaLabel.trim()) {\n    // Skip generic aria-labels for dropdowns\n    if (isDropdown) {\n      const genericTerms = ['dropdownlist', 'combobox', 'select', 'dropdown'];\n      const isGeneric = genericTerms.some(term => ariaLabel.toLowerCase() === term.toLowerCase());\n      if (!isGeneric) {\n        return ariaLabel.trim();\n      }\n    } else {\n      return ariaLabel.trim();\n    }\n  }\n\n  // 4. Check for title attribute\n  const title = element.getAttribute('title');\n  if (title !== null && title !== void 0 && title.trim()) {\n    return title.trim();\n  }\n\n  // 5. Check for placeholder\n  const placeholder = element.getAttribute('placeholder');\n  if (placeholder !== null && placeholder !== void 0 && placeholder.trim()) {\n    return placeholder.trim();\n  }\n\n  // 6. Check for name attribute\n  const name = element.getAttribute('name');\n  if (name !== null && name !== void 0 && name.trim()) {\n    return name.trim();\n  }\n\n  // 7. Check for data-label or similar data attributes\n  const dataLabel = element.getAttribute('data-label') || element.getAttribute('data-name');\n  if (dataLabel !== null && dataLabel !== void 0 && dataLabel.trim()) {\n    return dataLabel.trim();\n  }\n\n  // 8. Check for text content (for non-dropdown elements or as fallback)\n  if (!isDropdown) {\n    var _element$textContent2;\n    const textContent = (_element$textContent2 = element.textContent) === null || _element$textContent2 === void 0 ? void 0 : _element$textContent2.trim();\n    if (textContent && textContent.length > 0) {\n      return textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;\n    }\n  }\n\n  // 9. Check for value attribute (for inputs)\n  const value = element.getAttribute('value');\n  if (value !== null && value !== void 0 && value.trim()) {\n    return value.trim();\n  }\n\n  // 10. Fallback to element type and id/class\n  const id = element.id;\n  const className = getElementClassName(element);\n  if (id) {\n    return `${tagName}#${id}`;\n  } else if (className) {\n    const firstClass = className.split(' ')[0];\n    return `${tagName}.${firstClass}`;\n  }\n\n  // 11. Final fallback\n  return tagName;\n};\n\n/**\r\n * Check if element should be ignored for highlighting\r\n */\nconst shouldIgnoreHighlight = element => {\n  return element.classList.contains('AgentAdditionalContextPopup') || element.classList.contains(\"mdc-tooltip__surface\") || element.classList.contains(\"mdc-tooltip__surface-animation\") || element.classList.contains(\"mdc-tooltip\") || element.classList.contains(\"mdc-tooltip--shown\") || element.classList.contains(\"mdc-tooltip--showing\") || element.classList.contains(\"mdc-tooltip--hiding\") || element.getAttribute(\"role\") === \"tooltip\" || !!element.closest(\"#Tooltip-unique\") || !!element.closest(\"#my-react-drawer\") || !!element.closest(\"#tooltip-section-popover\") || !!element.closest(\"#btn-setting-toolbar\") || !!element.closest(\"#button-toolbar\") || !!element.closest(\"#color-picker\") || !!element.closest(\".qadpt-ext-banner\") || !!element.closest(\"#leftDrawer\") || !!element.closest(\"#image-popover\") || !!element.closest(\"#toggle-fit\") || !!element.closest(\"#color-popover\") || !!element.closest(\"#rte-popover\") || !!element.closest(\"#rte-alignment\") || !!element.closest(\"#rte-alignment-menu\") || !!element.closest(\"#rte-font\") || !!element.closest(\"#rte-bold\") || !!element.closest(\"#rte-italic\") || !!element.closest(\"#rte-underline\") || !!element.closest(\"#rte-strke-through\") || !!element.closest(\"#rte-alignment-menu-items\") || !!element.closest(\"#rte-more\") || !!element.closest(\"#rte-text-color\") || !!element.closest(\"#rte-text-color-popover\") || !!element.closest(\"#rte-text-highlight\") || !!element.closest(\"#rte-text-highlight-pop\") || !!element.closest(\"#rte-text-heading\") || !!element.closest(\"#rte-text-heading-menu-items\") || !!element.closest(\"#rte-text-format\") || !!element.closest(\"#rte-text-ul\") || !!element.closest(\"#rte-text-hyperlink\") || !!element.closest(\"#rte-video\") || !!element.closest(\"#rte-clear-formatting\") || !!element.closest(\"#rte-hyperlink-popover\") || !!element.closest(\"#rte-box\") || !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") || !!element.closest(\"#rte-placeholder\") || !!element.closest(\"#qadpt-designpopup\") || !!element.closest(\"#image-properties\") || !!element.closest(\"#rte-toolbar\") || !!element.closest(\"#tooltipdialog\") || !!element.closest(\"#rte-toolbar-paper\") || !!element.closest(\"#stop-scraping-button-container\") || !!element.closest(\"#rte-alignment-menu\") || !!element.closest(\"#rte-alignment-menu-items\") || !!element.closest(\"#quickadapt-scraping-instructions\") ||\n  // Ignore our own instruction banner\n  !!element.closest(\"#AgentAdditionalContextPopup\") ||\n  // Ignore our own instruction banner\n  !!element.closest(\"#textareaadditionalcontextpopup\");\n};\n\n/**\r\n * Check if element should be ignored for events\r\n */\nconst shouldIgnoreEvents = element => {\n  return element.classList.contains(\"mdc-tooltip__surface\") || element.classList.contains(\"mdc-tooltip__surface-animation\") || element.classList.contains(\"mdc-tooltip\") || element.classList.contains(\"mdc-tooltip--shown\") || element.classList.contains(\"mdc-tooltip--showing\") || element.classList.contains(\"mdc-tooltip--hiding\") || element.getAttribute(\"role\") === \"tooltip\" || !!element.closest(\"#Tooltip-unique\") || !!element.closest(\"#tooltip-section-popover\") || !!element.closest(\"#btn-setting-toolbar\") || !!element.closest(\"#button-toolbar\") || !!element.closest(\"#color-picker\") || !!element.closest(\".qadpt-ext-banner\") || !!element.closest(\"#leftDrawer\") || !!element.closest(\"#rte-popover\") || !!element.closest(\"#stop-scraping-button-container\") || !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") || !!element.closest(\"#rte-box\") || !!element.closest(\"#rte-placeholder\") || !!element.closest(\"#rte-alignment-menu-items\") || !!element.closest(\"#qadpt-designpopup\") || !!element.closest(\"#quickadapt-scraping-instructions\") ||\n  // Ignore our own instruction banner\n  !!element.closest(\"#AgentAdditionalContextPopup\") ||\n  // Ignore our own instruction banner\n  !!element.closest(\"#textareaadditionalcontextpopup\");\n};\n\n/**\r\n * Add persistent red border to element WITHOUT blocking clicks\r\n */\nconst addPersistentHighlightWithoutBlocking = element => {\n  if (shouldIgnoreHighlight(element)) return;\n\n  // Add persistent red border\n  element.style.outline = '3px solid #ff0000 !important';\n  element.style.outlineOffset = '2px';\n  element.setAttribute('data-quickadapt-highlighted', 'true');\n  _highlightedElements.add(element);\n\n  // No overlay creation - allow clicks to pass through\n};\n\n/**\r\n * Add persistent red border to element and create click-blocking overlay (legacy function)\r\n */\nconst addPersistentHighlight = element => {\n  var _element$offsetParent;\n  if (shouldIgnoreHighlight(element)) return;\n\n  // Add persistent red border\n  element.style.outline = '3px solid #ff0000 !important';\n  element.style.outlineOffset = '2px';\n  element.setAttribute('data-quickadapt-highlighted', 'true');\n  _highlightedElements.add(element);\n\n  // Create click-blocking overlay\n  const overlay = document.createElement('div');\n  overlay.style.cssText = `\n    position: absolute;\n    top: ${element.offsetTop}px;\n    left: ${element.offsetLeft}px;\n    width: ${element.offsetWidth}px;\n    height: ${element.offsetHeight}px;\n    background: transparent;\n    z-index: 999999;\n    pointer-events: auto;\n    cursor: not-allowed;\n  `;\n  overlay.setAttribute('data-quickadapt-overlay', 'true');\n  const tooltipText = t('Element already scraped click blocked');\n  overlay.title = tooltipText;\n\n  // Position overlay relative to the element's parent\n  const rect = element.getBoundingClientRect();\n  const parentRect = ((_element$offsetParent = element.offsetParent) === null || _element$offsetParent === void 0 ? void 0 : _element$offsetParent.getBoundingClientRect()) || {\n    top: 0,\n    left: 0\n  };\n  overlay.style.top = `${rect.top - parentRect.top + window.scrollY}px`;\n  overlay.style.left = `${rect.left - parentRect.left + window.scrollX}px`;\n\n  // Add overlay to the element's parent or body\n  const parent = element.offsetParent || document.body;\n  parent.appendChild(overlay);\n  _overlayElements.add(overlay);\n\n  // Block clicks on the overlay\n  overlay.addEventListener('click', e => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, true);\n};\n\n/**\r\n * Remove all highlights and overlays\r\n */\nconst removeAllHighlights = () => {\n  // Remove highlights\n  _highlightedElements.forEach(element => {\n    if (element && element.style) {\n      element.style.outline = '';\n      element.style.outlineOffset = '';\n      element.removeAttribute('data-quickadapt-highlighted');\n    }\n  });\n  _highlightedElements.clear();\n\n  // Remove overlays\n  _overlayElements.forEach(overlay => {\n    if (overlay && overlay.parentNode) {\n      overlay.parentNode.removeChild(overlay);\n    }\n  });\n  _overlayElements.clear();\n};\n\n/**\r\n * Show brief visual feedback when an element is clicked and scraped\r\n */\nconst showClickFeedback = element => {\n  try {\n    // Create a temporary feedback indicator\n    const feedback = document.createElement('div');\n    feedback.style.cssText = `\n      position: absolute;\n      background: #4CAF50;\n      color: white;\n      padding: 4px 8px;\n      border-radius: 4px;\n      font-size: 12px;\n      font-weight: bold;\n      z-index: 10001;\n      pointer-events: none;\n      box-shadow: 0 2px 8px rgba(0,0,0,0.3);\n      opacity: 0;\n      transition: opacity 0.2s ease;\n    `;\n    const scrapedText = `✓ ${t('Scraped')}`;\n    feedback.textContent = scrapedText;\n\n    // Position the feedback near the clicked element\n    const rect = element.getBoundingClientRect();\n    feedback.style.left = `${rect.left + window.scrollX}px`;\n    feedback.style.top = `${rect.top + window.scrollY - 30}px`;\n    document.body.appendChild(feedback);\n\n    // Animate in\n    setTimeout(() => {\n      feedback.style.opacity = '1';\n    }, 10);\n\n    // Remove after 2 seconds\n    setTimeout(() => {\n      feedback.style.opacity = '0';\n      setTimeout(() => {\n        if (feedback.parentNode) {\n          feedback.parentNode.removeChild(feedback);\n        }\n      }, 200);\n    }, 2000);\n  } catch (error) {}\n};\n\n/**\r\n * Extract data from a single element (optimized for click-based scraping)\r\n */\nconst extractElementData = element => {\n  var _element$textContent3;\n  const rect = element.getBoundingClientRect();\n  const xpath = generateXPath(element);\n  const type = getElementType(element);\n  const cssSelector = generateCssSelector(element);\n\n  // Helper to get value from input/select/textarea or custom dropdowns\n  function getFieldValue(el) {\n    // Standard HTML form elements\n    if (el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) {\n      return el.value;\n    }\n    if (el instanceof HTMLSelectElement) {\n      var _el$options$el$select;\n      return ((_el$options$el$select = el.options[el.selectedIndex]) === null || _el$options$el$select === void 0 ? void 0 : _el$options$el$select.text) || el.value;\n    }\n    // Syncfusion/other custom dropdowns (e.g., ejs-dropdownlist)\n    // Try to find an input or selected item inside\n    const input = el.querySelector('input');\n    if (input && input instanceof HTMLInputElement) {\n      return input.value;\n    }\n    // Try to find selected item span (common in custom dropdowns)\n    const selectedSpan = el.querySelector('.e-selected-item, .e-list-item.e-active, .e-ddl .e-input, .e-dropdownlist .e-input');\n    if (selectedSpan) {\n      var _selectedSpan$textCon;\n      return (_selectedSpan$textCon = selectedSpan.textContent) === null || _selectedSpan$textCon === void 0 ? void 0 : _selectedSpan$textCon.trim();\n    }\n    // Try data-value attribute\n    if (el.hasAttribute('data-value')) {\n      return el.getAttribute('data-value');\n    }\n    return undefined;\n  }\n\n  // Try to get value from the element itself\n  let value = getFieldValue(element);\n  // If not found, try to get from a child input/select/textarea\n  if (value === undefined) {\n    const childField = element.querySelector('input, select, textarea');\n    if (childField) {\n      value = getFieldValue(childField);\n    }\n  }\n  return {\n    tagName: element.tagName,\n    id: element.id || '',\n    className: getElementClassName(element),\n    text: ((_element$textContent3 = element.textContent) === null || _element$textContent3 === void 0 ? void 0 : _element$textContent3.trim()) || element.id,\n    //Added for agent training\n    labelName: generateLabelName(element),\n    // Add labelName for fallback identification\n    attributes: Array.from(element.attributes).reduce((acc, attr) => {\n      acc[attr.name] = attr.value;\n      return acc;\n    }, {}),\n    xpath,\n    cssSelector,\n    selector: xpath || cssSelector,\n    // Primary selector with fallback\n    rect: {\n      top: rect.top,\n      left: rect.left,\n      width: rect.width,\n      height: rect.height\n    },\n    children: [],\n    // We don't need children for click-based scraping\n    isVisible: rect.width > 0 && rect.height > 0,\n    timestamp: new Date().toISOString(),\n    url: window.location.href,\n    // Add URL to each element\n    type,\n    ...(value !== undefined ? {\n      value\n    } : {})\n  };\n};\nconst getElementType = element => {\n  const tagName = element.tagName.toLowerCase();\n\n  // Check for ejs-radiobutton first\n  if (tagName === 'ejs-radiobutton' || element.closest('ejs-radiobutton')) {\n    return 'radio';\n  }\n  if (tagName === 'input') {\n    const inputElement = element;\n    const type = inputElement.type || 'text';\n\n    // Special handling for date inputs\n    if (type === 'text' && (element.id.toLowerCase().includes('date') || element.className.toLowerCase().includes('date') || (element.getAttribute('placeholder') || '').toLowerCase().includes('date'))) {\n      // Check if it's a date range input or single date input\n      const value = inputElement.value || '';\n      const placeholder = element.getAttribute('placeholder') || '';\n      const id = element.id.toLowerCase();\n      const className = element.className.toLowerCase();\n      const name = (element.getAttribute('name') || '').toLowerCase();\n\n      // Check if the value contains date range patterns (e.g., \"01/01/2023 - 01/31/2023\")\n      const dateRangePattern = /\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}\\s*[-–—]\\s*\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}/;\n      const hasDateRangeValue = dateRangePattern.test(value);\n\n      // Check for explicit date range terms (more specific)\n      const explicitRangeTerms = ['daterange', 'date-range', 'date_range', 'period', 'duration', 'span', 'dates'];\n      const hasExplicitRangeClass = explicitRangeTerms.some(term => id.includes(term) || className.includes(term) || name.includes(term));\n\n      // Check for range indicators in a more specific way\n      const rangeIndicators = ['fromdate', 'todate', 'startdate', 'enddate', 'date_from', 'date_to', 'date-from', 'date-to', 'start_date', 'end_date', 'start-date', 'end-date'];\n      const hasSpecificRangeIndicator = rangeIndicators.some(indicator => id.includes(indicator) || className.includes(indicator) || name.includes(indicator));\n\n      // Check for range indicators in placeholder (more specific)\n      const placeholderRangeTerms = ['from', 'to', 'between', 'range', '-', '–', '—'];\n      const hasPlaceholderRangeIndicator = placeholderRangeTerms.some(term => placeholder.toLowerCase().includes(term));\n\n      // Return daterange only if we have strong indicators, otherwise dateinput\n      if (hasDateRangeValue || hasExplicitRangeClass || hasSpecificRangeIndicator || hasPlaceholderRangeIndicator) {\n        return 'daterange';\n      } else {\n        return 'dateinput';\n      }\n    }\n\n    // Inside a dropdown component\n    if (element.closest('ejs-dropdownlist, ejs-combobox, ejs-multiselect, [role=\"combobox\"]')) {\n      return 'dropdown';\n    }\n    return type;\n  }\n\n  // ejs-dropdownlist\n  if (tagName === 'ejs-dropdownlist' || element.closest('ejs-dropdownlist')) {\n    return 'dropdown';\n  }\n\n  // select\n  if (tagName === 'select') {\n    const selectElement = element;\n    return selectElement.multiple ? 'multiselect' : 'dropdown';\n  }\n\n  // textarea\n  if (tagName === 'textarea') {\n    return 'textarea';\n  }\n\n  // contenteditable\n  if (element.contentEditable === 'true') {\n    return 'contenteditable';\n  }\n\n  // dropdown-like custom components\n  if (element.getAttribute('role') === 'combobox' || element.getAttribute('role') === 'listbox' || element.className.includes('dropdown') || element.className.includes('select')) {\n    return 'dropdown';\n  }\n\n  // ✅ Fallback for clickable, non-input UI elements\n  return 'click';\n};\n\n/**\r\n * Handle click events for element scraping\r\n */\n\nconst handleElementClick = async (event, agentData) => {\n  try {\n    // IMPORTANT: Don't prevent default or stop propagation\n    // This allows the original click functionality to work normally\n    // (navigation, form submission, button clicks, etc.)\n\n    const target = event.target;\n    if (!target || !target.nodeType || target.nodeType !== Node.ELEMENT_NODE) {\n      return;\n    }\n    if (shouldIgnoreEvents(target)) {\n      return;\n    }\n    if (target.hasAttribute('data-quickadapt-highlighted')) {\n      return;\n    }\n\n    // Extract data from clicked element ONLY\n    const clickedElementData = extractElementData(target);\n\n    // Store only the clicked element data (no parent element)\n    const elementsToStore = [clickedElementData];\n\n    // Add to scraped data\n    console.log(`📝 Attempting to scrape element: ${target.tagName} with XPath: ${clickedElementData.xpath}`);\n    setScrapedData({\n      elements: elementsToStore\n    }, true);\n\n    // Save to local storage immediately after each element is scraped\n\n    await saveScrapedDataToStorage(agentData);\n    console.log(`💾 Element data saved to local storage immediately`);\n\n    // Add persistent red border WITHOUT blocking clicks (only to clicked element)\n    addPersistentHighlightWithoutBlocking(target);\n\n    // // Show brief success feedback\n    // showClickFeedback(target);\n  } catch (error) {\n    console.error('Error in handleElementClick:', error);\n  }\n};\nexport const setScrapedData = (data, append = false) => {\n  const timestamp = new Date().toISOString();\n  _lastScrapedTimestamp = timestamp;\n  if (!append) {\n    // Clear existing data if not appending\n    _scrapedData = [];\n    _elementMap.clear();\n  }\n\n  // Process each element in the data\n  if (data && data.elements && Array.isArray(data.elements)) {\n    data.elements.forEach(element => {\n      // Add timestamp to the element\n      element.timestamp = timestamp;\n\n      // Use XPath as a unique identifier for the element\n      if (element.xpath) {\n        // If element already exists in the map, don't add it again (prevent duplicates)\n        if (_elementMap.has(element.xpath)) {\n          console.log(`⚠️ Skipping duplicate element with XPath: ${element.xpath}`);\n          return; // Skip this element\n        } else {\n          // New element, add to map and data array\n          console.log(`✅ Adding new element with XPath: ${element.xpath}`);\n          _elementMap.set(element.xpath, element);\n          _scrapedData.push(element);\n        }\n      } else {\n        // No XPath, check for duplicates by other means (tagName + id + className)\n        const isDuplicate = _scrapedData.some(existing => existing.tagName === element.tagName && existing.id === element.id && existing.className === element.className);\n        if (!isDuplicate) {\n          _scrapedData.push(element);\n        } else {}\n      }\n    });\n  }\n};\n\n/**\r\n * Get the currently scraped data\r\n */\nexport const getScrapedData = () => {\n  return _scrapedData;\n};\n\n/**\r\n * Get element by XPath\r\n */\nexport const getElementByXPath = xpath => {\n  return _elementMap.get(xpath);\n};\n\n/**\r\n * Find DOM element using fallback mechanisms\r\n * Priority: xpath -> labelName -> cssSelector\r\n */\n// export const findElementWithFallback = (elementData: {\n//   xpath?: string;\n//   labelName?: string;\n//   cssSelector?: string;\n//   id?: string;\n// }): HTMLElement | null => {\n//   // Primary: Try xpath first\n//   if (elementData.xpath) {\n//     try {\n//       const result = document.evaluate(\n//         elementData.xpath,\n//         document,\n//         null,\n//         XPathResult.FIRST_ORDERED_NODE_TYPE,\n//         null\n//       );\n//       const element = result.singleNodeValue as HTMLElement;\n//       if (element) {\n//         console.log(`✅ Found element using xpath: ${elementData.xpath}`);\n//         return element;\n//       }\n//     } catch (error) {\n//       console.warn(`❌ XPath failed: ${elementData.xpath}`, error);\n//     }\n//   }\n\n//   // Fallback 1: Try to find by labelName\n//   if (elementData.labelName) {\n//     console.log(`🔄 Falling back to labelName: ${elementData.labelName}`);\n\n//     // Try different approaches to find by labelName\n//     const labelSelectors = [\n//       `[aria-label=\"${elementData.labelName}\"]`,\n//       `[title=\"${elementData.labelName}\"]`,\n//       `[placeholder=\"${elementData.labelName}\"]`,\n//       `[name=\"${elementData.labelName}\"]`,\n//       `[data-label=\"${elementData.labelName}\"]`,\n//       `[data-name=\"${elementData.labelName}\"]`\n//     ];\n\n//     for (const selector of labelSelectors) {\n//       try {\n//         const element = document.querySelector(selector) as HTMLElement;\n//         if (element) {\n//           console.log(`✅ Found element using labelName selector: ${selector}`);\n//           return element;\n//         }\n//       } catch (error) {\n//         console.warn(`❌ LabelName selector failed: ${selector}`, error);\n//       }\n//     }\n\n//     // Try to find by text content containing the labelName\n//     try {\n//       const xpath = `//*[contains(text(), \"${elementData.labelName}\")]`;\n//       const result = document.evaluate(\n//         xpath,\n//         document,\n//         null,\n//         XPathResult.FIRST_ORDERED_NODE_TYPE,\n//         null\n//       );\n//       const element = result.singleNodeValue as HTMLElement;\n//       if (element) {\n//         console.log(`✅ Found element using text content: ${elementData.labelName}`);\n//         return element;\n//       }\n//     } catch (error) {\n//       console.warn(`❌ Text content search failed for: ${elementData.labelName}`, error);\n//     }\n\n//     // Try to find label element and get its associated input\n//     try {\n//       const labelElement = Array.from(document.querySelectorAll('label')).find(\n//         label => label.textContent?.trim() === elementData.labelName\n//       );\n//       if (labelElement) {\n//         const forAttribute = labelElement.getAttribute('for');\n//         if (forAttribute) {\n//           const associatedElement = document.getElementById(forAttribute) as HTMLElement;\n//           if (associatedElement) {\n//             console.log(`✅ Found element using label association: ${elementData.labelName}`);\n//             return associatedElement;\n//           }\n//         }\n//       }\n//     } catch (error) {\n//       console.warn(`❌ Label association search failed for: ${elementData.labelName}`, error);\n//     }\n//   }\n\n//   // Fallback 2: Try cssSelector\n//   if (elementData.cssSelector) {\n//     console.log(`🔄 Falling back to cssSelector: ${elementData.cssSelector}`);\n//     try {\n//       const element = document.querySelector(elementData.cssSelector) as HTMLElement;\n//       if (element) {\n//         console.log(`✅ Found element using cssSelector: ${elementData.cssSelector}`);\n//         return element;\n//       }\n//     } catch (error) {\n//       console.warn(`❌ CSS selector failed: ${elementData.cssSelector}`, error);\n\n//       // Try to fix common CSS selector issues\n//       if (elementData.cssSelector.includes('#')) {\n//         try {\n//           // Extract ID and try attribute selector\n//           const idMatch = elementData.cssSelector.match(/#([^.\\s>+~]+)/);\n//           if (idMatch) {\n//             const id = idMatch[1];\n//             const tagMatch = elementData.cssSelector.match(/^([a-zA-Z]+)/);\n//             const tag = tagMatch ? tagMatch[1] : '';\n//             const attributeSelector = tag ? `${tag}[id=\"${id}\"]` : `[id=\"${id}\"]`;\n\n//             console.log(`🔄 Trying attribute selector fallback: ${attributeSelector}`);\n//             const element = document.querySelector(attributeSelector) as HTMLElement;\n//             if (element) {\n//               console.log(`✅ Found element using attribute selector: ${attributeSelector}`);\n//               return element;\n//             }\n//           }\n//         } catch (attributeError) {\n//           console.warn(`❌ Attribute selector fallback also failed`, attributeError);\n//         }\n//       }\n//     }\n//   }\n\n//   // Final fallback: Try by ID if available\n//   if (elementData.id) {\n//     console.log(`🔄 Final fallback to ID: ${elementData.id}`);\n//     try {\n//       const element = document.getElementById(elementData.id) as HTMLElement;\n//       if (element) {\n//         console.log(`✅ Found element using ID: ${elementData.id}`);\n//         return element;\n//       }\n//     } catch (error) {\n//       console.warn(`❌ ID selector failed: ${elementData.id}`, error);\n//     }\n//   }\n\n//   console.error(`❌ All fallback methods failed for element:`, elementData);\n//   return null;\n// };\n\n/**\r\n * Test function to demonstrate element finding with fallback mechanisms\r\n * This can be called from browser console for testing\r\n */\n// export const testElementFinding = (elementData: {\n//   xpath?: string;\n//   labelName?: string;\n//   cssSelector?: string;\n//   id?: string;\n// }): void => {\n//   console.log('🧪 Testing element finding with fallback mechanisms...');\n//   console.log('Input data:', elementData);\n\n//   const foundElement = findElementWithFallback(elementData);\n\n//   if (foundElement) {\n//     console.log('✅ Successfully found element:', foundElement);\n//     console.log('Element details:', {\n//       tagName: foundElement.tagName,\n//       id: foundElement.id,\n//       className: foundElement.className,\n//       textContent: foundElement.textContent?.substring(0, 100)\n//     });\n//   } else {\n//     console.log('❌ Could not find element with any fallback method');\n//   }\n// };\n\n/**\r\n * Get all xpath data from scraped elements\r\n */\nexport const getXPathData = () => {\n  return _scrapedData.map(element => ({\n    xpath: element.xpath,\n    tagName: element.tagName,\n    id: element.id,\n    className: element.className,\n    text: element.text,\n    timestamp: element.timestamp,\n    url: element.url\n  }));\n};\n\n/**\r\n * Manually send current scraped data to backend API (can be called independently)\r\n */\nexport const exportScrapedDataToFile = async accountId => {\n  try {\n    if (_scrapedData.length === 0) {\n      // Try to load from storage if no current data\n      const storedData = await loadScrapedDataFromStorage();\n      if (!storedData || !storedData.scrapedData || storedData.scrapedData.length === 0) {\n        // alert('No scraped data available to send to API. Please scrape some elements first.');\n        return;\n      }\n      // Use stored data for API call\n      await saveScrapedDataToFile(accountId);\n    } else {\n      // Save current data to storage first, then send to API\n      await saveScrapedDataToStorage();\n      await saveScrapedDataToFile(accountId);\n    }\n  } catch (error) {\n    // alert('Error sending scraped data to backend API. Check console for details.');\n  }\n};\n\n/**\r\n * Get scraped data count\r\n */\nexport const getScrapedDataCount = () => {\n  return _scrapedData.length;\n};\n\n/**\r\n * Check if there's existing scraped data in storage\r\n */\nexport const hasScrapedDataInStorage = async () => {\n  try {\n    const storedData = await loadScrapedDataFromStorage();\n    return storedData && storedData.scrapedData && Array.isArray(storedData.scrapedData) && storedData.scrapedData.length > 0;\n  } catch (error) {\n    return false;\n  }\n};\n\n/**\r\n * Clear all scraped data (both in-memory and storage) - use when starting completely fresh\r\n */\nexport const clearAllScrapedData = async () => {\n  console.log('🧹 Clearing all scraped data (memory + storage)');\n  clearScrapedData(); // Clear in-memory data\n  await clearScrapedDataFromStorage(); // Clear storage data\n  removeAllHighlights(); // Clear visual highlights\n};\n\n/**\r\n * Get current scraping status for debugging\r\n */\nexport const getScrapingStatus = () => {\n  return {\n    isActive: _isScrapingActive,\n    elementCount: _scrapedData.length,\n    elementMapSize: _elementMap.size,\n    lastTimestamp: _lastScrapedTimestamp,\n    highlightedElementsCount: _highlightedElements.size\n  };\n};\n\n/**\r\n * Get the timestamp of the last scrape\r\n */\nexport const getLastScrapedTimestamp = () => {\n  return _lastScrapedTimestamp;\n};\n\n/**\r\n * Clear scraped data\r\n */\nexport const clearScrapedData = () => {\n  console.log(`🧹 Clearing scraped data - had ${_scrapedData.length} elements and ${_elementMap.size} in element map`);\n  _scrapedData = [];\n  _lastScrapedTimestamp = '';\n  _elementMap.clear(); // Clear the element map to allow re-scraping of same elements\n};\n\n/**\r\n * Clear scraped data from Chrome storage (for debugging/reset purposes)\r\n */\nexport const clearScrapedDataFromStorage = async () => {\n  try {\n    localStorage.removeItem('quickadapt-scraped-data');\n  } catch (error) {\n    console.error('Error clearing scraped data from storage:', error);\n  }\n};\n\n/**\r\n * Save scraped data to Chrome storage\r\n */\n\nexport const saveScrapedDataToStorage = async agentData => {\n  try {\n    const storageData = {\n      scrapedData: _scrapedData,\n      timestamp: _lastScrapedTimestamp,\n      url: window.location.href,\n      title: document.title,\n      elementCount: _scrapedData.length,\n      xpathData: _scrapedData.map(element => ({\n        xpath: element.xpath,\n        tagName: element.tagName,\n        id: element.id,\n        className: element.className,\n        text: element.text,\n        labelName: element.labelName,\n        cssSelector: element.cssSelector,\n        selector: element.selector,\n        attributes: element.attributes,\n        timestamp: element.timestamp,\n        url: element.url\n      })),\n      // Add agent data if provided\n      ...(agentData && {\n        agentData: {\n          AccountId: agentData.accountId,\n          Description: agentData.agentDescription,\n          Name: agentData.agentName,\n          url: agentData.agentUrl\n        }\n      })\n    };\n    localStorage.setItem('quickadapt-scraped-data', JSON.stringify(storageData));\n    storageData.xpathData.forEach((item, index) => {\n      console.log(`${index + 1}. ${item.tagName} - ${item.xpath}`);\n    });\n  } catch (error) {}\n};\n\n/**\r\n * Load scraped data from Chrome storage\r\n */\nexport const loadScrapedDataFromStorage = async () => {\n  try {\n    const data = localStorage.getItem('quickadapt-scraped-data');\n    return data ? JSON.parse(data) : null;\n  } catch (error) {\n    return null;\n  }\n};\n\n/**\r\n * Send scraped data from Chrome storage to backend API\r\n */\nexport const saveScrapedDataToFile = async accountId => {\n  try {\n    const storedData = await loadScrapedDataFromStorage();\n    if (!storedData) {\n      return;\n    }\n    const apiData = {\n      metadata: {\n        url: storedData.url || window.location.href,\n        title: storedData.title || document.title,\n        timestamp: storedData.timestamp || new Date().toISOString(),\n        elementCount: storedData.elementCount || 0,\n        exportedAt: new Date().toISOString()\n      },\n      elements: storedData.scrapedData || [],\n      xpathData: storedData.xpathData || []\n    };\n\n    // Send data to backend API\n    await uploadXPathsFile(apiData, accountId);\n  } catch (error) {}\n};\n\n/**\r\n * Upload XPath data to backend API using existing FileService\r\n */\nexport const uploadXPathsFile = async (data, accountId) => {\n  try {\n    // Convert JSON data to FormData as expected by the existing API\n    const formData = new FormData();\n\n    // Create a JSON file blob\n    const jsonBlob = new Blob([JSON.stringify(data, null, 2)], {\n      type: 'application/json'\n    });\n\n    // Generate filename with timestamp\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n    const pageTitle = (data.metadata.title || 'scraped-data').replace(/[^a-zA-Z0-9]/g, '-');\n    const filename = `quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;\n\n    // Add the file to FormData\n    formData.append('aifiles', jsonBlob, filename); // ✅ Correct key name\n\n    // Add metadata as form fields if needed\n    formData.append('elementCount', data.metadata.elementCount.toString());\n    formData.append('url', data.metadata.url);\n    formData.append('timestamp', data.metadata.timestamp);\n\n    // Import and use the existing uploadXpathsFile function\n    const {\n      uploadXpathsFile\n    } = await import('./FileService');\n    if (!accountId) {\n      throw new Error('Account ID is required to upload XPath data');\n    }\n    const response = await uploadXpathsFile(accountId, formData);\n  } catch (error) {\n    // Show error message to user\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n    throw error; // Re-throw to be caught by calling function\n  }\n};\n\n/**\r\n * Start click-based scraping process\r\n */\n\nexport const startAgentScraping = async (agentName, agentDescription, accountId, agentUrl) => {\n  if (_isScrapingActive) return;\n  _isScrapingActive = true;\n  console.log('🎯 Starting scraping session - checking storage consistency');\n\n  // Check if Chrome storage has scraped data\n  const storedData = await loadScrapedDataFromStorage();\n  if (!storedData || !storedData.scrapedData || !Array.isArray(storedData.scrapedData) || storedData.scrapedData.length === 0) {\n    console.log('📊 No valid data in Chrome storage - clearing in-memory data');\n    clearScrapedData(); // Clear in-memory data if storage is empty\n    await saveScrapedDataToStorage({\n      accountId,\n      agentDescription,\n      agentName,\n      agentUrl\n    });\n  } else {\n    console.log(`📊 Storage validation passed - ${storedData.scrapedData.length} elements in storage, ${_scrapedData.length} in memory`);\n  }\n  console.log(`📊 Current scraped elements count: ${_scrapedData.length}`);\n  _currentAgentData = {\n    accountId,\n    agentDescription,\n    agentName,\n    agentUrl\n  };\n  // Re-highlight existing scraped elements instead of clearing all highlights\n  _scrapedData.forEach(element => {\n    if (element.xpath) {\n      try {\n        const elementNode = document.evaluate(element.xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;\n        if (elementNode) {\n          addPersistentHighlightWithoutBlocking(elementNode);\n        }\n      } catch (error) {\n        // Element might not exist anymore, that's okay\n      }\n    }\n  });\n\n  // Add click event listener to capture element clicks\n  if (!_clickListener) {\n    _clickListener = event => {\n      // Call the async function without awaiting to avoid blocking the event handler\n\n      handleElementClick(event, _currentAgentData).catch(error => {\n        console.error('Error in click handler:', error);\n      });\n    };\n    document.addEventListener('click', _clickListener, true); // Use capture phase\n  }\n\n  // Send message to content script to enable click-based scraping\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\n    try {\n      chrome.runtime.sendMessage({\n        action: 'startClickScraping'\n      });\n    } catch (error) {\n      // Fallback: try to communicate through window events\n      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));\n    }\n  }\n\n  // Show user instruction with translation support\n  showScrapingInstructions();\n};\nexport const startScraping = () => {\n  if (_isScrapingActive) return;\n  _isScrapingActive = true;\n  clearScrapedData();\n\n  // Add click event listener to capture element clicks\n  if (!_clickListener) {\n    _clickListener = handleElementClick;\n    document.addEventListener('click', _clickListener, true); // Use capture phase\n  }\n\n  // Send message to content script to enable click-based scraping\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\n    try {\n      chrome.runtime.sendMessage({\n        action: 'startClickScraping'\n      });\n    } catch (error) {\n      // Fallback: try to communicate through window events\n      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));\n    }\n  }\n\n  // Show user instruction\n  showScrapingInstructions();\n};\n\n/**\r\n * Stop click-based scraping process\r\n */\nexport const stopScraping = async (isAgentTraining, accountId, agentName, agentDescription, agentUrl) => {\n  if (!_isScrapingActive) return;\n  _isScrapingActive = false;\n\n  // Remove click event listener\n  if (_clickListener) {\n    document.removeEventListener('click', _clickListener, true);\n    _clickListener = null;\n  }\n\n  // Process scraped data before clearing from storage\n  if (_scrapedData.length > 0) {\n    // Save to storage one final time to ensure we have the latest data, including agent data\n    await saveScrapedDataToStorage({\n      accountId,\n      agentDescription,\n      agentName,\n      agentUrl\n    });\n\n    // Get data from Chrome storage and save to file\n    !isAgentTraining && (await saveScrapedDataToFile(accountId));\n    const filteredData = await getFilteredScrapedData();\n    console.log(filteredData, \"filteredData\");\n    const agent = {\n      AccountId: accountId,\n      Description: agentDescription,\n      Name: agentName,\n      TrainingFields: filteredData,\n      url: window.location.href\n    };\n    if (isAgentTraining && agentName && agentDescription) {\n      await NewAgentTraining(agent);\n    }\n  }\n  await clearScrapedDataFromStorage();\n\n  // Remove all highlights and overlays\n  removeAllHighlights();\n\n  // Send message to background script to stop scraping\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\n    try {\n      chrome.runtime.sendMessage({\n        action: 'stopClickScraping'\n      });\n    } catch (error) {\n      // Fallback: try to communicate through window events\n      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));\n    }\n  }\n\n  // Hide instructions\n  hideScrapingInstructions();\n};\n\n/**\r\n * Cancel training process - stops scraping and clears data without processing\r\n */\nexport const cancelTraining = async () => {\n  if (!_isScrapingActive) return;\n  _isScrapingActive = false;\n\n  // Remove click event listener\n  if (_clickListener) {\n    document.removeEventListener('click', _clickListener, true);\n    _clickListener = null;\n  }\n\n  // Clear scraped data from storage immediately without processing\n  await clearScrapedDataFromStorage();\n  console.log('🧹 Cleared scraped data from storage after cancel training');\n\n  // Clear in-memory data\n  clearScrapedData();\n\n  // Remove all highlights and overlays\n  removeAllHighlights();\n\n  // Send message to background script to stop scraping\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\n    try {\n      chrome.runtime.sendMessage({\n        action: 'stopClickScraping'\n      });\n    } catch (error) {\n      // Fallback: try to communicate through window events\n      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));\n    }\n  }\n\n  // Hide instructions\n  hideScrapingInstructions();\n};\nexport const getFilteredScrapedData = async () => {\n  const storedData = await loadScrapedDataFromStorage();\n  if (!storedData || !Array.isArray(storedData.scrapedData)) return [];\n  return storedData.scrapedData.map(item => {\n    // Implement fallback logic for selector identification\n    let primarySelector = item.xpath || '';\n    let fallbackSelector = item.cssSelector || '';\n\n    // If no xpath, use labelName as fallback identifier\n    if (!primarySelector && item.labelName) {\n      primarySelector = `[aria-label=\"${item.labelName}\"]`; // Try aria-label first\n      // Additional fallback selectors based on labelName\n      if (!primarySelector) {\n        primarySelector = `[title=\"${item.labelName}\"]`; // Try title attribute\n      }\n      if (!primarySelector) {\n        primarySelector = `[placeholder=\"${item.labelName}\"]`; // Try placeholder\n      }\n      if (!primarySelector) {\n        primarySelector = `[name=\"${item.labelName}\"]`; // Try name attribute\n      }\n    }\n\n    // If still no selector, use cssSelector as final fallback\n    if (!primarySelector) {\n      primarySelector = fallbackSelector;\n    }\n    return {\n      Name: item.text || '',\n      xpath: item.xpath || '',\n      labelName: item.labelName || '',\n      selector: primarySelector,\n      cssSelector: item.cssSelector || '',\n      value: item.value || '',\n      type: item.type || ''\n    };\n  });\n};\n\n/**\r\n * Show scraping instructions to user\r\n */\nconst showScrapingInstructions = () => {\n  // Remove existing instruction if any\n  hideScrapingInstructions();\n  const instructionDiv = document.createElement('div');\n  instructionDiv.id = 'quickadapt-scraping-instructions';\n  instructionDiv.style.cssText = `\n    position: fixed;\n    top: 20px;\n    right: 20px;\n    background: #4CAF50;\n    color: white;\n    padding: 15px 20px;\n    border-radius: 8px;\n    font-family: Arial, sans-serif;\n    font-size: 14px;\n    font-weight: bold;\n    z-index: 10000;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n    max-width: 320px;\n    text-align: center;\n  `;\n\n  // Use translations with i18n instance directly\n  const mainTitle = `🎯 ${t('Click Scraping Active')}`;\n  const clickElement = `• ${t('Click any element to scrape its XPath data')}`;\n  const onlyClicked = `• ${t('Only the clicked element is scraped no duplicates')}`;\n  const originalClick = `• ${t('Original click functionality still works')}`;\n  const redBorders = `• ${t('Red borders show scraped elements')}`;\n  const dataSaved = `• ${t('Data is saved to Chrome storage')}`;\n  instructionDiv.innerHTML = `\n  \n    ${mainTitle}<br>\n    <small style=\"font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;\">\n      ${clickElement}<br>\n      ${onlyClicked}<br>\n      ${originalClick}<br>\n      ${redBorders}<br>\n      ${dataSaved}\n    </small>\n  `;\n  document.body.appendChild(instructionDiv);\n\n  // Auto-hide after 8 seconds\n  setTimeout(() => {\n    if (instructionDiv.parentNode) {\n      instructionDiv.style.opacity = '0.7';\n    }\n  }, 8000);\n};\n\n/**\r\n * Hide scraping instructions\r\n */\nconst hideScrapingInstructions = () => {\n  const existingInstruction = document.getElementById('quickadapt-scraping-instructions');\n  if (existingInstruction) {\n    existingInstruction.remove();\n  }\n};\n\n/**\r\n * Initialize click-based scraping service\r\n * This should be called when the extension is loaded\r\n */\nexport const initScrapingService = async () => {\n  _isScrapingActive = false;\n  _scrapedData = [];\n  _elementMap.clear();\n  _lastScrapedTimestamp = '';\n  _clickListener = null;\n\n  // Try to restore scraped data from storage (in case of page refresh)\n  try {\n    const storedData = await loadScrapedDataFromStorage();\n    if (storedData && storedData.scrapedData && Array.isArray(storedData.scrapedData)) {\n      _scrapedData = storedData.scrapedData;\n      _lastScrapedTimestamp = storedData.timestamp || '';\n\n      // Rebuild the element map for duplicate detection\n      _scrapedData.forEach(element => {\n        if (element.xpath) {\n          _elementMap.set(element.xpath, element);\n        }\n      });\n      console.log(`🔄 Restored ${_scrapedData.length} scraped elements from storage after page refresh`);\n\n      // Re-highlight the previously scraped elements\n      _scrapedData.forEach(element => {\n        if (element.xpath) {\n          try {\n            const elementNode = document.evaluate(element.xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;\n            if (elementNode) {\n              addPersistentHighlightWithoutBlocking(elementNode);\n            }\n          } catch (error) {\n            // Element might not exist anymore, that's okay\n          }\n        }\n      });\n    }\n  } catch (error) {\n    console.log('No previous scraped data found or error loading from storage');\n  }\n\n  // Check if we're in a Chrome extension environment\n  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {\n    // Listen for messages from background script\n    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {\n      if (message.action === 'updateScrapingState') {\n        _isScrapingActive = message.isActive;\n        sendResponse({\n          success: true\n        });\n        return true;\n      }\n      if (message.action === 'getScrapingState') {\n        sendResponse({\n          isActive: _isScrapingActive,\n          lastTimestamp: _lastScrapedTimestamp,\n          elementCount: _scrapedData.length\n        });\n        return true;\n      }\n      if (message.action === 'getScrapedData') {\n        sendResponse({\n          data: _scrapedData,\n          timestamp: _lastScrapedTimestamp\n        });\n        return true;\n      }\n      if (message.action === 'clearScrapedData') {\n        clearScrapedData();\n        sendResponse({\n          success: true\n        });\n        return true;\n      }\n    });\n  } else {}\n};\n\n// Initialize the service\ninitScrapingService().catch(error => {\n  console.error('Error initializing scraping service:', error);\n});\n\n/**\r\n * Utility to extract only essential data from a scraped element object\r\n * Includes fallback identification mechanisms\r\n */\nexport function extractMinimalScrapeData(element) {\n  return {\n    xpath: element.xpath || '',\n    labelName: element.labelName || element.text || '',\n    cssSelector: element.cssSelector || '',\n    selector: element.selector || element.xpath || element.cssSelector || '',\n    value: element.value,\n    text: element.text || '',\n    id: element.id || ''\n  };\n}", "map": {"version": 3, "names": ["NewAgentTraining", "i18n", "t", "bind", "_currentAgentData", "undefined", "_isScrapingActive", "_scrapedData", "_lastScrapedTimestamp", "_elementMap", "Map", "_clickListener", "_highlightedElements", "Set", "_overlayElements", "isScrapingActive", "setScrapingActive", "active", "generateXPath", "element", "tagName", "toLowerCase", "id", "input", "querySelector", "dataId", "getAttribute", "className", "uniqueClasses", "split", "filter", "cls", "startsWith", "length", "generatePositionalXPath", "dropdownContainer", "closest", "HTMLElement", "dropdownId", "path", "current", "nodeType", "Node", "ELEMENT_NODE", "selector", "nodeName", "unshift", "sibling", "previousElementSibling", "position", "parentElement", "join", "generateCssSelector", "el", "test", "getElementClassName", "trim", "replace", "baseVal", "generateLabelName", "_label$textContent", "label", "document", "textContent", "isDropdown", "includes", "_element$textContent", "genericTerms", "isGeneric", "some", "term", "substring", "aria<PERSON><PERSON><PERSON>", "title", "placeholder", "name", "dataLabel", "_element$textContent2", "value", "firstClass", "shouldIgnoreHighlight", "classList", "contains", "shouldIgnoreEvents", "addPersistentHighlightWithoutBlocking", "style", "outline", "outlineOffset", "setAttribute", "add", "addPersistentHighlight", "_element$offsetParent", "overlay", "createElement", "cssText", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight", "tooltipText", "rect", "getBoundingClientRect", "parentRect", "offsetParent", "top", "left", "window", "scrollY", "scrollX", "parent", "body", "append<PERSON><PERSON><PERSON>", "addEventListener", "e", "preventDefault", "stopPropagation", "removeAllHighlights", "for<PERSON>ach", "removeAttribute", "clear", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showClickFeedback", "feedback", "scrapedText", "setTimeout", "opacity", "error", "extractElementData", "_element$textContent3", "xpath", "type", "getElementType", "cssSelector", "getFieldValue", "HTMLInputElement", "HTMLTextAreaElement", "HTMLSelectElement", "_el$options$el$select", "options", "selectedIndex", "text", "selectedSpan", "_selectedSpan$textCon", "hasAttribute", "childField", "labelName", "attributes", "Array", "from", "reduce", "acc", "attr", "width", "height", "children", "isVisible", "timestamp", "Date", "toISOString", "url", "location", "href", "inputElement", "dateRangePattern", "hasDateRangeValue", "explicitRangeTerms", "hasExplicitRangeClass", "rangeIndicators", "hasSpecificRangeIndicator", "indicator", "placeholder<PERSON><PERSON><PERSON>Terms", "hasPlaceholderRangeIndicator", "selectElement", "multiple", "contentEditable", "handleElementClick", "event", "agentData", "target", "clickedElementData", "elementsToStore", "console", "log", "setScrapedData", "elements", "saveScrapedDataToStorage", "data", "append", "isArray", "has", "set", "push", "isDuplicate", "existing", "getScrapedData", "getElementByXPath", "get", "getXPathData", "map", "exportScrapedDataToFile", "accountId", "storedData", "loadScrapedDataFromStorage", "scrapedData", "saveScrapedDataToFile", "getScrapedDataCount", "hasScrapedDataInStorage", "clearAllScrapedData", "clearScrapedData", "clearScrapedDataFromStorage", "getScrapingStatus", "isActive", "elementCount", "elementMapSize", "size", "lastTimestamp", "highlightedElementsCount", "getLastScrapedTimestamp", "localStorage", "removeItem", "storageData", "xpathData", "AccountId", "Description", "agentDescription", "Name", "<PERSON><PERSON><PERSON>", "agentUrl", "setItem", "JSON", "stringify", "item", "index", "getItem", "parse", "apiData", "metadata", "exportedAt", "uploadXPathsFile", "formData", "FormData", "jsonBlob", "Blob", "pageTitle", "filename", "toString", "uploadXpathsFile", "Error", "response", "errorMessage", "message", "startAgentScraping", "elementNode", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "singleNodeValue", "catch", "chrome", "runtime", "sendMessage", "action", "dispatchEvent", "CustomEvent", "showScrapingInstructions", "startScraping", "stopScraping", "isAgentTraining", "removeEventListener", "filteredData", "getFilteredScrapedData", "agent", "TrainingFields", "hideScrapingInstructions", "cancelTraining", "primarySelector", "fallbackSelector", "instructionDiv", "mainTitle", "clickElement", "onlyClicked", "originalClick", "redBorders", "dataSaved", "innerHTML", "existingInstruction", "getElementById", "remove", "initScrapingService", "onMessage", "addListener", "_sender", "sendResponse", "success", "extractMinimalScrapeData"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/services/ScrapingService.ts"], "sourcesContent": ["// ScrapingService.ts - <PERSON>les targeted click-based scraping functionality\r\n\r\nimport useDrawerStore, { DrawerState } from '../store/drawerStore';\r\nimport {NewAgentTraining} from '../services/AIService';\r\nimport { constants } from 'node:crypto';\r\n\r\nimport i18n from '../multilinguial/i18n';\r\n\r\n// Get the translation function directly from i18next\r\nconst t = i18n.t.bind(i18n);\r\n\r\n\r\nlet _currentAgentData: {\r\n  accountId?: string;\r\n  agentDescription?: string;\r\n  agentName?: string;\r\n  agentUrl?: string;\r\n} | undefined = undefined;\r\n\r\n// Global state to track if scraping is active\r\nlet _isScrapingActive = false;\r\nlet _scrapedData: any[] = [];\r\nlet _lastScrapedTimestamp: string = '';\r\nlet _elementMap: Map<string, ElementData> = new Map(); // Map to track elements by XPath\r\nlet _clickListener: ((event: MouseEvent) => void) | null = null;\r\nlet _highlightedElements: Set<HTMLElement> = new Set(); // Track highlighted elements\r\nlet _overlayElements: Set<HTMLElement> = new Set(); // Track overlay elements for click blocking\r\n// Interface for element data\r\nexport interface ElementData {\r\n  tagName: string;\r\n  id: string;\r\n  className: string;\r\n  text: string;\r\n  labelName: string; // Add labelName for fallback identification\r\n  attributes: Record<string, string>;\r\n  xpath: string;\r\n  cssSelector: string;\r\n  selector: string; // Primary selector (xpath with cssSelector fallback)\r\n  rect: {\r\n    top: number;\r\n    left: number;\r\n    width: number;\r\n    height: number;\r\n  };\r\n  children: ElementData[];\r\n  isVisible: boolean;\r\n  timestamp: string;\r\n  url: string; // Add URL field to track which page the element is from\r\n}\r\n\r\n// Interface for scraped page data\r\nexport interface ScrapedPageData {\r\n  url: string;\r\n  title: string;\r\n  timestamp: string;\r\n  elements: ElementData[];\r\n}\r\nexport type ScrapedElement = {\r\n  labelName: string;\r\n  id: string;\r\n  selector: string;\r\n  xpath: string;\r\n  cssSelector: string;\r\n  value: string;\r\n  type: string;\r\n};\r\n\r\n/**\r\n * Check if scraping is currently active\r\n */\r\nexport const isScrapingActive = (): boolean => {\r\n  return _isScrapingActive;\r\n};\r\n\r\n/**\r\n * Set the scraping active state\r\n */\r\nexport const setScrapingActive = (active: boolean): void => {\r\n  _isScrapingActive = active;\r\n};\r\n\r\n/**\r\n * Generate XPath for an element\r\n */\r\n\r\nconst generateXPath = (element: HTMLElement): string => {\r\n  const tagName = element.tagName.toLowerCase();\r\n\r\n  // ✅ ejs-multiselect logic\r\n  if (tagName === 'ejs-multiselect') {\r\n    if (element.id) {\r\n      return `//*[@id=\"${element.id}\"]`;\r\n    }\r\n\r\n    const input = element.querySelector('input');\r\n    if (input && input.id) {\r\n      return `//*[@id=\"${input.id}\"]/ancestor::ejs-multiselect[1]`;\r\n    }\r\n\r\n    const dataId = element.getAttribute('data-id');\r\n    if (dataId) {\r\n      return `//ejs-multiselect[@data-id=\"${dataId}\"]`;\r\n    }\r\n\r\n    const className = element.className;\r\n    if (className) {\r\n      const uniqueClasses = className\r\n        .split(' ')\r\n        .filter(cls => cls && !cls.startsWith('e-') && cls.length > 2);\r\n      if (uniqueClasses.length > 0) {\r\n        return `//ejs-multiselect[contains(@class, \"${uniqueClasses[0]}\")]`;\r\n      }\r\n    }\r\n\r\n    return generatePositionalXPath(element);\r\n  }\r\n\r\n  // ✅ ejs-dropdownlist logic (updated)\r\n  const dropdownContainer = tagName === 'ejs-dropdownlist'\r\n    ? element\r\n    : element.closest('ejs-dropdownlist');\r\n\r\n  if (dropdownContainer instanceof HTMLElement) {\r\n    const dropdownId = dropdownContainer.id;\r\n    const input = dropdownContainer.querySelector('input');\r\n\r\n    if (dropdownId && input) {\r\n      return `//ejs-dropdownlist[@id=\"${dropdownId}\"]/div/input`;\r\n    }\r\n\r\n    return generatePositionalXPath(dropdownContainer);\r\n  }\r\n\r\n  if (element.id) {\r\n    return `//*[@id=\"${element.id}\"]`;\r\n  }\r\n\r\n  return generatePositionalXPath(element);\r\n};\r\n\r\nconst generatePositionalXPath = (element: HTMLElement): string => {\r\n  const path: string[] = [];\r\n  let current: Element | null = element;\r\n\r\n  while (current && current.nodeType === Node.ELEMENT_NODE) {\r\n    let selector = current.nodeName.toLowerCase();\r\n\r\n    if (current.id) {\r\n      selector += `[@id=\"${current.id}\"]`;\r\n      path.unshift(selector);\r\n      break;\r\n    } else {\r\n      let sibling = current.previousElementSibling;\r\n      let position = 1;\r\n      while (sibling) {\r\n        if (sibling.nodeName.toLowerCase() === selector) {\r\n          position++;\r\n        }\r\n        sibling = sibling.previousElementSibling;\r\n      }\r\n\r\n      if (position > 1) {\r\n        selector += `[${position}]`;\r\n      }\r\n\r\n      path.unshift(selector);\r\n    }\r\n\r\n    current = current.parentElement;\r\n  }\r\n\r\n  return '//' + path.join('/');\r\n};\r\n\r\n\r\n\r\n\r\n\r\n/**\r\n * Generate CSS selector for an element\r\n */\r\n\r\n\r\nconst generateCssSelector = (el: HTMLElement): string => {\r\n  if (!el) return '';\r\n  const path = [];\r\n  while (el && el.nodeType === Node.ELEMENT_NODE) {\r\n    let selector = el.tagName.toLowerCase();\r\n    if (el.id) {\r\n      // Check if ID starts with a number or contains special characters\r\n      const id = el.id;\r\n      if (/^[0-9]/.test(id) || /[^a-zA-Z0-9_-]/.test(id)) {\r\n        // Use attribute selector for IDs that start with numbers or have special chars\r\n        selector += `[id=\"${id}\"]`;\r\n      } else {\r\n        // Use standard ID selector for valid IDs\r\n        selector += `#${id}`;\r\n      }\r\n      path.unshift(selector);\r\n      break;\r\n    } else {\r\n      // Safely handle className - it might be a string or SVGAnimatedString\r\n      const className = getElementClassName(el);\r\n      if (className) {\r\n        selector += '.' + className.trim().replace(/\\s+/g, '.');\r\n      }\r\n      path.unshift(selector);\r\n      el = el.parentElement!;\r\n    }\r\n  }\r\n  return path.join(' > ');\r\n};\r\n\r\n/**\r\n * Safely get className from an element (handles both HTML and SVG elements)\r\n */\r\nconst getElementClassName = (el: Element): string => {\r\n  if (!el) return '';\r\n\r\n  // For HTML elements, className is usually a string\r\n  if (typeof el.className === 'string') {\r\n    return el.className;\r\n  }\r\n\r\n  // For SVG elements, className might be an SVGAnimatedString\r\n  if (el.className && typeof el.className === 'object' && 'baseVal' in el.className) {\r\n    return (el.className as any).baseVal || '';\r\n  }\r\n\r\n  // Fallback: try to get class attribute directly\r\n  return el.getAttribute('class') || '';\r\n};\r\n\r\n/**\r\n * Generate a labelName for an element that can be used for fallback identification\r\n */\r\nconst generateLabelName = (element: HTMLElement): string => {\r\n  // Try to find a meaningful label or name for the element\r\n\r\n  // 1. Check for explicit label element\r\n  if (element.id) {\r\n    const label = document.querySelector(`label[for=\"${element.id}\"]`);\r\n    if (label && label.textContent?.trim()) {\r\n      return label.textContent.trim();\r\n    }\r\n  }\r\n\r\n  // 2. Special handling for dropdown elements - prioritize text content over generic aria-labels\r\n  const tagName = element.tagName.toLowerCase();\r\n  const isDropdown = tagName === 'ejs-dropdownlist' ||\r\n                     element.closest('ejs-dropdownlist') ||\r\n                     element.getAttribute('role') === 'combobox' ||\r\n                     element.className.includes('dropdown') ||\r\n                     element.className.includes('select');\r\n\r\n  if (isDropdown) {\r\n    // For dropdowns, prioritize visible text content over generic aria-labels\r\n    const textContent = element.textContent?.trim();\r\n    if (textContent && textContent.length > 0) {\r\n      // Filter out generic dropdown text like \"dropdownlist\", \"combobox\", etc.\r\n      const genericTerms = ['dropdownlist', 'combobox', 'select', 'dropdown'];\r\n      const isGeneric = genericTerms.some(term =>\r\n        textContent.toLowerCase() === term.toLowerCase()\r\n      );\r\n\r\n      if (!isGeneric) {\r\n        return textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 3. Check for aria-label (but not for dropdowns with generic values)\r\n  const ariaLabel = element.getAttribute('aria-label');\r\n  if (ariaLabel?.trim()) {\r\n    // Skip generic aria-labels for dropdowns\r\n    if (isDropdown) {\r\n      const genericTerms = ['dropdownlist', 'combobox', 'select', 'dropdown'];\r\n      const isGeneric = genericTerms.some(term =>\r\n        ariaLabel.toLowerCase() === term.toLowerCase()\r\n      );\r\n      if (!isGeneric) {\r\n        return ariaLabel.trim();\r\n      }\r\n    } else {\r\n      return ariaLabel.trim();\r\n    }\r\n  }\r\n\r\n  // 4. Check for title attribute\r\n  const title = element.getAttribute('title');\r\n  if (title?.trim()) {\r\n    return title.trim();\r\n  }\r\n\r\n  // 5. Check for placeholder\r\n  const placeholder = element.getAttribute('placeholder');\r\n  if (placeholder?.trim()) {\r\n    return placeholder.trim();\r\n  }\r\n\r\n  // 6. Check for name attribute\r\n  const name = element.getAttribute('name');\r\n  if (name?.trim()) {\r\n    return name.trim();\r\n  }\r\n\r\n  // 7. Check for data-label or similar data attributes\r\n  const dataLabel = element.getAttribute('data-label') || element.getAttribute('data-name');\r\n  if (dataLabel?.trim()) {\r\n    return dataLabel.trim();\r\n  }\r\n\r\n  // 8. Check for text content (for non-dropdown elements or as fallback)\r\n  if (!isDropdown) {\r\n    const textContent = element.textContent?.trim();\r\n    if (textContent && textContent.length > 0) {\r\n      return textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;\r\n    }\r\n  }\r\n\r\n  // 9. Check for value attribute (for inputs)\r\n  const value = element.getAttribute('value');\r\n  if (value?.trim()) {\r\n    return value.trim();\r\n  }\r\n\r\n  // 10. Fallback to element type and id/class\r\n  const id = element.id;\r\n  const className = getElementClassName(element);\r\n\r\n  if (id) {\r\n    return `${tagName}#${id}`;\r\n  } else if (className) {\r\n    const firstClass = className.split(' ')[0];\r\n    return `${tagName}.${firstClass}`;\r\n  }\r\n\r\n  // 11. Final fallback\r\n  return tagName;\r\n};\r\n\r\n/**\r\n * Check if element should be ignored for highlighting\r\n */\r\nconst shouldIgnoreHighlight = (element: HTMLElement): boolean => {\r\n  return (\r\n    element.classList.contains('AgentAdditionalContextPopup') ||\r\n    element.classList.contains(\"mdc-tooltip__surface\") ||\r\n    element.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n    element.classList.contains(\"mdc-tooltip\") ||\r\n    element.classList.contains(\"mdc-tooltip--shown\") ||\r\n    element.classList.contains(\"mdc-tooltip--showing\") ||\r\n    element.classList.contains(\"mdc-tooltip--hiding\") ||\r\n    element.getAttribute(\"role\") === \"tooltip\" ||\r\n    !!element.closest(\"#Tooltip-unique\") ||\r\n    !!element.closest(\"#my-react-drawer\") ||\r\n    !!element.closest(\"#tooltip-section-popover\") ||\r\n    !!element.closest(\"#btn-setting-toolbar\") ||\r\n    !!element.closest(\"#button-toolbar\") ||\r\n    !!element.closest(\"#color-picker\") ||\r\n    !!element.closest(\".qadpt-ext-banner\") ||\r\n    !!element.closest(\"#leftDrawer\") ||\r\n    !!element.closest(\"#image-popover\") ||\r\n    !!element.closest(\"#toggle-fit\") ||\r\n    !!element.closest(\"#color-popover\") ||\r\n    !!element.closest(\"#rte-popover\") ||\r\n    !!element.closest(\"#rte-alignment\") ||\r\n    !!element.closest(\"#rte-alignment-menu\") ||\r\n    !!element.closest(\"#rte-font\") ||\r\n    !!element.closest(\"#rte-bold\") ||\r\n    !!element.closest(\"#rte-italic\") ||\r\n    !!element.closest(\"#rte-underline\") ||\r\n    !!element.closest(\"#rte-strke-through\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#rte-more\") ||\r\n    !!element.closest(\"#rte-text-color\") ||\r\n    !!element.closest(\"#rte-text-color-popover\") ||\r\n    !!element.closest(\"#rte-text-highlight\") ||\r\n    !!element.closest(\"#rte-text-highlight-pop\") ||\r\n    !!element.closest(\"#rte-text-heading\") ||\r\n    !!element.closest(\"#rte-text-heading-menu-items\") ||\r\n    !!element.closest(\"#rte-text-format\") ||\r\n    !!element.closest(\"#rte-text-ul\") ||\r\n    !!element.closest(\"#rte-text-hyperlink\") ||\r\n    !!element.closest(\"#rte-video\") ||\r\n    !!element.closest(\"#rte-clear-formatting\") ||\r\n    !!element.closest(\"#rte-hyperlink-popover\") ||\r\n    !!element.closest(\"#rte-box\") ||\r\n    !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") ||\r\n    !!element.closest(\"#rte-placeholder\") ||\r\n    !!element.closest(\"#qadpt-designpopup\") ||\r\n    !!element.closest(\"#image-properties\") ||\r\n    !!element.closest(\"#rte-toolbar\") ||\r\n    !!element.closest(\"#tooltipdialog\") ||\r\n    !!element.closest(\"#rte-toolbar-paper\") ||\r\n    !!element.closest(\"#stop-scraping-button-container\") ||\r\n    !!element.closest(\"#rte-alignment-menu\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#quickadapt-scraping-instructions\") || // Ignore our own instruction banner\r\n    !!element.closest(\"#AgentAdditionalContextPopup\") || // Ignore our own instruction banner\r\n    !!element.closest(\"#textareaadditionalcontextpopup\")\r\n\r\n  );\r\n};\r\n\r\n/**\r\n * Check if element should be ignored for events\r\n */\r\nconst shouldIgnoreEvents = (element: HTMLElement): boolean => {\r\n  return (\r\n    element.classList.contains(\"mdc-tooltip__surface\") ||\r\n    element.classList.contains(\"mdc-tooltip__surface-animation\") ||\r\n    element.classList.contains(\"mdc-tooltip\") ||\r\n    element.classList.contains(\"mdc-tooltip--shown\") ||\r\n    element.classList.contains(\"mdc-tooltip--showing\") ||\r\n    element.classList.contains(\"mdc-tooltip--hiding\") ||\r\n    element.getAttribute(\"role\") === \"tooltip\" ||\r\n    !!element.closest(\"#Tooltip-unique\") ||\r\n    !!element.closest(\"#tooltip-section-popover\") ||\r\n    !!element.closest(\"#btn-setting-toolbar\") ||\r\n    !!element.closest(\"#button-toolbar\") ||\r\n    !!element.closest(\"#color-picker\") ||\r\n    !!element.closest(\".qadpt-ext-banner\") ||\r\n    !!element.closest(\"#leftDrawer\") ||\r\n    !!element.closest(\"#rte-popover\") ||\r\n    !!element.closest(\"#stop-scraping-button-container\") ||\r\n    !!element.closest(element.id.startsWith(\"rt-editor\") ? `#${element.id}` : \"nope\") ||\r\n    !!element.closest(\"#rte-box\") ||\r\n    !!element.closest(\"#rte-placeholder\") ||\r\n    !!element.closest(\"#rte-alignment-menu-items\") ||\r\n    !!element.closest(\"#qadpt-designpopup\") ||\r\n    !!element.closest(\"#quickadapt-scraping-instructions\")||\r\n     // Ignore our own instruction banner\r\n     !!element.closest(\"#AgentAdditionalContextPopup\")|| // Ignore our own instruction banner\r\n    !!element.closest(\"#textareaadditionalcontextpopup\")\r\n  );\r\n};\r\n\r\n/**\r\n * Add persistent red border to element WITHOUT blocking clicks\r\n */\r\nconst addPersistentHighlightWithoutBlocking = (element: HTMLElement): void => {\r\n  if (shouldIgnoreHighlight(element)) return;\r\n\r\n  // Add persistent red border\r\n  element.style.outline = '3px solid #ff0000 !important';\r\n  element.style.outlineOffset = '2px';\r\n  element.setAttribute('data-quickadapt-highlighted', 'true');\r\n  _highlightedElements.add(element);\r\n\r\n  // No overlay creation - allow clicks to pass through\r\n};\r\n\r\n/**\r\n * Add persistent red border to element and create click-blocking overlay (legacy function)\r\n */\r\nconst addPersistentHighlight = (element: HTMLElement): void => {\r\n  if (shouldIgnoreHighlight(element)) return;\r\n\r\n  // Add persistent red border\r\n  element.style.outline = '3px solid #ff0000 !important';\r\n  element.style.outlineOffset = '2px';\r\n  element.setAttribute('data-quickadapt-highlighted', 'true');\r\n  _highlightedElements.add(element);\r\n\r\n  // Create click-blocking overlay\r\n  const overlay = document.createElement('div');\r\n  overlay.style.cssText = `\r\n    position: absolute;\r\n    top: ${element.offsetTop}px;\r\n    left: ${element.offsetLeft}px;\r\n    width: ${element.offsetWidth}px;\r\n    height: ${element.offsetHeight}px;\r\n    background: transparent;\r\n    z-index: 999999;\r\n    pointer-events: auto;\r\n    cursor: not-allowed;\r\n  `;\r\n  overlay.setAttribute('data-quickadapt-overlay', 'true');\r\n \r\n  \r\n  const tooltipText = t('Element already scraped click blocked');\r\n  overlay.title = tooltipText;\r\n\r\n  // Position overlay relative to the element's parent\r\n  const rect = element.getBoundingClientRect();\r\n  const parentRect = element.offsetParent?.getBoundingClientRect() || { top: 0, left: 0 };\r\n\r\n  overlay.style.top = `${rect.top - parentRect.top + window.scrollY}px`;\r\n  overlay.style.left = `${rect.left - parentRect.left + window.scrollX}px`;\r\n\r\n  // Add overlay to the element's parent or body\r\n  const parent = element.offsetParent || document.body;\r\n  parent.appendChild(overlay);\r\n  _overlayElements.add(overlay);\r\n\r\n  // Block clicks on the overlay\r\n  overlay.addEventListener('click', (e) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n  }, true);\r\n};\r\n\r\n/**\r\n * Remove all highlights and overlays\r\n */\r\nconst removeAllHighlights = (): void => {\r\n  // Remove highlights\r\n  _highlightedElements.forEach(element => {\r\n    if (element && element.style) {\r\n      element.style.outline = '';\r\n      element.style.outlineOffset = '';\r\n      element.removeAttribute('data-quickadapt-highlighted');\r\n    }\r\n  });\r\n  _highlightedElements.clear();\r\n\r\n  // Remove overlays\r\n  _overlayElements.forEach(overlay => {\r\n    if (overlay && overlay.parentNode) {\r\n      overlay.parentNode.removeChild(overlay);\r\n    }\r\n  });\r\n  _overlayElements.clear();\r\n};\r\n\r\n/**\r\n * Show brief visual feedback when an element is clicked and scraped\r\n */\r\nconst showClickFeedback = (element: HTMLElement): void => {\r\n  try {\r\n    // Create a temporary feedback indicator\r\n    const feedback = document.createElement('div');\r\n    feedback.style.cssText = `\r\n      position: absolute;\r\n      background: #4CAF50;\r\n      color: white;\r\n      padding: 4px 8px;\r\n      border-radius: 4px;\r\n      font-size: 12px;\r\n      font-weight: bold;\r\n      z-index: 10001;\r\n      pointer-events: none;\r\n      box-shadow: 0 2px 8px rgba(0,0,0,0.3);\r\n      opacity: 0;\r\n      transition: opacity 0.2s ease;\r\n    `;\r\n  \r\n    \r\n    const scrapedText = `✓ ${t('Scraped')}`;\r\n    feedback.textContent = scrapedText;\r\n\r\n    // Position the feedback near the clicked element\r\n    const rect = element.getBoundingClientRect();\r\n    feedback.style.left = `${rect.left + window.scrollX}px`;\r\n    feedback.style.top = `${rect.top + window.scrollY - 30}px`;\r\n\r\n    document.body.appendChild(feedback);\r\n\r\n    // Animate in\r\n    setTimeout(() => {\r\n      feedback.style.opacity = '1';\r\n    }, 10);\r\n\r\n    // Remove after 2 seconds\r\n    setTimeout(() => {\r\n      feedback.style.opacity = '0';\r\n      setTimeout(() => {\r\n        if (feedback.parentNode) {\r\n          feedback.parentNode.removeChild(feedback);\r\n        }\r\n      }, 200);\r\n    }, 2000);\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Extract data from a single element (optimized for click-based scraping)\r\n */\r\nconst extractElementData = (element: HTMLElement): ElementData & { value?: any ,type?:any } => {\r\n  const rect = element.getBoundingClientRect();\r\n  const xpath = generateXPath(element);\r\n  const type =   getElementType(element);\r\n  const cssSelector = generateCssSelector(element);\r\n\r\n  // Helper to get value from input/select/textarea or custom dropdowns\r\n  function getFieldValue(el: HTMLElement): any {\r\n    // Standard HTML form elements\r\n    if (el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) {\r\n      return el.value;\r\n    }\r\n    if (el instanceof HTMLSelectElement) {\r\n      return el.options[el.selectedIndex]?.text || el.value;\r\n    }\r\n    // Syncfusion/other custom dropdowns (e.g., ejs-dropdownlist)\r\n    // Try to find an input or selected item inside\r\n    const input = el.querySelector('input');\r\n    if (input && input instanceof HTMLInputElement) {\r\n      return input.value;\r\n    }\r\n    // Try to find selected item span (common in custom dropdowns)\r\n    const selectedSpan = el.querySelector('.e-selected-item, .e-list-item.e-active, .e-ddl .e-input, .e-dropdownlist .e-input');\r\n    if (selectedSpan) {\r\n      return selectedSpan.textContent?.trim();\r\n    }\r\n    // Try data-value attribute\r\n    if (el.hasAttribute('data-value')) {\r\n      return el.getAttribute('data-value');\r\n    }\r\n    return undefined;\r\n  }\r\n\r\n  // Try to get value from the element itself\r\n  let value = getFieldValue(element);\r\n  // If not found, try to get from a child input/select/textarea\r\n  if (value === undefined) {\r\n    const childField = element.querySelector('input, select, textarea');\r\n    if (childField) {\r\n      value = getFieldValue(childField as HTMLElement);\r\n    }\r\n  }\r\n\r\n  return {\r\n    tagName: element.tagName,\r\n    id: element.id || '',\r\n    className: getElementClassName(element),\r\n    text: element.textContent?.trim() || element.id,  //Added for agent training\r\n    labelName: generateLabelName(element), // Add labelName for fallback identification\r\n    attributes: Array.from(element.attributes).reduce((acc, attr) => {\r\n      acc[attr.name] = attr.value;\r\n      return acc;\r\n    }, {} as Record<string, string>),\r\n    xpath,\r\n    cssSelector,\r\n    selector: xpath || cssSelector, // Primary selector with fallback\r\n    rect: {\r\n      top: rect.top,\r\n      left: rect.left,\r\n      width: rect.width,\r\n      height: rect.height\r\n    },\r\n    children: [], // We don't need children for click-based scraping\r\n    isVisible: rect.width > 0 && rect.height > 0,\r\n    timestamp: new Date().toISOString(),\r\n    url: window.location.href, // Add URL to each element\r\n    type,\r\n    ...(value !== undefined ? { value } : {})\r\n  };\r\n};\r\n\r\n\r\nconst getElementType = (element: HTMLElement): any => {\r\n  const tagName = element.tagName.toLowerCase();\r\n\r\n  // Check for ejs-radiobutton first\r\n  if (tagName === 'ejs-radiobutton' || element.closest('ejs-radiobutton')) {\r\n    return 'radio';\r\n  }\r\n\r\n  if (tagName === 'input') {\r\n    const inputElement = element as HTMLInputElement;\r\n    const type = inputElement.type || 'text';\r\n\r\n    // Special handling for date inputs\r\n    if (\r\n      type === 'text' &&\r\n      (\r\n        element.id.toLowerCase().includes('date') ||\r\n        element.className.toLowerCase().includes('date') ||\r\n        (element.getAttribute('placeholder') || '').toLowerCase().includes('date')\r\n      )\r\n    ) {\r\n      // Check if it's a date range input or single date input\r\n      const value = inputElement.value || '';\r\n      const placeholder = element.getAttribute('placeholder') || '';\r\n      const id = element.id.toLowerCase();\r\n      const className = element.className.toLowerCase();\r\n      const name = (element.getAttribute('name') || '').toLowerCase();\r\n\r\n      // Check if the value contains date range patterns (e.g., \"01/01/2023 - 01/31/2023\")\r\n      const dateRangePattern = /\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}\\s*[-–—]\\s*\\d{1,2}[\\/\\-\\.]\\d{1,2}[\\/\\-\\.]\\d{2,4}/;\r\n      const hasDateRangeValue = dateRangePattern.test(value);\r\n\r\n      // Check for explicit date range terms (more specific)\r\n      const explicitRangeTerms = [\r\n        'daterange', 'date-range', 'date_range',\r\n        'period', 'duration', 'span','dates'\r\n      ];\r\n      const hasExplicitRangeClass = explicitRangeTerms.some(term =>\r\n        id.includes(term) || className.includes(term) || name.includes(term)\r\n      );\r\n\r\n      // Check for range indicators in a more specific way\r\n      const rangeIndicators = [\r\n        'fromdate', 'todate', 'startdate', 'enddate',\r\n        'date_from', 'date_to', 'date-from', 'date-to',\r\n        'start_date', 'end_date', 'start-date', 'end-date'\r\n      ];\r\n\r\n      const hasSpecificRangeIndicator = rangeIndicators.some(indicator =>\r\n        id.includes(indicator) || className.includes(indicator) || name.includes(indicator)\r\n      );\r\n\r\n      // Check for range indicators in placeholder (more specific)\r\n      const placeholderRangeTerms = ['from', 'to', 'between', 'range', '-', '–', '—'];\r\n      const hasPlaceholderRangeIndicator = placeholderRangeTerms.some(term =>\r\n        placeholder.toLowerCase().includes(term)\r\n      );\r\n\r\n      // Return daterange only if we have strong indicators, otherwise dateinput\r\n      if (hasDateRangeValue || hasExplicitRangeClass || hasSpecificRangeIndicator || hasPlaceholderRangeIndicator) {\r\n        return 'daterange';\r\n      } else {\r\n        return 'dateinput';\r\n      }\r\n    }\r\n\r\n    // Inside a dropdown component\r\n    if (element.closest('ejs-dropdownlist, ejs-combobox, ejs-multiselect, [role=\"combobox\"]')) {\r\n      return 'dropdown';\r\n    }\r\n\r\n    return type;\r\n  }\r\n\r\n  // ejs-dropdownlist\r\n  if (tagName === 'ejs-dropdownlist' || element.closest('ejs-dropdownlist')) {\r\n    return 'dropdown';\r\n  }\r\n\r\n  // select\r\n  if (tagName === 'select') {\r\n    const selectElement = element as HTMLSelectElement;\r\n    return selectElement.multiple ? 'multiselect' : 'dropdown';\r\n  }\r\n\r\n  // textarea\r\n  if (tagName === 'textarea') {\r\n    return 'textarea';\r\n  }\r\n\r\n  // contenteditable\r\n  if (element.contentEditable === 'true') {\r\n    return 'contenteditable';\r\n  }\r\n\r\n  // dropdown-like custom components\r\n  if (\r\n    element.getAttribute('role') === 'combobox' ||\r\n    element.getAttribute('role') === 'listbox' ||\r\n    element.className.includes('dropdown') ||\r\n    element.className.includes('select')\r\n  ) {\r\n    return 'dropdown';\r\n  }\r\n\r\n  // ✅ Fallback for clickable, non-input UI elements\r\n  return 'click';\r\n};\r\n\r\n\r\n\r\n\r\n\r\n/**\r\n * Handle click events for element scraping\r\n */\r\n\r\nconst handleElementClick = async ( event: MouseEvent,agentData?: {\r\n  accountId?: string;\r\n  agentDescription?: string;\r\n  agentName?: string;\r\n  agentUrl?: string;\r\n}): Promise<void> => {\r\n  try {\r\n    // IMPORTANT: Don't prevent default or stop propagation\r\n    // This allows the original click functionality to work normally\r\n    // (navigation, form submission, button clicks, etc.)\r\n\r\n    const target = event.target as HTMLElement;\r\n    if (!target || !target.nodeType || target.nodeType !== Node.ELEMENT_NODE) {\r\n      return;\r\n    }\r\n\r\n    if (shouldIgnoreEvents(target)) {\r\n      return;\r\n    }\r\n\r\n    if (target.hasAttribute('data-quickadapt-highlighted')) {\r\n      return;\r\n    }\r\n\r\n\r\n    // Extract data from clicked element ONLY\r\n    const clickedElementData = extractElementData(target);\r\n\r\n    // Store only the clicked element data (no parent element)\r\n    const elementsToStore = [clickedElementData];\r\n\r\n    // Add to scraped data\r\n    console.log(`📝 Attempting to scrape element: ${target.tagName} with XPath: ${clickedElementData.xpath}`);\r\n    setScrapedData({ elements: elementsToStore }, true);\r\n\r\n    // Save to local storage immediately after each element is scraped\r\n\r\n    await saveScrapedDataToStorage(agentData);\r\n    console.log(`💾 Element data saved to local storage immediately`);\r\n\r\n    // Add persistent red border WITHOUT blocking clicks (only to clicked element)\r\n    addPersistentHighlightWithoutBlocking(target);\r\n\r\n\r\n    // // Show brief success feedback\r\n    // showClickFeedback(target);\r\n\r\n\r\n  } catch (error) {\r\n    console.error('Error in handleElementClick:', error);\r\n  }\r\n};\r\n\r\nexport const setScrapedData = (data: any, append: boolean = false): void => {\r\n  const timestamp = new Date().toISOString();\r\n  _lastScrapedTimestamp = timestamp;\r\n\r\n  if (!append) {\r\n    // Clear existing data if not appending\r\n    _scrapedData = [];\r\n    _elementMap.clear();\r\n  }\r\n\r\n  // Process each element in the data\r\n  if (data && data.elements && Array.isArray(data.elements)) {\r\n    data.elements.forEach((element: ElementData) => {\r\n      // Add timestamp to the element\r\n      element.timestamp = timestamp;\r\n\r\n      // Use XPath as a unique identifier for the element\r\n      if (element.xpath) {\r\n        // If element already exists in the map, don't add it again (prevent duplicates)\r\n        if (_elementMap.has(element.xpath)) {\r\n          console.log(`⚠️ Skipping duplicate element with XPath: ${element.xpath}`);\r\n          return; // Skip this element\r\n        } else {\r\n          // New element, add to map and data array\r\n          console.log(`✅ Adding new element with XPath: ${element.xpath}`);\r\n          _elementMap.set(element.xpath, element);\r\n          _scrapedData.push(element);\r\n        \r\n        }\r\n      } else {\r\n        // No XPath, check for duplicates by other means (tagName + id + className)\r\n        const isDuplicate = _scrapedData.some(existing =>\r\n          existing.tagName === element.tagName &&\r\n          existing.id === element.id &&\r\n          existing.className === element.className\r\n        );\r\n\r\n        if (!isDuplicate) {\r\n          _scrapedData.push(element);\r\n\r\n        } else {\r\n\r\n        }\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * Get the currently scraped data\r\n */\r\nexport const getScrapedData = (): any[] => {\r\n  return _scrapedData;\r\n};\r\n\r\n/**\r\n * Get element by XPath\r\n */\r\nexport const getElementByXPath = (xpath: string): ElementData | undefined => {\r\n  return _elementMap.get(xpath);\r\n};\r\n\r\n/**\r\n * Find DOM element using fallback mechanisms\r\n * Priority: xpath -> labelName -> cssSelector\r\n */\r\n// export const findElementWithFallback = (elementData: {\r\n//   xpath?: string;\r\n//   labelName?: string;\r\n//   cssSelector?: string;\r\n//   id?: string;\r\n// }): HTMLElement | null => {\r\n//   // Primary: Try xpath first\r\n//   if (elementData.xpath) {\r\n//     try {\r\n//       const result = document.evaluate(\r\n//         elementData.xpath,\r\n//         document,\r\n//         null,\r\n//         XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n//         null\r\n//       );\r\n//       const element = result.singleNodeValue as HTMLElement;\r\n//       if (element) {\r\n//         console.log(`✅ Found element using xpath: ${elementData.xpath}`);\r\n//         return element;\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ XPath failed: ${elementData.xpath}`, error);\r\n//     }\r\n//   }\r\n\r\n//   // Fallback 1: Try to find by labelName\r\n//   if (elementData.labelName) {\r\n//     console.log(`🔄 Falling back to labelName: ${elementData.labelName}`);\r\n\r\n//     // Try different approaches to find by labelName\r\n//     const labelSelectors = [\r\n//       `[aria-label=\"${elementData.labelName}\"]`,\r\n//       `[title=\"${elementData.labelName}\"]`,\r\n//       `[placeholder=\"${elementData.labelName}\"]`,\r\n//       `[name=\"${elementData.labelName}\"]`,\r\n//       `[data-label=\"${elementData.labelName}\"]`,\r\n//       `[data-name=\"${elementData.labelName}\"]`\r\n//     ];\r\n\r\n//     for (const selector of labelSelectors) {\r\n//       try {\r\n//         const element = document.querySelector(selector) as HTMLElement;\r\n//         if (element) {\r\n//           console.log(`✅ Found element using labelName selector: ${selector}`);\r\n//           return element;\r\n//         }\r\n//       } catch (error) {\r\n//         console.warn(`❌ LabelName selector failed: ${selector}`, error);\r\n//       }\r\n//     }\r\n\r\n//     // Try to find by text content containing the labelName\r\n//     try {\r\n//       const xpath = `//*[contains(text(), \"${elementData.labelName}\")]`;\r\n//       const result = document.evaluate(\r\n//         xpath,\r\n//         document,\r\n//         null,\r\n//         XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n//         null\r\n//       );\r\n//       const element = result.singleNodeValue as HTMLElement;\r\n//       if (element) {\r\n//         console.log(`✅ Found element using text content: ${elementData.labelName}`);\r\n//         return element;\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ Text content search failed for: ${elementData.labelName}`, error);\r\n//     }\r\n\r\n//     // Try to find label element and get its associated input\r\n//     try {\r\n//       const labelElement = Array.from(document.querySelectorAll('label')).find(\r\n//         label => label.textContent?.trim() === elementData.labelName\r\n//       );\r\n//       if (labelElement) {\r\n//         const forAttribute = labelElement.getAttribute('for');\r\n//         if (forAttribute) {\r\n//           const associatedElement = document.getElementById(forAttribute) as HTMLElement;\r\n//           if (associatedElement) {\r\n//             console.log(`✅ Found element using label association: ${elementData.labelName}`);\r\n//             return associatedElement;\r\n//           }\r\n//         }\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ Label association search failed for: ${elementData.labelName}`, error);\r\n//     }\r\n//   }\r\n\r\n//   // Fallback 2: Try cssSelector\r\n//   if (elementData.cssSelector) {\r\n//     console.log(`🔄 Falling back to cssSelector: ${elementData.cssSelector}`);\r\n//     try {\r\n//       const element = document.querySelector(elementData.cssSelector) as HTMLElement;\r\n//       if (element) {\r\n//         console.log(`✅ Found element using cssSelector: ${elementData.cssSelector}`);\r\n//         return element;\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ CSS selector failed: ${elementData.cssSelector}`, error);\r\n\r\n//       // Try to fix common CSS selector issues\r\n//       if (elementData.cssSelector.includes('#')) {\r\n//         try {\r\n//           // Extract ID and try attribute selector\r\n//           const idMatch = elementData.cssSelector.match(/#([^.\\s>+~]+)/);\r\n//           if (idMatch) {\r\n//             const id = idMatch[1];\r\n//             const tagMatch = elementData.cssSelector.match(/^([a-zA-Z]+)/);\r\n//             const tag = tagMatch ? tagMatch[1] : '';\r\n//             const attributeSelector = tag ? `${tag}[id=\"${id}\"]` : `[id=\"${id}\"]`;\r\n\r\n//             console.log(`🔄 Trying attribute selector fallback: ${attributeSelector}`);\r\n//             const element = document.querySelector(attributeSelector) as HTMLElement;\r\n//             if (element) {\r\n//               console.log(`✅ Found element using attribute selector: ${attributeSelector}`);\r\n//               return element;\r\n//             }\r\n//           }\r\n//         } catch (attributeError) {\r\n//           console.warn(`❌ Attribute selector fallback also failed`, attributeError);\r\n//         }\r\n//       }\r\n//     }\r\n//   }\r\n\r\n//   // Final fallback: Try by ID if available\r\n//   if (elementData.id) {\r\n//     console.log(`🔄 Final fallback to ID: ${elementData.id}`);\r\n//     try {\r\n//       const element = document.getElementById(elementData.id) as HTMLElement;\r\n//       if (element) {\r\n//         console.log(`✅ Found element using ID: ${elementData.id}`);\r\n//         return element;\r\n//       }\r\n//     } catch (error) {\r\n//       console.warn(`❌ ID selector failed: ${elementData.id}`, error);\r\n//     }\r\n//   }\r\n\r\n//   console.error(`❌ All fallback methods failed for element:`, elementData);\r\n//   return null;\r\n// };\r\n\r\n/**\r\n * Test function to demonstrate element finding with fallback mechanisms\r\n * This can be called from browser console for testing\r\n */\r\n// export const testElementFinding = (elementData: {\r\n//   xpath?: string;\r\n//   labelName?: string;\r\n//   cssSelector?: string;\r\n//   id?: string;\r\n// }): void => {\r\n//   console.log('🧪 Testing element finding with fallback mechanisms...');\r\n//   console.log('Input data:', elementData);\r\n\r\n//   const foundElement = findElementWithFallback(elementData);\r\n\r\n//   if (foundElement) {\r\n//     console.log('✅ Successfully found element:', foundElement);\r\n//     console.log('Element details:', {\r\n//       tagName: foundElement.tagName,\r\n//       id: foundElement.id,\r\n//       className: foundElement.className,\r\n//       textContent: foundElement.textContent?.substring(0, 100)\r\n//     });\r\n//   } else {\r\n//     console.log('❌ Could not find element with any fallback method');\r\n//   }\r\n// };\r\n\r\n\r\n\r\n/**\r\n * Get all xpath data from scraped elements\r\n */\r\nexport const getXPathData = (): Array<{xpath: string, tagName: string, id: string, className: string, text: string, timestamp: string, url: string}> => {\r\n  return _scrapedData.map(element => ({\r\n    xpath: element.xpath,\r\n    tagName: element.tagName,\r\n    id: element.id,\r\n    className: element.className,\r\n    text: element.text,\r\n    timestamp: element.timestamp,\r\n    url: element.url\r\n  }));\r\n};\r\n\r\n/**\r\n * Manually send current scraped data to backend API (can be called independently)\r\n */\r\nexport const exportScrapedDataToFile = async (accountId?: string): Promise<void> => {\r\n  try {\r\n    if (_scrapedData.length === 0) {\r\n      // Try to load from storage if no current data\r\n      const storedData = await loadScrapedDataFromStorage();\r\n      if (!storedData || !storedData.scrapedData || storedData.scrapedData.length === 0) {\r\n        // alert('No scraped data available to send to API. Please scrape some elements first.');\r\n        return;\r\n      }\r\n      // Use stored data for API call\r\n      await saveScrapedDataToFile(accountId);\r\n    } else {\r\n      // Save current data to storage first, then send to API\r\n      await saveScrapedDataToStorage();\r\n      await saveScrapedDataToFile(accountId);\r\n    }\r\n  } catch (error) {\r\n    // alert('Error sending scraped data to backend API. Check console for details.');\r\n  }\r\n};\r\n\r\n/**\r\n * Get scraped data count\r\n */\r\nexport const getScrapedDataCount = (): number => {\r\n  return _scrapedData.length;\r\n};\r\n\r\n/**\r\n * Check if there's existing scraped data in storage\r\n */\r\nexport const hasScrapedDataInStorage = async (): Promise<boolean> => {\r\n  try {\r\n    const storedData = await loadScrapedDataFromStorage();\r\n    return storedData && storedData.scrapedData && Array.isArray(storedData.scrapedData) && storedData.scrapedData.length > 0;\r\n  } catch (error) {\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Clear all scraped data (both in-memory and storage) - use when starting completely fresh\r\n */\r\nexport const clearAllScrapedData = async (): Promise<void> => {\r\n  console.log('🧹 Clearing all scraped data (memory + storage)');\r\n  clearScrapedData(); // Clear in-memory data\r\n  await clearScrapedDataFromStorage(); // Clear storage data\r\n  removeAllHighlights(); // Clear visual highlights\r\n};\r\n\r\n/**\r\n * Get current scraping status for debugging\r\n */\r\nexport const getScrapingStatus = (): {\r\n  isActive: boolean;\r\n  elementCount: number;\r\n  elementMapSize: number;\r\n  lastTimestamp: string;\r\n  highlightedElementsCount: number;\r\n} => {\r\n  return {\r\n    isActive: _isScrapingActive,\r\n    elementCount: _scrapedData.length,\r\n    elementMapSize: _elementMap.size,\r\n    lastTimestamp: _lastScrapedTimestamp,\r\n    highlightedElementsCount: _highlightedElements.size\r\n  };\r\n};\r\n\r\n/**\r\n * Get the timestamp of the last scrape\r\n */\r\nexport const getLastScrapedTimestamp = (): string => {\r\n  return _lastScrapedTimestamp;\r\n};\r\n\r\n/**\r\n * Clear scraped data\r\n */\r\nexport const clearScrapedData = (): void => {\r\n  console.log(`🧹 Clearing scraped data - had ${_scrapedData.length} elements and ${_elementMap.size} in element map`);\r\n  _scrapedData = [];\r\n  _lastScrapedTimestamp = '';\r\n  _elementMap.clear(); // Clear the element map to allow re-scraping of same elements\r\n};\r\n\r\n/**\r\n * Clear scraped data from Chrome storage (for debugging/reset purposes)\r\n */\r\nexport const clearScrapedDataFromStorage = async (): Promise<void> => {\r\n \r\n  try {\r\n          localStorage.removeItem('quickadapt-scraped-data');\r\n\r\n  } catch (error) {\r\n    console.error('Error clearing scraped data from storage:', error);\r\n  }\r\n};\r\n\r\n/**\r\n * Save scraped data to Chrome storage\r\n */\r\n\r\nexport const saveScrapedDataToStorage = async (agentData?: {\r\n  accountId?: string;\r\n  agentDescription?: string;\r\n  agentName?: string;\r\n  agentUrl?: string;\r\n}): Promise<void> => {\r\n  try {\r\n      const storageData = {\r\n        scrapedData: _scrapedData,\r\n        timestamp: _lastScrapedTimestamp,\r\n        url: window.location.href,\r\n        title: document.title,\r\n        elementCount: _scrapedData.length,\r\n        xpathData: _scrapedData.map(element => ({\r\n          xpath: element.xpath,\r\n          tagName: element.tagName,\r\n          id: element.id,\r\n          className: element.className,\r\n          text: element.text,\r\n          labelName: element.labelName,\r\n          cssSelector: element.cssSelector,\r\n          selector: element.selector,\r\n          attributes: element.attributes,\r\n          timestamp: element.timestamp,\r\n          url: element.url\r\n\r\n        })),\r\n        // Add agent data if provided\r\n        ...(agentData && {\r\n          agentData: {\r\n            AccountId: agentData.accountId,\r\n            Description: agentData.agentDescription,\r\n            Name: agentData.agentName,\r\n            url: agentData.agentUrl\r\n          }\r\n        })\r\n      };\r\n    localStorage.setItem('quickadapt-scraped-data', JSON.stringify(storageData));\r\n   storageData.xpathData.forEach((item, index) => {\r\n        console.log(`${index + 1}. ${item.tagName} - ${item.xpath}`);\r\n      });\r\n\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Load scraped data from Chrome storage\r\n */\r\nexport const loadScrapedDataFromStorage = async (): Promise<any> => {\r\n  try {\r\n    \r\n      const data = localStorage.getItem('quickadapt-scraped-data');\r\n      return data ? JSON.parse(data) : null;\r\n    \r\n  } catch (error) {\r\n    return null;\r\n  }\r\n};\r\n\r\n\r\n\r\n/**\r\n * Send scraped data from Chrome storage to backend API\r\n */\r\nexport const saveScrapedDataToFile = async (accountId?: string): Promise<void> => {\r\n  try {\r\n\r\n    const storedData = await loadScrapedDataFromStorage();\r\n\r\n    if (!storedData) {\r\n      return;\r\n    }\r\n\r\n    const apiData = {\r\n      metadata: {\r\n        url: storedData.url || window.location.href,\r\n        title: storedData.title || document.title,\r\n        timestamp: storedData.timestamp || new Date().toISOString(),\r\n        elementCount: storedData.elementCount || 0,\r\n        exportedAt: new Date().toISOString()\r\n      },\r\n      elements: storedData.scrapedData || [],\r\n      xpathData: storedData.xpathData || []\r\n    };\r\n\r\n\r\n    // Send data to backend API\r\n    await uploadXPathsFile(apiData, accountId);\r\n\r\n  } catch (error) {\r\n  }\r\n};\r\n\r\n/**\r\n * Upload XPath data to backend API using existing FileService\r\n */\r\nexport const uploadXPathsFile = async (data: any, accountId?: string): Promise<void> => {\r\n  try {\r\n\r\n    // Convert JSON data to FormData as expected by the existing API\r\n    const formData = new FormData();\r\n\r\n    // Create a JSON file blob\r\n    const jsonBlob = new Blob([JSON.stringify(data, null, 2)], {\r\n      type: 'application/json'\r\n    });\r\n\r\n    // Generate filename with timestamp\r\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\r\n    const pageTitle = (data.metadata.title || 'scraped-data').replace(/[^a-zA-Z0-9]/g, '-');\r\n    const filename = `quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;\r\n\r\n    // Add the file to FormData\r\n    formData.append('aifiles', jsonBlob, filename); // ✅ Correct key name\r\n\r\n    // Add metadata as form fields if needed\r\n    formData.append('elementCount', data.metadata.elementCount.toString());\r\n    formData.append('url', data.metadata.url);\r\n    formData.append('timestamp', data.metadata.timestamp);\r\n\r\n   \r\n\r\n    // Import and use the existing uploadXpathsFile function\r\n    const { uploadXpathsFile } = await import('./FileService');\r\n\r\n    if (!accountId) {\r\n      throw new Error('Account ID is required to upload XPath data');\r\n    }\r\n\r\n    const response = await uploadXpathsFile(accountId, formData);\r\n\r\n  } catch (error) {\r\n\r\n    // Show error message to user\r\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\r\n\r\n    throw error; // Re-throw to be caught by calling function\r\n  }\r\n};\r\n\r\n/**\r\n * Start click-based scraping process\r\n */\r\n\r\nexport const startAgentScraping = async (agentName: string, agentDescription: string, accountId: string, agentUrl: string): Promise<void> => {\r\n  if (_isScrapingActive) return;\r\n\r\n  _isScrapingActive = true;\r\n  console.log('🎯 Starting scraping session - checking storage consistency');\r\n\r\n  // Check if Chrome storage has scraped data\r\n  const storedData = await loadScrapedDataFromStorage();\r\n  if (!storedData || !storedData.scrapedData || !Array.isArray(storedData.scrapedData) || storedData.scrapedData.length === 0) {\r\n    console.log('📊 No valid data in Chrome storage - clearing in-memory data');\r\n    clearScrapedData(); // Clear in-memory data if storage is empty\r\n    await saveScrapedDataToStorage({\r\n      accountId,\r\n      agentDescription,\r\n      agentName,\r\n      agentUrl\r\n    });\r\n  } else {\r\n    \r\n    console.log(`📊 Storage validation passed - ${storedData.scrapedData.length} elements in storage, ${_scrapedData.length} in memory`);\r\n  }\r\n\r\n  console.log(`📊 Current scraped elements count: ${_scrapedData.length}`);\r\n _currentAgentData = {\r\n    accountId,\r\n    agentDescription,\r\n    agentName,\r\n    agentUrl\r\n  };\r\n  // Re-highlight existing scraped elements instead of clearing all highlights\r\n  _scrapedData.forEach(element => {\r\n    if (element.xpath) {\r\n      try {\r\n        const elementNode = document.evaluate(\r\n          element.xpath,\r\n          document,\r\n          null,\r\n          XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n          null\r\n        ).singleNodeValue as HTMLElement;\r\n\r\n        if (elementNode) {\r\n          addPersistentHighlightWithoutBlocking(elementNode);\r\n        }\r\n      } catch (error) {\r\n        // Element might not exist anymore, that's okay\r\n      }\r\n    }\r\n  });\r\n\r\n  // Add click event listener to capture element clicks\r\n  if (!_clickListener) {\r\n    _clickListener = (event: MouseEvent) => {\r\n      // Call the async function without awaiting to avoid blocking the event handler\r\n\r\n      handleElementClick(event,_currentAgentData).catch(error => {\r\n        console.error('Error in click handler:', error);\r\n      });\r\n    };\r\n    document.addEventListener('click', _clickListener, true); // Use capture phase\r\n  }\r\n\r\n  // Send message to content script to enable click-based scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'startClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));\r\n    }\r\n  }\r\n\r\n \r\n  // Show user instruction with translation support\r\n  showScrapingInstructions();\r\n};\r\n\r\n \r\nexport const startScraping = (): void => {\r\n  if (_isScrapingActive) return;\r\n\r\n  _isScrapingActive = true;\r\n  clearScrapedData();\r\n\r\n\r\n  // Add click event listener to capture element clicks\r\n  if (!_clickListener) {\r\n    _clickListener = handleElementClick;\r\n    document.addEventListener('click', _clickListener, true); // Use capture phase\r\n  }\r\n\r\n  // Send message to content script to enable click-based scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'startClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Show user instruction\r\n  showScrapingInstructions();\r\n};\r\n\r\n\r\n \r\n\r\n\r\n\r\n/**\r\n * Stop click-based scraping process\r\n */\r\nexport const stopScraping = async (\r\n  isAgentTraining: boolean,\r\n  accountId: string,\r\n  agentName?: string,\r\n  agentDescription?: string,\r\n  agentUrl?: string,\r\n\r\n): Promise<void> => {\r\n  if (!_isScrapingActive) return;\r\n  _isScrapingActive = false;\r\n\r\n\r\n  // Remove click event listener\r\n  if (_clickListener) {\r\n    document.removeEventListener('click', _clickListener, true);\r\n    _clickListener = null;\r\n  }\r\n\r\n  // Process scraped data before clearing from storage\r\n  if (_scrapedData.length > 0) {\r\n\r\n\r\n    // Save to storage one final time to ensure we have the latest data, including agent data\r\n     await saveScrapedDataToStorage({\r\n      accountId,\r\n      agentDescription,\r\n       agentName,\r\n      agentUrl\r\n    });\r\n\r\n\r\n\r\n    // Get data from Chrome storage and save to file\r\n    !isAgentTraining && await saveScrapedDataToFile(accountId);\r\n   const filteredData = await getFilteredScrapedData();\r\n   console.log(filteredData,\"filteredData\");\r\n\r\n    const agent = {\r\n      AccountId:accountId,\r\n      Description:agentDescription,\r\n      Name:agentName,\r\n      TrainingFields:filteredData,\r\n      url: window.location.href\r\n    };\r\n    if (isAgentTraining && agentName && agentDescription) {\r\n      await NewAgentTraining(agent);\r\n    }\r\n  }\r\n\r\n     await clearScrapedDataFromStorage();\r\n\r\n\r\n  // Remove all highlights and overlays\r\n  removeAllHighlights();\r\n\r\n  // Send message to background script to stop scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'stopClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Hide instructions\r\n  hideScrapingInstructions();\r\n};\r\n\r\n/**\r\n * Cancel training process - stops scraping and clears data without processing\r\n */\r\nexport const cancelTraining = async (): Promise<void> => {\r\n  if (!_isScrapingActive) return;\r\n  _isScrapingActive = false;\r\n\r\n  // Remove click event listener\r\n  if (_clickListener) {\r\n    document.removeEventListener('click', _clickListener, true);\r\n    _clickListener = null;\r\n  }\r\n\r\n  // Clear scraped data from storage immediately without processing\r\n  await clearScrapedDataFromStorage();\r\n  console.log('🧹 Cleared scraped data from storage after cancel training');\r\n\r\n  // Clear in-memory data\r\n  clearScrapedData();\r\n\r\n  // Remove all highlights and overlays\r\n  removeAllHighlights();\r\n\r\n  // Send message to background script to stop scraping\r\n  if (typeof chrome !== 'undefined' && chrome.runtime) {\r\n    try {\r\n      chrome.runtime.sendMessage({\r\n        action: 'stopClickScraping'\r\n      });\r\n    } catch (error) {\r\n      // Fallback: try to communicate through window events\r\n      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));\r\n    }\r\n  }\r\n\r\n  // Hide instructions\r\n  hideScrapingInstructions();\r\n};\r\n\r\nexport const getFilteredScrapedData = async (): Promise<ScrapedElement[]> => {\r\n  const storedData = await loadScrapedDataFromStorage();\r\n  if (!storedData || !Array.isArray(storedData.scrapedData)) return [];\r\n\r\n  return storedData.scrapedData.map((item: any) => {\r\n    // Implement fallback logic for selector identification\r\n    let primarySelector = item.xpath || '';\r\n    let fallbackSelector = item.cssSelector || '';\r\n\r\n    // If no xpath, use labelName as fallback identifier\r\n    if (!primarySelector && item.labelName) {\r\n      primarySelector = `[aria-label=\"${item.labelName}\"]`; // Try aria-label first\r\n      // Additional fallback selectors based on labelName\r\n      if (!primarySelector) {\r\n        primarySelector = `[title=\"${item.labelName}\"]`; // Try title attribute\r\n      }\r\n      if (!primarySelector) {\r\n        primarySelector = `[placeholder=\"${item.labelName}\"]`; // Try placeholder\r\n      }\r\n      if (!primarySelector) {\r\n        primarySelector = `[name=\"${item.labelName}\"]`; // Try name attribute\r\n      }\r\n    }\r\n\r\n    // If still no selector, use cssSelector as final fallback\r\n    if (!primarySelector) {\r\n      primarySelector = fallbackSelector;\r\n    }\r\n\r\n    return {\r\n      Name:item.text || '',\r\n      xpath: item.xpath || '',\r\n      labelName: item.labelName  || '',\r\n      selector: primarySelector,\r\n      cssSelector: item.cssSelector || '',\r\n      value: item.value || '',\r\n      type: item.type || '',\r\n    };\r\n  });\r\n};\r\n\r\n/**\r\n * Show scraping instructions to user\r\n */\r\nconst showScrapingInstructions = (): void => {\r\n  // Remove existing instruction if any\r\n  hideScrapingInstructions();\r\n\r\n  const instructionDiv = document.createElement('div');\r\n  instructionDiv.id = 'quickadapt-scraping-instructions';\r\n  instructionDiv.style.cssText = `\r\n    position: fixed;\r\n    top: 20px;\r\n    right: 20px;\r\n    background: #4CAF50;\r\n    color: white;\r\n    padding: 15px 20px;\r\n    border-radius: 8px;\r\n    font-family: Arial, sans-serif;\r\n    font-size: 14px;\r\n    font-weight: bold;\r\n    z-index: 10000;\r\n    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n    max-width: 320px;\r\n    text-align: center;\r\n  `;\r\n  \r\n  // Use translations with i18n instance directly\r\n  const mainTitle = `🎯 ${t('Click Scraping Active')}`;\r\n  const clickElement = `• ${t('Click any element to scrape its XPath data')}`;\r\n  const onlyClicked = `• ${t('Only the clicked element is scraped no duplicates')}`;\r\n  const originalClick = `• ${t('Original click functionality still works')}`;\r\n  const redBorders = `• ${t('Red borders show scraped elements')}`;\r\n  const dataSaved = `• ${t('Data is saved to Chrome storage')}`;\r\n  \r\n  instructionDiv.innerHTML = `\r\n  \r\n    ${mainTitle}<br>\r\n    <small style=\"font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;\">\r\n      ${clickElement}<br>\r\n      ${onlyClicked}<br>\r\n      ${originalClick}<br>\r\n      ${redBorders}<br>\r\n      ${dataSaved}\r\n    </small>\r\n  `;\r\n\r\n  document.body.appendChild(instructionDiv);\r\n\r\n  // Auto-hide after 8 seconds\r\n  setTimeout(() => {\r\n    if (instructionDiv.parentNode) {\r\n      instructionDiv.style.opacity = '0.7';\r\n    }\r\n  }, 8000);\r\n};\r\n\r\n/**\r\n * Hide scraping instructions\r\n */\r\nconst hideScrapingInstructions = (): void => {\r\n  const existingInstruction = document.getElementById('quickadapt-scraping-instructions');\r\n  if (existingInstruction) {\r\n    existingInstruction.remove();\r\n  }\r\n};\r\n\r\n\r\n/**\r\n * Initialize click-based scraping service\r\n * This should be called when the extension is loaded\r\n */\r\nexport const initScrapingService = async (): Promise<void> => {\r\n  _isScrapingActive = false;\r\n  _scrapedData = [];\r\n  _elementMap.clear();\r\n  _lastScrapedTimestamp = '';\r\n  _clickListener = null;\r\n\r\n  // Try to restore scraped data from storage (in case of page refresh)\r\n  try {\r\n    const storedData = await loadScrapedDataFromStorage();\r\n    if (storedData && storedData.scrapedData && Array.isArray(storedData.scrapedData)) {\r\n      _scrapedData = storedData.scrapedData;\r\n      _lastScrapedTimestamp = storedData.timestamp || '';\r\n\r\n      // Rebuild the element map for duplicate detection\r\n      _scrapedData.forEach(element => {\r\n        if (element.xpath) {\r\n          _elementMap.set(element.xpath, element);\r\n        }\r\n      });\r\n\r\n      console.log(`🔄 Restored ${_scrapedData.length} scraped elements from storage after page refresh`);\r\n\r\n      // Re-highlight the previously scraped elements\r\n      _scrapedData.forEach(element => {\r\n        if (element.xpath) {\r\n          try {\r\n            const elementNode = document.evaluate(\r\n              element.xpath,\r\n              document,\r\n              null,\r\n              XPathResult.FIRST_ORDERED_NODE_TYPE,\r\n              null\r\n            ).singleNodeValue as HTMLElement;\r\n\r\n            if (elementNode) {\r\n              addPersistentHighlightWithoutBlocking(elementNode);\r\n            }\r\n          } catch (error) {\r\n            // Element might not exist anymore, that's okay\r\n          }\r\n        }\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.log('No previous scraped data found or error loading from storage');\r\n  }\r\n\r\n  // Check if we're in a Chrome extension environment\r\n  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {\r\n    // Listen for messages from background script\r\n    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {\r\n      if (message.action === 'updateScrapingState') {\r\n        _isScrapingActive = message.isActive;\r\n        sendResponse({ success: true });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'getScrapingState') {\r\n        sendResponse({\r\n          isActive: _isScrapingActive,\r\n          lastTimestamp: _lastScrapedTimestamp,\r\n          elementCount: _scrapedData.length\r\n        });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'getScrapedData') {\r\n        sendResponse({\r\n          data: _scrapedData,\r\n          timestamp: _lastScrapedTimestamp\r\n        });\r\n        return true;\r\n      }\r\n\r\n      if (message.action === 'clearScrapedData') {\r\n        clearScrapedData();\r\n        sendResponse({ success: true });\r\n        return true;\r\n      }\r\n    });\r\n  } else {\r\n  }\r\n};\r\n\r\n\r\n// Initialize the service\r\ninitScrapingService().catch(error => {\r\n  console.error('Error initializing scraping service:', error);\r\n});\r\n\r\n/**\r\n * Utility to extract only essential data from a scraped element object\r\n * Includes fallback identification mechanisms\r\n */\r\nexport function extractMinimalScrapeData(element: any): {\r\n  xpath: string,\r\n  labelName: string,\r\n  cssSelector: string,\r\n  selector: string,\r\n  value: any,\r\n  text: string,\r\n  id: string\r\n} {\r\n  return {\r\n    xpath: element.xpath || '',\r\n    labelName: element.labelName || element.text || '',\r\n    cssSelector: element.cssSelector || '',\r\n    selector: element.selector || element.xpath || element.cssSelector || '',\r\n    value: element.value,\r\n    text: element.text || '',\r\n    id: element.id || '',\r\n  };\r\n}\r\n"], "mappings": "AAAA;;AAGA,SAAQA,gBAAgB,QAAO,uBAAuB;AAGtD,OAAOC,IAAI,MAAM,uBAAuB;;AAExC;AACA,MAAMC,CAAC,GAAGD,IAAI,CAACC,CAAC,CAACC,IAAI,CAACF,IAAI,CAAC;AAG3B,IAAIG,iBAKS,GAAGC,SAAS;;AAEzB;AACA,IAAIC,iBAAiB,GAAG,KAAK;AAC7B,IAAIC,YAAmB,GAAG,EAAE;AAC5B,IAAIC,qBAA6B,GAAG,EAAE;AACtC,IAAIC,WAAqC,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD,IAAIC,cAAoD,GAAG,IAAI;AAC/D,IAAIC,oBAAsC,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD,IAAIC,gBAAkC,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;AACpD;;AAuBA;;AAiBA;AACA;AACA;AACA,OAAO,MAAME,gBAAgB,GAAGA,CAAA,KAAe;EAC7C,OAAOT,iBAAiB;AAC1B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMU,iBAAiB,GAAIC,MAAe,IAAW;EAC1DX,iBAAiB,GAAGW,MAAM;AAC5B,CAAC;;AAED;AACA;AACA;;AAEA,MAAMC,aAAa,GAAIC,OAAoB,IAAa;EACtD,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC;;EAE7C;EACA,IAAID,OAAO,KAAK,iBAAiB,EAAE;IACjC,IAAID,OAAO,CAACG,EAAE,EAAE;MACd,OAAO,YAAYH,OAAO,CAACG,EAAE,IAAI;IACnC;IAEA,MAAMC,KAAK,GAAGJ,OAAO,CAACK,aAAa,CAAC,OAAO,CAAC;IAC5C,IAAID,KAAK,IAAIA,KAAK,CAACD,EAAE,EAAE;MACrB,OAAO,YAAYC,KAAK,CAACD,EAAE,iCAAiC;IAC9D;IAEA,MAAMG,MAAM,GAAGN,OAAO,CAACO,YAAY,CAAC,SAAS,CAAC;IAC9C,IAAID,MAAM,EAAE;MACV,OAAO,+BAA+BA,MAAM,IAAI;IAClD;IAEA,MAAME,SAAS,GAAGR,OAAO,CAACQ,SAAS;IACnC,IAAIA,SAAS,EAAE;MACb,MAAMC,aAAa,GAAGD,SAAS,CAC5BE,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAACC,GAAG,IAAIA,GAAG,IAAI,CAACA,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC,IAAID,GAAG,CAACE,MAAM,GAAG,CAAC,CAAC;MAChE,IAAIL,aAAa,CAACK,MAAM,GAAG,CAAC,EAAE;QAC5B,OAAO,uCAAuCL,aAAa,CAAC,CAAC,CAAC,KAAK;MACrE;IACF;IAEA,OAAOM,uBAAuB,CAACf,OAAO,CAAC;EACzC;;EAEA;EACA,MAAMgB,iBAAiB,GAAGf,OAAO,KAAK,kBAAkB,GACpDD,OAAO,GACPA,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC;EAEvC,IAAID,iBAAiB,YAAYE,WAAW,EAAE;IAC5C,MAAMC,UAAU,GAAGH,iBAAiB,CAACb,EAAE;IACvC,MAAMC,KAAK,GAAGY,iBAAiB,CAACX,aAAa,CAAC,OAAO,CAAC;IAEtD,IAAIc,UAAU,IAAIf,KAAK,EAAE;MACvB,OAAO,2BAA2Be,UAAU,cAAc;IAC5D;IAEA,OAAOJ,uBAAuB,CAACC,iBAAiB,CAAC;EACnD;EAEA,IAAIhB,OAAO,CAACG,EAAE,EAAE;IACd,OAAO,YAAYH,OAAO,CAACG,EAAE,IAAI;EACnC;EAEA,OAAOY,uBAAuB,CAACf,OAAO,CAAC;AACzC,CAAC;AAED,MAAMe,uBAAuB,GAAIf,OAAoB,IAAa;EAChE,MAAMoB,IAAc,GAAG,EAAE;EACzB,IAAIC,OAAuB,GAAGrB,OAAO;EAErC,OAAOqB,OAAO,IAAIA,OAAO,CAACC,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;IACxD,IAAIC,QAAQ,GAAGJ,OAAO,CAACK,QAAQ,CAACxB,WAAW,CAAC,CAAC;IAE7C,IAAImB,OAAO,CAAClB,EAAE,EAAE;MACdsB,QAAQ,IAAI,SAASJ,OAAO,CAAClB,EAAE,IAAI;MACnCiB,IAAI,CAACO,OAAO,CAACF,QAAQ,CAAC;MACtB;IACF,CAAC,MAAM;MACL,IAAIG,OAAO,GAAGP,OAAO,CAACQ,sBAAsB;MAC5C,IAAIC,QAAQ,GAAG,CAAC;MAChB,OAAOF,OAAO,EAAE;QACd,IAAIA,OAAO,CAACF,QAAQ,CAACxB,WAAW,CAAC,CAAC,KAAKuB,QAAQ,EAAE;UAC/CK,QAAQ,EAAE;QACZ;QACAF,OAAO,GAAGA,OAAO,CAACC,sBAAsB;MAC1C;MAEA,IAAIC,QAAQ,GAAG,CAAC,EAAE;QAChBL,QAAQ,IAAI,IAAIK,QAAQ,GAAG;MAC7B;MAEAV,IAAI,CAACO,OAAO,CAACF,QAAQ,CAAC;IACxB;IAEAJ,OAAO,GAAGA,OAAO,CAACU,aAAa;EACjC;EAEA,OAAO,IAAI,GAAGX,IAAI,CAACY,IAAI,CAAC,GAAG,CAAC;AAC9B,CAAC;;AAMD;AACA;AACA;;AAGA,MAAMC,mBAAmB,GAAIC,EAAe,IAAa;EACvD,IAAI,CAACA,EAAE,EAAE,OAAO,EAAE;EAClB,MAAMd,IAAI,GAAG,EAAE;EACf,OAAOc,EAAE,IAAIA,EAAE,CAACZ,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;IAC9C,IAAIC,QAAQ,GAAGS,EAAE,CAACjC,OAAO,CAACC,WAAW,CAAC,CAAC;IACvC,IAAIgC,EAAE,CAAC/B,EAAE,EAAE;MACT;MACA,MAAMA,EAAE,GAAG+B,EAAE,CAAC/B,EAAE;MAChB,IAAI,QAAQ,CAACgC,IAAI,CAAChC,EAAE,CAAC,IAAI,gBAAgB,CAACgC,IAAI,CAAChC,EAAE,CAAC,EAAE;QAClD;QACAsB,QAAQ,IAAI,QAAQtB,EAAE,IAAI;MAC5B,CAAC,MAAM;QACL;QACAsB,QAAQ,IAAI,IAAItB,EAAE,EAAE;MACtB;MACAiB,IAAI,CAACO,OAAO,CAACF,QAAQ,CAAC;MACtB;IACF,CAAC,MAAM;MACL;MACA,MAAMjB,SAAS,GAAG4B,mBAAmB,CAACF,EAAE,CAAC;MACzC,IAAI1B,SAAS,EAAE;QACbiB,QAAQ,IAAI,GAAG,GAAGjB,SAAS,CAAC6B,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;MACzD;MACAlB,IAAI,CAACO,OAAO,CAACF,QAAQ,CAAC;MACtBS,EAAE,GAAGA,EAAE,CAACH,aAAc;IACxB;EACF;EACA,OAAOX,IAAI,CAACY,IAAI,CAAC,KAAK,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMI,mBAAmB,GAAIF,EAAW,IAAa;EACnD,IAAI,CAACA,EAAE,EAAE,OAAO,EAAE;;EAElB;EACA,IAAI,OAAOA,EAAE,CAAC1B,SAAS,KAAK,QAAQ,EAAE;IACpC,OAAO0B,EAAE,CAAC1B,SAAS;EACrB;;EAEA;EACA,IAAI0B,EAAE,CAAC1B,SAAS,IAAI,OAAO0B,EAAE,CAAC1B,SAAS,KAAK,QAAQ,IAAI,SAAS,IAAI0B,EAAE,CAAC1B,SAAS,EAAE;IACjF,OAAQ0B,EAAE,CAAC1B,SAAS,CAAS+B,OAAO,IAAI,EAAE;EAC5C;;EAEA;EACA,OAAOL,EAAE,CAAC3B,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;AACvC,CAAC;;AAED;AACA;AACA;AACA,MAAMiC,iBAAiB,GAAIxC,OAAoB,IAAa;EAC1D;;EAEA;EACA,IAAIA,OAAO,CAACG,EAAE,EAAE;IAAA,IAAAsC,kBAAA;IACd,MAAMC,KAAK,GAAGC,QAAQ,CAACtC,aAAa,CAAC,cAAcL,OAAO,CAACG,EAAE,IAAI,CAAC;IAClE,IAAIuC,KAAK,KAAAD,kBAAA,GAAIC,KAAK,CAACE,WAAW,cAAAH,kBAAA,eAAjBA,kBAAA,CAAmBJ,IAAI,CAAC,CAAC,EAAE;MACtC,OAAOK,KAAK,CAACE,WAAW,CAACP,IAAI,CAAC,CAAC;IACjC;EACF;;EAEA;EACA,MAAMpC,OAAO,GAAGD,OAAO,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC;EAC7C,MAAM2C,UAAU,GAAG5C,OAAO,KAAK,kBAAkB,IAC9BD,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,IACnCjB,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,KAAK,UAAU,IAC3CP,OAAO,CAACQ,SAAS,CAACsC,QAAQ,CAAC,UAAU,CAAC,IACtC9C,OAAO,CAACQ,SAAS,CAACsC,QAAQ,CAAC,QAAQ,CAAC;EAEvD,IAAID,UAAU,EAAE;IAAA,IAAAE,oBAAA;IACd;IACA,MAAMH,WAAW,IAAAG,oBAAA,GAAG/C,OAAO,CAAC4C,WAAW,cAAAG,oBAAA,uBAAnBA,oBAAA,CAAqBV,IAAI,CAAC,CAAC;IAC/C,IAAIO,WAAW,IAAIA,WAAW,CAAC9B,MAAM,GAAG,CAAC,EAAE;MACzC;MACA,MAAMkC,YAAY,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;MACvE,MAAMC,SAAS,GAAGD,YAAY,CAACE,IAAI,CAACC,IAAI,IACtCP,WAAW,CAAC1C,WAAW,CAAC,CAAC,KAAKiD,IAAI,CAACjD,WAAW,CAAC,CACjD,CAAC;MAED,IAAI,CAAC+C,SAAS,EAAE;QACd,OAAOL,WAAW,CAAC9B,MAAM,GAAG,EAAE,GAAG8B,WAAW,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGR,WAAW;MACrF;IACF;EACF;;EAEA;EACA,MAAMS,SAAS,GAAGrD,OAAO,CAACO,YAAY,CAAC,YAAY,CAAC;EACpD,IAAI8C,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEhB,IAAI,CAAC,CAAC,EAAE;IACrB;IACA,IAAIQ,UAAU,EAAE;MACd,MAAMG,YAAY,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;MACvE,MAAMC,SAAS,GAAGD,YAAY,CAACE,IAAI,CAACC,IAAI,IACtCE,SAAS,CAACnD,WAAW,CAAC,CAAC,KAAKiD,IAAI,CAACjD,WAAW,CAAC,CAC/C,CAAC;MACD,IAAI,CAAC+C,SAAS,EAAE;QACd,OAAOI,SAAS,CAAChB,IAAI,CAAC,CAAC;MACzB;IACF,CAAC,MAAM;MACL,OAAOgB,SAAS,CAAChB,IAAI,CAAC,CAAC;IACzB;EACF;;EAEA;EACA,MAAMiB,KAAK,GAAGtD,OAAO,CAACO,YAAY,CAAC,OAAO,CAAC;EAC3C,IAAI+C,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEjB,IAAI,CAAC,CAAC,EAAE;IACjB,OAAOiB,KAAK,CAACjB,IAAI,CAAC,CAAC;EACrB;;EAEA;EACA,MAAMkB,WAAW,GAAGvD,OAAO,CAACO,YAAY,CAAC,aAAa,CAAC;EACvD,IAAIgD,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAElB,IAAI,CAAC,CAAC,EAAE;IACvB,OAAOkB,WAAW,CAAClB,IAAI,CAAC,CAAC;EAC3B;;EAEA;EACA,MAAMmB,IAAI,GAAGxD,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC;EACzC,IAAIiD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEnB,IAAI,CAAC,CAAC,EAAE;IAChB,OAAOmB,IAAI,CAACnB,IAAI,CAAC,CAAC;EACpB;;EAEA;EACA,MAAMoB,SAAS,GAAGzD,OAAO,CAACO,YAAY,CAAC,YAAY,CAAC,IAAIP,OAAO,CAACO,YAAY,CAAC,WAAW,CAAC;EACzF,IAAIkD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEpB,IAAI,CAAC,CAAC,EAAE;IACrB,OAAOoB,SAAS,CAACpB,IAAI,CAAC,CAAC;EACzB;;EAEA;EACA,IAAI,CAACQ,UAAU,EAAE;IAAA,IAAAa,qBAAA;IACf,MAAMd,WAAW,IAAAc,qBAAA,GAAG1D,OAAO,CAAC4C,WAAW,cAAAc,qBAAA,uBAAnBA,qBAAA,CAAqBrB,IAAI,CAAC,CAAC;IAC/C,IAAIO,WAAW,IAAIA,WAAW,CAAC9B,MAAM,GAAG,CAAC,EAAE;MACzC,OAAO8B,WAAW,CAAC9B,MAAM,GAAG,EAAE,GAAG8B,WAAW,CAACQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGR,WAAW;IACrF;EACF;;EAEA;EACA,MAAMe,KAAK,GAAG3D,OAAO,CAACO,YAAY,CAAC,OAAO,CAAC;EAC3C,IAAIoD,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEtB,IAAI,CAAC,CAAC,EAAE;IACjB,OAAOsB,KAAK,CAACtB,IAAI,CAAC,CAAC;EACrB;;EAEA;EACA,MAAMlC,EAAE,GAAGH,OAAO,CAACG,EAAE;EACrB,MAAMK,SAAS,GAAG4B,mBAAmB,CAACpC,OAAO,CAAC;EAE9C,IAAIG,EAAE,EAAE;IACN,OAAO,GAAGF,OAAO,IAAIE,EAAE,EAAE;EAC3B,CAAC,MAAM,IAAIK,SAAS,EAAE;IACpB,MAAMoD,UAAU,GAAGpD,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1C,OAAO,GAAGT,OAAO,IAAI2D,UAAU,EAAE;EACnC;;EAEA;EACA,OAAO3D,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA,MAAM4D,qBAAqB,GAAI7D,OAAoB,IAAc;EAC/D,OACEA,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,6BAA6B,CAAC,IACzD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IAC5D/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IACzC/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,IAChD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IACjD/D,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,IAC1C,CAAC,CAACP,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,IACrC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,0BAA0B,CAAC,IAC7C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,sBAAsB,CAAC,IACzC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,eAAe,CAAC,IAClC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mBAAmB,CAAC,IACtC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,aAAa,CAAC,IAChC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,aAAa,CAAC,IAChC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,cAAc,CAAC,IACjC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,qBAAqB,CAAC,IACxC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,WAAW,CAAC,IAC9B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,WAAW,CAAC,IAC9B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,aAAa,CAAC,IAChC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,oBAAoB,CAAC,IACvC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,2BAA2B,CAAC,IAC9C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,WAAW,CAAC,IAC9B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,yBAAyB,CAAC,IAC5C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,qBAAqB,CAAC,IACxC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,yBAAyB,CAAC,IAC5C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mBAAmB,CAAC,IACtC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,8BAA8B,CAAC,IACjD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,IACrC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,cAAc,CAAC,IACjC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,qBAAqB,CAAC,IACxC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,YAAY,CAAC,IAC/B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,uBAAuB,CAAC,IAC1C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,wBAAwB,CAAC,IAC3C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,UAAU,CAAC,IAC7B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAACjB,OAAO,CAACG,EAAE,CAACU,UAAU,CAAC,WAAW,CAAC,GAAG,IAAIb,OAAO,CAACG,EAAE,EAAE,GAAG,MAAM,CAAC,IACjF,CAAC,CAACH,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,IACrC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,oBAAoB,CAAC,IACvC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mBAAmB,CAAC,IACtC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,cAAc,CAAC,IACjC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,gBAAgB,CAAC,IACnC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,oBAAoB,CAAC,IACvC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iCAAiC,CAAC,IACpD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,qBAAqB,CAAC,IACxC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,2BAA2B,CAAC,IAC9C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mCAAmC,CAAC;EAAI;EAC1D,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,8BAA8B,CAAC;EAAI;EACrD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iCAAiC,CAAC;AAGxD,CAAC;;AAED;AACA;AACA;AACA,MAAM+C,kBAAkB,GAAIhE,OAAoB,IAAc;EAC5D,OACEA,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,IAC5D/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,IACzC/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,oBAAoB,CAAC,IAChD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,sBAAsB,CAAC,IAClD/D,OAAO,CAAC8D,SAAS,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IACjD/D,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,IAC1C,CAAC,CAACP,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,0BAA0B,CAAC,IAC7C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,sBAAsB,CAAC,IACzC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,IACpC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,eAAe,CAAC,IAClC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mBAAmB,CAAC,IACtC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,aAAa,CAAC,IAChC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,cAAc,CAAC,IACjC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iCAAiC,CAAC,IACpD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAACjB,OAAO,CAACG,EAAE,CAACU,UAAU,CAAC,WAAW,CAAC,GAAG,IAAIb,OAAO,CAACG,EAAE,EAAE,GAAG,MAAM,CAAC,IACjF,CAAC,CAACH,OAAO,CAACiB,OAAO,CAAC,UAAU,CAAC,IAC7B,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,IACrC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,2BAA2B,CAAC,IAC9C,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,oBAAoB,CAAC,IACvC,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,mCAAmC,CAAC;EACrD;EACA,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,8BAA8B,CAAC;EAAG;EACrD,CAAC,CAACjB,OAAO,CAACiB,OAAO,CAAC,iCAAiC,CAAC;AAExD,CAAC;;AAED;AACA;AACA;AACA,MAAMgD,qCAAqC,GAAIjE,OAAoB,IAAW;EAC5E,IAAI6D,qBAAqB,CAAC7D,OAAO,CAAC,EAAE;;EAEpC;EACAA,OAAO,CAACkE,KAAK,CAACC,OAAO,GAAG,8BAA8B;EACtDnE,OAAO,CAACkE,KAAK,CAACE,aAAa,GAAG,KAAK;EACnCpE,OAAO,CAACqE,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;EAC3D5E,oBAAoB,CAAC6E,GAAG,CAACtE,OAAO,CAAC;;EAEjC;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMuE,sBAAsB,GAAIvE,OAAoB,IAAW;EAAA,IAAAwE,qBAAA;EAC7D,IAAIX,qBAAqB,CAAC7D,OAAO,CAAC,EAAE;;EAEpC;EACAA,OAAO,CAACkE,KAAK,CAACC,OAAO,GAAG,8BAA8B;EACtDnE,OAAO,CAACkE,KAAK,CAACE,aAAa,GAAG,KAAK;EACnCpE,OAAO,CAACqE,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;EAC3D5E,oBAAoB,CAAC6E,GAAG,CAACtE,OAAO,CAAC;;EAEjC;EACA,MAAMyE,OAAO,GAAG9B,QAAQ,CAAC+B,aAAa,CAAC,KAAK,CAAC;EAC7CD,OAAO,CAACP,KAAK,CAACS,OAAO,GAAG;AAC1B;AACA,WAAW3E,OAAO,CAAC4E,SAAS;AAC5B,YAAY5E,OAAO,CAAC6E,UAAU;AAC9B,aAAa7E,OAAO,CAAC8E,WAAW;AAChC,cAAc9E,OAAO,CAAC+E,YAAY;AAClC;AACA;AACA;AACA;AACA,GAAG;EACDN,OAAO,CAACJ,YAAY,CAAC,yBAAyB,EAAE,MAAM,CAAC;EAGvD,MAAMW,WAAW,GAAGjG,CAAC,CAAC,uCAAuC,CAAC;EAC9D0F,OAAO,CAACnB,KAAK,GAAG0B,WAAW;;EAE3B;EACA,MAAMC,IAAI,GAAGjF,OAAO,CAACkF,qBAAqB,CAAC,CAAC;EAC5C,MAAMC,UAAU,GAAG,EAAAX,qBAAA,GAAAxE,OAAO,CAACoF,YAAY,cAAAZ,qBAAA,uBAApBA,qBAAA,CAAsBU,qBAAqB,CAAC,CAAC,KAAI;IAAEG,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE;EAAE,CAAC;EAEvFb,OAAO,CAACP,KAAK,CAACmB,GAAG,GAAG,GAAGJ,IAAI,CAACI,GAAG,GAAGF,UAAU,CAACE,GAAG,GAAGE,MAAM,CAACC,OAAO,IAAI;EACrEf,OAAO,CAACP,KAAK,CAACoB,IAAI,GAAG,GAAGL,IAAI,CAACK,IAAI,GAAGH,UAAU,CAACG,IAAI,GAAGC,MAAM,CAACE,OAAO,IAAI;;EAExE;EACA,MAAMC,MAAM,GAAG1F,OAAO,CAACoF,YAAY,IAAIzC,QAAQ,CAACgD,IAAI;EACpDD,MAAM,CAACE,WAAW,CAACnB,OAAO,CAAC;EAC3B9E,gBAAgB,CAAC2E,GAAG,CAACG,OAAO,CAAC;;EAE7B;EACAA,OAAO,CAACoB,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAK;IACvCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAY;EACtC;EACAxG,oBAAoB,CAACyG,OAAO,CAAClG,OAAO,IAAI;IACtC,IAAIA,OAAO,IAAIA,OAAO,CAACkE,KAAK,EAAE;MAC5BlE,OAAO,CAACkE,KAAK,CAACC,OAAO,GAAG,EAAE;MAC1BnE,OAAO,CAACkE,KAAK,CAACE,aAAa,GAAG,EAAE;MAChCpE,OAAO,CAACmG,eAAe,CAAC,6BAA6B,CAAC;IACxD;EACF,CAAC,CAAC;EACF1G,oBAAoB,CAAC2G,KAAK,CAAC,CAAC;;EAE5B;EACAzG,gBAAgB,CAACuG,OAAO,CAACzB,OAAO,IAAI;IAClC,IAAIA,OAAO,IAAIA,OAAO,CAAC4B,UAAU,EAAE;MACjC5B,OAAO,CAAC4B,UAAU,CAACC,WAAW,CAAC7B,OAAO,CAAC;IACzC;EACF,CAAC,CAAC;EACF9E,gBAAgB,CAACyG,KAAK,CAAC,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA,MAAMG,iBAAiB,GAAIvG,OAAoB,IAAW;EACxD,IAAI;IACF;IACA,MAAMwG,QAAQ,GAAG7D,QAAQ,CAAC+B,aAAa,CAAC,KAAK,CAAC;IAC9C8B,QAAQ,CAACtC,KAAK,CAACS,OAAO,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAGD,MAAM8B,WAAW,GAAG,KAAK1H,CAAC,CAAC,SAAS,CAAC,EAAE;IACvCyH,QAAQ,CAAC5D,WAAW,GAAG6D,WAAW;;IAElC;IACA,MAAMxB,IAAI,GAAGjF,OAAO,CAACkF,qBAAqB,CAAC,CAAC;IAC5CsB,QAAQ,CAACtC,KAAK,CAACoB,IAAI,GAAG,GAAGL,IAAI,CAACK,IAAI,GAAGC,MAAM,CAACE,OAAO,IAAI;IACvDe,QAAQ,CAACtC,KAAK,CAACmB,GAAG,GAAG,GAAGJ,IAAI,CAACI,GAAG,GAAGE,MAAM,CAACC,OAAO,GAAG,EAAE,IAAI;IAE1D7C,QAAQ,CAACgD,IAAI,CAACC,WAAW,CAACY,QAAQ,CAAC;;IAEnC;IACAE,UAAU,CAAC,MAAM;MACfF,QAAQ,CAACtC,KAAK,CAACyC,OAAO,GAAG,GAAG;IAC9B,CAAC,EAAE,EAAE,CAAC;;IAEN;IACAD,UAAU,CAAC,MAAM;MACfF,QAAQ,CAACtC,KAAK,CAACyC,OAAO,GAAG,GAAG;MAC5BD,UAAU,CAAC,MAAM;QACf,IAAIF,QAAQ,CAACH,UAAU,EAAE;UACvBG,QAAQ,CAACH,UAAU,CAACC,WAAW,CAACE,QAAQ,CAAC;QAC3C;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,CAAC,OAAOI,KAAK,EAAE,CAChB;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMC,kBAAkB,GAAI7G,OAAoB,IAA+C;EAAA,IAAA8G,qBAAA;EAC7F,MAAM7B,IAAI,GAAGjF,OAAO,CAACkF,qBAAqB,CAAC,CAAC;EAC5C,MAAM6B,KAAK,GAAGhH,aAAa,CAACC,OAAO,CAAC;EACpC,MAAMgH,IAAI,GAAKC,cAAc,CAACjH,OAAO,CAAC;EACtC,MAAMkH,WAAW,GAAGjF,mBAAmB,CAACjC,OAAO,CAAC;;EAEhD;EACA,SAASmH,aAAaA,CAACjF,EAAe,EAAO;IAC3C;IACA,IAAIA,EAAE,YAAYkF,gBAAgB,IAAIlF,EAAE,YAAYmF,mBAAmB,EAAE;MACvE,OAAOnF,EAAE,CAACyB,KAAK;IACjB;IACA,IAAIzB,EAAE,YAAYoF,iBAAiB,EAAE;MAAA,IAAAC,qBAAA;MACnC,OAAO,EAAAA,qBAAA,GAAArF,EAAE,CAACsF,OAAO,CAACtF,EAAE,CAACuF,aAAa,CAAC,cAAAF,qBAAA,uBAA5BA,qBAAA,CAA8BG,IAAI,KAAIxF,EAAE,CAACyB,KAAK;IACvD;IACA;IACA;IACA,MAAMvD,KAAK,GAAG8B,EAAE,CAAC7B,aAAa,CAAC,OAAO,CAAC;IACvC,IAAID,KAAK,IAAIA,KAAK,YAAYgH,gBAAgB,EAAE;MAC9C,OAAOhH,KAAK,CAACuD,KAAK;IACpB;IACA;IACA,MAAMgE,YAAY,GAAGzF,EAAE,CAAC7B,aAAa,CAAC,oFAAoF,CAAC;IAC3H,IAAIsH,YAAY,EAAE;MAAA,IAAAC,qBAAA;MAChB,QAAAA,qBAAA,GAAOD,YAAY,CAAC/E,WAAW,cAAAgF,qBAAA,uBAAxBA,qBAAA,CAA0BvF,IAAI,CAAC,CAAC;IACzC;IACA;IACA,IAAIH,EAAE,CAAC2F,YAAY,CAAC,YAAY,CAAC,EAAE;MACjC,OAAO3F,EAAE,CAAC3B,YAAY,CAAC,YAAY,CAAC;IACtC;IACA,OAAOrB,SAAS;EAClB;;EAEA;EACA,IAAIyE,KAAK,GAAGwD,aAAa,CAACnH,OAAO,CAAC;EAClC;EACA,IAAI2D,KAAK,KAAKzE,SAAS,EAAE;IACvB,MAAM4I,UAAU,GAAG9H,OAAO,CAACK,aAAa,CAAC,yBAAyB,CAAC;IACnE,IAAIyH,UAAU,EAAE;MACdnE,KAAK,GAAGwD,aAAa,CAACW,UAAyB,CAAC;IAClD;EACF;EAEA,OAAO;IACL7H,OAAO,EAAED,OAAO,CAACC,OAAO;IACxBE,EAAE,EAAEH,OAAO,CAACG,EAAE,IAAI,EAAE;IACpBK,SAAS,EAAE4B,mBAAmB,CAACpC,OAAO,CAAC;IACvC0H,IAAI,EAAE,EAAAZ,qBAAA,GAAA9G,OAAO,CAAC4C,WAAW,cAAAkE,qBAAA,uBAAnBA,qBAAA,CAAqBzE,IAAI,CAAC,CAAC,KAAIrC,OAAO,CAACG,EAAE;IAAG;IAClD4H,SAAS,EAAEvF,iBAAiB,CAACxC,OAAO,CAAC;IAAE;IACvCgI,UAAU,EAAEC,KAAK,CAACC,IAAI,CAAClI,OAAO,CAACgI,UAAU,CAAC,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAC/DD,GAAG,CAACC,IAAI,CAAC7E,IAAI,CAAC,GAAG6E,IAAI,CAAC1E,KAAK;MAC3B,OAAOyE,GAAG;IACZ,CAAC,EAAE,CAAC,CAA2B,CAAC;IAChCrB,KAAK;IACLG,WAAW;IACXzF,QAAQ,EAAEsF,KAAK,IAAIG,WAAW;IAAE;IAChCjC,IAAI,EAAE;MACJI,GAAG,EAAEJ,IAAI,CAACI,GAAG;MACbC,IAAI,EAAEL,IAAI,CAACK,IAAI;MACfgD,KAAK,EAAErD,IAAI,CAACqD,KAAK;MACjBC,MAAM,EAAEtD,IAAI,CAACsD;IACf,CAAC;IACDC,QAAQ,EAAE,EAAE;IAAE;IACdC,SAAS,EAAExD,IAAI,CAACqD,KAAK,GAAG,CAAC,IAAIrD,IAAI,CAACsD,MAAM,GAAG,CAAC;IAC5CG,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACnCC,GAAG,EAAEtD,MAAM,CAACuD,QAAQ,CAACC,IAAI;IAAE;IAC3B/B,IAAI;IACJ,IAAIrD,KAAK,KAAKzE,SAAS,GAAG;MAAEyE;IAAM,CAAC,GAAG,CAAC,CAAC;EAC1C,CAAC;AACH,CAAC;AAGD,MAAMsD,cAAc,GAAIjH,OAAoB,IAAU;EACpD,MAAMC,OAAO,GAAGD,OAAO,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC;;EAE7C;EACA,IAAID,OAAO,KAAK,iBAAiB,IAAID,OAAO,CAACiB,OAAO,CAAC,iBAAiB,CAAC,EAAE;IACvE,OAAO,OAAO;EAChB;EAEA,IAAIhB,OAAO,KAAK,OAAO,EAAE;IACvB,MAAM+I,YAAY,GAAGhJ,OAA2B;IAChD,MAAMgH,IAAI,GAAGgC,YAAY,CAAChC,IAAI,IAAI,MAAM;;IAExC;IACA,IACEA,IAAI,KAAK,MAAM,KAEbhH,OAAO,CAACG,EAAE,CAACD,WAAW,CAAC,CAAC,CAAC4C,QAAQ,CAAC,MAAM,CAAC,IACzC9C,OAAO,CAACQ,SAAS,CAACN,WAAW,CAAC,CAAC,CAAC4C,QAAQ,CAAC,MAAM,CAAC,IAChD,CAAC9C,OAAO,CAACO,YAAY,CAAC,aAAa,CAAC,IAAI,EAAE,EAAEL,WAAW,CAAC,CAAC,CAAC4C,QAAQ,CAAC,MAAM,CAAC,CAC3E,EACD;MACA;MACA,MAAMa,KAAK,GAAGqF,YAAY,CAACrF,KAAK,IAAI,EAAE;MACtC,MAAMJ,WAAW,GAAGvD,OAAO,CAACO,YAAY,CAAC,aAAa,CAAC,IAAI,EAAE;MAC7D,MAAMJ,EAAE,GAAGH,OAAO,CAACG,EAAE,CAACD,WAAW,CAAC,CAAC;MACnC,MAAMM,SAAS,GAAGR,OAAO,CAACQ,SAAS,CAACN,WAAW,CAAC,CAAC;MACjD,MAAMsD,IAAI,GAAG,CAACxD,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,EAAEL,WAAW,CAAC,CAAC;;MAE/D;MACA,MAAM+I,gBAAgB,GAAG,uFAAuF;MAChH,MAAMC,iBAAiB,GAAGD,gBAAgB,CAAC9G,IAAI,CAACwB,KAAK,CAAC;;MAEtD;MACA,MAAMwF,kBAAkB,GAAG,CACzB,WAAW,EAAE,YAAY,EAAE,YAAY,EACvC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAC,OAAO,CACrC;MACD,MAAMC,qBAAqB,GAAGD,kBAAkB,CAACjG,IAAI,CAACC,IAAI,IACxDhD,EAAE,CAAC2C,QAAQ,CAACK,IAAI,CAAC,IAAI3C,SAAS,CAACsC,QAAQ,CAACK,IAAI,CAAC,IAAIK,IAAI,CAACV,QAAQ,CAACK,IAAI,CACrE,CAAC;;MAED;MACA,MAAMkG,eAAe,GAAG,CACtB,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAC5C,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAC9C,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CACnD;MAED,MAAMC,yBAAyB,GAAGD,eAAe,CAACnG,IAAI,CAACqG,SAAS,IAC9DpJ,EAAE,CAAC2C,QAAQ,CAACyG,SAAS,CAAC,IAAI/I,SAAS,CAACsC,QAAQ,CAACyG,SAAS,CAAC,IAAI/F,IAAI,CAACV,QAAQ,CAACyG,SAAS,CACpF,CAAC;;MAED;MACA,MAAMC,qBAAqB,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/E,MAAMC,4BAA4B,GAAGD,qBAAqB,CAACtG,IAAI,CAACC,IAAI,IAClEI,WAAW,CAACrD,WAAW,CAAC,CAAC,CAAC4C,QAAQ,CAACK,IAAI,CACzC,CAAC;;MAED;MACA,IAAI+F,iBAAiB,IAAIE,qBAAqB,IAAIE,yBAAyB,IAAIG,4BAA4B,EAAE;QAC3G,OAAO,WAAW;MACpB,CAAC,MAAM;QACL,OAAO,WAAW;MACpB;IACF;;IAEA;IACA,IAAIzJ,OAAO,CAACiB,OAAO,CAAC,oEAAoE,CAAC,EAAE;MACzF,OAAO,UAAU;IACnB;IAEA,OAAO+F,IAAI;EACb;;EAEA;EACA,IAAI/G,OAAO,KAAK,kBAAkB,IAAID,OAAO,CAACiB,OAAO,CAAC,kBAAkB,CAAC,EAAE;IACzE,OAAO,UAAU;EACnB;;EAEA;EACA,IAAIhB,OAAO,KAAK,QAAQ,EAAE;IACxB,MAAMyJ,aAAa,GAAG1J,OAA4B;IAClD,OAAO0J,aAAa,CAACC,QAAQ,GAAG,aAAa,GAAG,UAAU;EAC5D;;EAEA;EACA,IAAI1J,OAAO,KAAK,UAAU,EAAE;IAC1B,OAAO,UAAU;EACnB;;EAEA;EACA,IAAID,OAAO,CAAC4J,eAAe,KAAK,MAAM,EAAE;IACtC,OAAO,iBAAiB;EAC1B;;EAEA;EACA,IACE5J,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,KAAK,UAAU,IAC3CP,OAAO,CAACO,YAAY,CAAC,MAAM,CAAC,KAAK,SAAS,IAC1CP,OAAO,CAACQ,SAAS,CAACsC,QAAQ,CAAC,UAAU,CAAC,IACtC9C,OAAO,CAACQ,SAAS,CAACsC,QAAQ,CAAC,QAAQ,CAAC,EACpC;IACA,OAAO,UAAU;EACnB;;EAEA;EACA,OAAO,OAAO;AAChB,CAAC;;AAMD;AACA;AACA;;AAEA,MAAM+G,kBAAkB,GAAG,MAAAA,CAAQC,KAAiB,EAACC,SAKpD,KAAoB;EACnB,IAAI;IACF;IACA;IACA;;IAEA,MAAMC,MAAM,GAAGF,KAAK,CAACE,MAAqB;IAC1C,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAAC1I,QAAQ,IAAI0I,MAAM,CAAC1I,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;MACxE;IACF;IAEA,IAAIwC,kBAAkB,CAACgG,MAAM,CAAC,EAAE;MAC9B;IACF;IAEA,IAAIA,MAAM,CAACnC,YAAY,CAAC,6BAA6B,CAAC,EAAE;MACtD;IACF;;IAGA;IACA,MAAMoC,kBAAkB,GAAGpD,kBAAkB,CAACmD,MAAM,CAAC;;IAErD;IACA,MAAME,eAAe,GAAG,CAACD,kBAAkB,CAAC;;IAE5C;IACAE,OAAO,CAACC,GAAG,CAAC,oCAAoCJ,MAAM,CAAC/J,OAAO,gBAAgBgK,kBAAkB,CAAClD,KAAK,EAAE,CAAC;IACzGsD,cAAc,CAAC;MAAEC,QAAQ,EAAEJ;IAAgB,CAAC,EAAE,IAAI,CAAC;;IAEnD;;IAEA,MAAMK,wBAAwB,CAACR,SAAS,CAAC;IACzCI,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;;IAEjE;IACAnG,qCAAqC,CAAC+F,MAAM,CAAC;;IAG7C;IACA;EAGF,CAAC,CAAC,OAAOpD,KAAK,EAAE;IACduD,OAAO,CAACvD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;EACtD;AACF,CAAC;AAED,OAAO,MAAMyD,cAAc,GAAGA,CAACG,IAAS,EAAEC,MAAe,GAAG,KAAK,KAAW;EAC1E,MAAM/B,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC1CvJ,qBAAqB,GAAGqJ,SAAS;EAEjC,IAAI,CAAC+B,MAAM,EAAE;IACX;IACArL,YAAY,GAAG,EAAE;IACjBE,WAAW,CAAC8G,KAAK,CAAC,CAAC;EACrB;;EAEA;EACA,IAAIoE,IAAI,IAAIA,IAAI,CAACF,QAAQ,IAAIrC,KAAK,CAACyC,OAAO,CAACF,IAAI,CAACF,QAAQ,CAAC,EAAE;IACzDE,IAAI,CAACF,QAAQ,CAACpE,OAAO,CAAElG,OAAoB,IAAK;MAC9C;MACAA,OAAO,CAAC0I,SAAS,GAAGA,SAAS;;MAE7B;MACA,IAAI1I,OAAO,CAAC+G,KAAK,EAAE;QACjB;QACA,IAAIzH,WAAW,CAACqL,GAAG,CAAC3K,OAAO,CAAC+G,KAAK,CAAC,EAAE;UAClCoD,OAAO,CAACC,GAAG,CAAC,6CAA6CpK,OAAO,CAAC+G,KAAK,EAAE,CAAC;UACzE,OAAO,CAAC;QACV,CAAC,MAAM;UACL;UACAoD,OAAO,CAACC,GAAG,CAAC,oCAAoCpK,OAAO,CAAC+G,KAAK,EAAE,CAAC;UAChEzH,WAAW,CAACsL,GAAG,CAAC5K,OAAO,CAAC+G,KAAK,EAAE/G,OAAO,CAAC;UACvCZ,YAAY,CAACyL,IAAI,CAAC7K,OAAO,CAAC;QAE5B;MACF,CAAC,MAAM;QACL;QACA,MAAM8K,WAAW,GAAG1L,YAAY,CAAC8D,IAAI,CAAC6H,QAAQ,IAC5CA,QAAQ,CAAC9K,OAAO,KAAKD,OAAO,CAACC,OAAO,IACpC8K,QAAQ,CAAC5K,EAAE,KAAKH,OAAO,CAACG,EAAE,IAC1B4K,QAAQ,CAACvK,SAAS,KAAKR,OAAO,CAACQ,SACjC,CAAC;QAED,IAAI,CAACsK,WAAW,EAAE;UAChB1L,YAAY,CAACyL,IAAI,CAAC7K,OAAO,CAAC;QAE5B,CAAC,MAAM,CAEP;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMgL,cAAc,GAAGA,CAAA,KAAa;EACzC,OAAO5L,YAAY;AACrB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM6L,iBAAiB,GAAIlE,KAAa,IAA8B;EAC3E,OAAOzH,WAAW,CAAC4L,GAAG,CAACnE,KAAK,CAAC;AAC/B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA,OAAO,MAAMoE,YAAY,GAAGA,CAAA,KAA4H;EACtJ,OAAO/L,YAAY,CAACgM,GAAG,CAACpL,OAAO,KAAK;IAClC+G,KAAK,EAAE/G,OAAO,CAAC+G,KAAK;IACpB9G,OAAO,EAAED,OAAO,CAACC,OAAO;IACxBE,EAAE,EAAEH,OAAO,CAACG,EAAE;IACdK,SAAS,EAAER,OAAO,CAACQ,SAAS;IAC5BkH,IAAI,EAAE1H,OAAO,CAAC0H,IAAI;IAClBgB,SAAS,EAAE1I,OAAO,CAAC0I,SAAS;IAC5BG,GAAG,EAAE7I,OAAO,CAAC6I;EACf,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMwC,uBAAuB,GAAG,MAAOC,SAAkB,IAAoB;EAClF,IAAI;IACF,IAAIlM,YAAY,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAC7B;MACA,MAAMyK,UAAU,GAAG,MAAMC,0BAA0B,CAAC,CAAC;MACrD,IAAI,CAACD,UAAU,IAAI,CAACA,UAAU,CAACE,WAAW,IAAIF,UAAU,CAACE,WAAW,CAAC3K,MAAM,KAAK,CAAC,EAAE;QACjF;QACA;MACF;MACA;MACA,MAAM4K,qBAAqB,CAACJ,SAAS,CAAC;IACxC,CAAC,MAAM;MACL;MACA,MAAMf,wBAAwB,CAAC,CAAC;MAChC,MAAMmB,qBAAqB,CAACJ,SAAS,CAAC;IACxC;EACF,CAAC,CAAC,OAAO1E,KAAK,EAAE;IACd;EAAA;AAEJ,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM+E,mBAAmB,GAAGA,CAAA,KAAc;EAC/C,OAAOvM,YAAY,CAAC0B,MAAM;AAC5B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM8K,uBAAuB,GAAG,MAAAA,CAAA,KAA8B;EACnE,IAAI;IACF,MAAML,UAAU,GAAG,MAAMC,0BAA0B,CAAC,CAAC;IACrD,OAAOD,UAAU,IAAIA,UAAU,CAACE,WAAW,IAAIxD,KAAK,CAACyC,OAAO,CAACa,UAAU,CAACE,WAAW,CAAC,IAAIF,UAAU,CAACE,WAAW,CAAC3K,MAAM,GAAG,CAAC;EAC3H,CAAC,CAAC,OAAO8F,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMiF,mBAAmB,GAAG,MAAAA,CAAA,KAA2B;EAC5D1B,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;EAC9D0B,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACpB,MAAMC,2BAA2B,CAAC,CAAC,CAAC,CAAC;EACrC9F,mBAAmB,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM+F,iBAAiB,GAAGA,CAAA,KAM5B;EACH,OAAO;IACLC,QAAQ,EAAE9M,iBAAiB;IAC3B+M,YAAY,EAAE9M,YAAY,CAAC0B,MAAM;IACjCqL,cAAc,EAAE7M,WAAW,CAAC8M,IAAI;IAChCC,aAAa,EAAEhN,qBAAqB;IACpCiN,wBAAwB,EAAE7M,oBAAoB,CAAC2M;EACjD,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMG,uBAAuB,GAAGA,CAAA,KAAc;EACnD,OAAOlN,qBAAqB;AAC9B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMyM,gBAAgB,GAAGA,CAAA,KAAY;EAC1C3B,OAAO,CAACC,GAAG,CAAC,kCAAkChL,YAAY,CAAC0B,MAAM,iBAAiBxB,WAAW,CAAC8M,IAAI,iBAAiB,CAAC;EACpHhN,YAAY,GAAG,EAAE;EACjBC,qBAAqB,GAAG,EAAE;EAC1BC,WAAW,CAAC8G,KAAK,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM2F,2BAA2B,GAAG,MAAAA,CAAA,KAA2B;EAEpE,IAAI;IACIS,YAAY,CAACC,UAAU,CAAC,yBAAyB,CAAC;EAE1D,CAAC,CAAC,OAAO7F,KAAK,EAAE;IACduD,OAAO,CAACvD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;EACnE;AACF,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAM2D,wBAAwB,GAAG,MAAOR,SAK9C,IAAoB;EACnB,IAAI;IACA,MAAM2C,WAAW,GAAG;MAClBjB,WAAW,EAAErM,YAAY;MACzBsJ,SAAS,EAAErJ,qBAAqB;MAChCwJ,GAAG,EAAEtD,MAAM,CAACuD,QAAQ,CAACC,IAAI;MACzBzF,KAAK,EAAEX,QAAQ,CAACW,KAAK;MACrB4I,YAAY,EAAE9M,YAAY,CAAC0B,MAAM;MACjC6L,SAAS,EAAEvN,YAAY,CAACgM,GAAG,CAACpL,OAAO,KAAK;QACtC+G,KAAK,EAAE/G,OAAO,CAAC+G,KAAK;QACpB9G,OAAO,EAAED,OAAO,CAACC,OAAO;QACxBE,EAAE,EAAEH,OAAO,CAACG,EAAE;QACdK,SAAS,EAAER,OAAO,CAACQ,SAAS;QAC5BkH,IAAI,EAAE1H,OAAO,CAAC0H,IAAI;QAClBK,SAAS,EAAE/H,OAAO,CAAC+H,SAAS;QAC5Bb,WAAW,EAAElH,OAAO,CAACkH,WAAW;QAChCzF,QAAQ,EAAEzB,OAAO,CAACyB,QAAQ;QAC1BuG,UAAU,EAAEhI,OAAO,CAACgI,UAAU;QAC9BU,SAAS,EAAE1I,OAAO,CAAC0I,SAAS;QAC5BG,GAAG,EAAE7I,OAAO,CAAC6I;MAEf,CAAC,CAAC,CAAC;MACH;MACA,IAAIkB,SAAS,IAAI;QACfA,SAAS,EAAE;UACT6C,SAAS,EAAE7C,SAAS,CAACuB,SAAS;UAC9BuB,WAAW,EAAE9C,SAAS,CAAC+C,gBAAgB;UACvCC,IAAI,EAAEhD,SAAS,CAACiD,SAAS;UACzBnE,GAAG,EAAEkB,SAAS,CAACkD;QACjB;MACF,CAAC;IACH,CAAC;IACHT,YAAY,CAACU,OAAO,CAAC,yBAAyB,EAAEC,IAAI,CAACC,SAAS,CAACV,WAAW,CAAC,CAAC;IAC7EA,WAAW,CAACC,SAAS,CAACzG,OAAO,CAAC,CAACmH,IAAI,EAAEC,KAAK,KAAK;MAC1CnD,OAAO,CAACC,GAAG,CAAC,GAAGkD,KAAK,GAAG,CAAC,KAAKD,IAAI,CAACpN,OAAO,MAAMoN,IAAI,CAACtG,KAAK,EAAE,CAAC;IAC9D,CAAC,CAAC;EAEN,CAAC,CAAC,OAAOH,KAAK,EAAE,CAChB;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM4E,0BAA0B,GAAG,MAAAA,CAAA,KAA0B;EAClE,IAAI;IAEA,MAAMhB,IAAI,GAAGgC,YAAY,CAACe,OAAO,CAAC,yBAAyB,CAAC;IAC5D,OAAO/C,IAAI,GAAG2C,IAAI,CAACK,KAAK,CAAChD,IAAI,CAAC,GAAG,IAAI;EAEzC,CAAC,CAAC,OAAO5D,KAAK,EAAE;IACd,OAAO,IAAI;EACb;AACF,CAAC;;AAID;AACA;AACA;AACA,OAAO,MAAM8E,qBAAqB,GAAG,MAAOJ,SAAkB,IAAoB;EAChF,IAAI;IAEF,MAAMC,UAAU,GAAG,MAAMC,0BAA0B,CAAC,CAAC;IAErD,IAAI,CAACD,UAAU,EAAE;MACf;IACF;IAEA,MAAMkC,OAAO,GAAG;MACdC,QAAQ,EAAE;QACR7E,GAAG,EAAE0C,UAAU,CAAC1C,GAAG,IAAItD,MAAM,CAACuD,QAAQ,CAACC,IAAI;QAC3CzF,KAAK,EAAEiI,UAAU,CAACjI,KAAK,IAAIX,QAAQ,CAACW,KAAK;QACzCoF,SAAS,EAAE6C,UAAU,CAAC7C,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC3DsD,YAAY,EAAEX,UAAU,CAACW,YAAY,IAAI,CAAC;QAC1CyB,UAAU,EAAE,IAAIhF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC;MACD0B,QAAQ,EAAEiB,UAAU,CAACE,WAAW,IAAI,EAAE;MACtCkB,SAAS,EAAEpB,UAAU,CAACoB,SAAS,IAAI;IACrC,CAAC;;IAGD;IACA,MAAMiB,gBAAgB,CAACH,OAAO,EAAEnC,SAAS,CAAC;EAE5C,CAAC,CAAC,OAAO1E,KAAK,EAAE,CAChB;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMgH,gBAAgB,GAAG,MAAAA,CAAOpD,IAAS,EAAEc,SAAkB,KAAoB;EACtF,IAAI;IAEF;IACA,MAAMuC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;IAE/B;IACA,MAAMC,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACb,IAAI,CAACC,SAAS,CAAC5C,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;MACzDxD,IAAI,EAAE;IACR,CAAC,CAAC;;IAEF;IACA,MAAM0B,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACtG,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;IAChE,MAAM2L,SAAS,GAAG,CAACzD,IAAI,CAACkD,QAAQ,CAACpK,KAAK,IAAI,cAAc,EAAEhB,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;IACvF,MAAM4L,QAAQ,GAAG,yBAAyBD,SAAS,IAAIvF,SAAS,OAAO;;IAEvE;IACAmF,QAAQ,CAACpD,MAAM,CAAC,SAAS,EAAEsD,QAAQ,EAAEG,QAAQ,CAAC,CAAC,CAAC;;IAEhD;IACAL,QAAQ,CAACpD,MAAM,CAAC,cAAc,EAAED,IAAI,CAACkD,QAAQ,CAACxB,YAAY,CAACiC,QAAQ,CAAC,CAAC,CAAC;IACtEN,QAAQ,CAACpD,MAAM,CAAC,KAAK,EAAED,IAAI,CAACkD,QAAQ,CAAC7E,GAAG,CAAC;IACzCgF,QAAQ,CAACpD,MAAM,CAAC,WAAW,EAAED,IAAI,CAACkD,QAAQ,CAAChF,SAAS,CAAC;;IAIrD;IACA,MAAM;MAAE0F;IAAiB,CAAC,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC;IAE1D,IAAI,CAAC9C,SAAS,EAAE;MACd,MAAM,IAAI+C,KAAK,CAAC,6CAA6C,CAAC;IAChE;IAEA,MAAMC,QAAQ,GAAG,MAAMF,gBAAgB,CAAC9C,SAAS,EAAEuC,QAAQ,CAAC;EAE9D,CAAC,CAAC,OAAOjH,KAAK,EAAE;IAEd;IACA,MAAM2H,YAAY,GAAG3H,KAAK,YAAYyH,KAAK,GAAGzH,KAAK,CAAC4H,OAAO,GAAG,wBAAwB;IAEtF,MAAM5H,KAAK,CAAC,CAAC;EACf;AACF,CAAC;;AAED;AACA;AACA;;AAEA,OAAO,MAAM6H,kBAAkB,GAAG,MAAAA,CAAOzB,SAAiB,EAAEF,gBAAwB,EAAExB,SAAiB,EAAE2B,QAAgB,KAAoB;EAC3I,IAAI9N,iBAAiB,EAAE;EAEvBA,iBAAiB,GAAG,IAAI;EACxBgL,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;;EAE1E;EACA,MAAMmB,UAAU,GAAG,MAAMC,0BAA0B,CAAC,CAAC;EACrD,IAAI,CAACD,UAAU,IAAI,CAACA,UAAU,CAACE,WAAW,IAAI,CAACxD,KAAK,CAACyC,OAAO,CAACa,UAAU,CAACE,WAAW,CAAC,IAAIF,UAAU,CAACE,WAAW,CAAC3K,MAAM,KAAK,CAAC,EAAE;IAC3HqJ,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;IAC3E0B,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACpB,MAAMvB,wBAAwB,CAAC;MAC7Be,SAAS;MACTwB,gBAAgB;MAChBE,SAAS;MACTC;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IAEL9C,OAAO,CAACC,GAAG,CAAC,kCAAkCmB,UAAU,CAACE,WAAW,CAAC3K,MAAM,yBAAyB1B,YAAY,CAAC0B,MAAM,YAAY,CAAC;EACtI;EAEAqJ,OAAO,CAACC,GAAG,CAAC,sCAAsChL,YAAY,CAAC0B,MAAM,EAAE,CAAC;EACzE7B,iBAAiB,GAAG;IACjBqM,SAAS;IACTwB,gBAAgB;IAChBE,SAAS;IACTC;EACF,CAAC;EACD;EACA7N,YAAY,CAAC8G,OAAO,CAAClG,OAAO,IAAI;IAC9B,IAAIA,OAAO,CAAC+G,KAAK,EAAE;MACjB,IAAI;QACF,MAAM2H,WAAW,GAAG/L,QAAQ,CAACgM,QAAQ,CACnC3O,OAAO,CAAC+G,KAAK,EACbpE,QAAQ,EACR,IAAI,EACJiM,WAAW,CAACC,uBAAuB,EACnC,IACF,CAAC,CAACC,eAA8B;QAEhC,IAAIJ,WAAW,EAAE;UACfzK,qCAAqC,CAACyK,WAAW,CAAC;QACpD;MACF,CAAC,CAAC,OAAO9H,KAAK,EAAE;QACd;MAAA;IAEJ;EACF,CAAC,CAAC;;EAEF;EACA,IAAI,CAACpH,cAAc,EAAE;IACnBA,cAAc,GAAIsK,KAAiB,IAAK;MACtC;;MAEAD,kBAAkB,CAACC,KAAK,EAAC7K,iBAAiB,CAAC,CAAC8P,KAAK,CAACnI,KAAK,IAAI;QACzDuD,OAAO,CAACvD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,CAAC;IACJ,CAAC;IACDjE,QAAQ,CAACkD,gBAAgB,CAAC,OAAO,EAAErG,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5D;;EAEA;EACA,IAAI,OAAOwP,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;IACnD,IAAI;MACFD,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC;QACzBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvI,KAAK,EAAE;MACd;MACArB,MAAM,CAAC6J,aAAa,CAAC,IAAIC,WAAW,CAAC,iCAAiC,CAAC,CAAC;IAC1E;EACF;;EAGA;EACAC,wBAAwB,CAAC,CAAC;AAC5B,CAAC;AAGD,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAY;EACvC,IAAIpQ,iBAAiB,EAAE;EAEvBA,iBAAiB,GAAG,IAAI;EACxB2M,gBAAgB,CAAC,CAAC;;EAGlB;EACA,IAAI,CAACtM,cAAc,EAAE;IACnBA,cAAc,GAAGqK,kBAAkB;IACnClH,QAAQ,CAACkD,gBAAgB,CAAC,OAAO,EAAErG,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5D;;EAEA;EACA,IAAI,OAAOwP,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;IACnD,IAAI;MACFD,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC;QACzBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvI,KAAK,EAAE;MACd;MACArB,MAAM,CAAC6J,aAAa,CAAC,IAAIC,WAAW,CAAC,iCAAiC,CAAC,CAAC;IAC1E;EACF;;EAEA;EACAC,wBAAwB,CAAC,CAAC;AAC5B,CAAC;;AAOD;AACA;AACA;AACA,OAAO,MAAME,YAAY,GAAG,MAAAA,CAC1BC,eAAwB,EACxBnE,SAAiB,EACjB0B,SAAkB,EAClBF,gBAAyB,EACzBG,QAAiB,KAEC;EAClB,IAAI,CAAC9N,iBAAiB,EAAE;EACxBA,iBAAiB,GAAG,KAAK;;EAGzB;EACA,IAAIK,cAAc,EAAE;IAClBmD,QAAQ,CAAC+M,mBAAmB,CAAC,OAAO,EAAElQ,cAAc,EAAE,IAAI,CAAC;IAC3DA,cAAc,GAAG,IAAI;EACvB;;EAEA;EACA,IAAIJ,YAAY,CAAC0B,MAAM,GAAG,CAAC,EAAE;IAG3B;IACC,MAAMyJ,wBAAwB,CAAC;MAC9Be,SAAS;MACTwB,gBAAgB;MACfE,SAAS;MACVC;IACF,CAAC,CAAC;;IAIF;IACA,CAACwC,eAAe,KAAI,MAAM/D,qBAAqB,CAACJ,SAAS,CAAC;IAC3D,MAAMqE,YAAY,GAAG,MAAMC,sBAAsB,CAAC,CAAC;IACnDzF,OAAO,CAACC,GAAG,CAACuF,YAAY,EAAC,cAAc,CAAC;IAEvC,MAAME,KAAK,GAAG;MACZjD,SAAS,EAACtB,SAAS;MACnBuB,WAAW,EAACC,gBAAgB;MAC5BC,IAAI,EAACC,SAAS;MACd8C,cAAc,EAACH,YAAY;MAC3B9G,GAAG,EAAEtD,MAAM,CAACuD,QAAQ,CAACC;IACvB,CAAC;IACD,IAAI0G,eAAe,IAAIzC,SAAS,IAAIF,gBAAgB,EAAE;MACpD,MAAMjO,gBAAgB,CAACgR,KAAK,CAAC;IAC/B;EACF;EAEG,MAAM9D,2BAA2B,CAAC,CAAC;;EAGtC;EACA9F,mBAAmB,CAAC,CAAC;;EAErB;EACA,IAAI,OAAO+I,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;IACnD,IAAI;MACFD,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC;QACzBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvI,KAAK,EAAE;MACd;MACArB,MAAM,CAAC6J,aAAa,CAAC,IAAIC,WAAW,CAAC,gCAAgC,CAAC,CAAC;IACzE;EACF;;EAEA;EACAU,wBAAwB,CAAC,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAA2B;EACvD,IAAI,CAAC7Q,iBAAiB,EAAE;EACxBA,iBAAiB,GAAG,KAAK;;EAEzB;EACA,IAAIK,cAAc,EAAE;IAClBmD,QAAQ,CAAC+M,mBAAmB,CAAC,OAAO,EAAElQ,cAAc,EAAE,IAAI,CAAC;IAC3DA,cAAc,GAAG,IAAI;EACvB;;EAEA;EACA,MAAMuM,2BAA2B,CAAC,CAAC;EACnC5B,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;;EAEzE;EACA0B,gBAAgB,CAAC,CAAC;;EAElB;EACA7F,mBAAmB,CAAC,CAAC;;EAErB;EACA,IAAI,OAAO+I,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;IACnD,IAAI;MACFD,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC;QACzBC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOvI,KAAK,EAAE;MACd;MACArB,MAAM,CAAC6J,aAAa,CAAC,IAAIC,WAAW,CAAC,gCAAgC,CAAC,CAAC;IACzE;EACF;;EAEA;EACAU,wBAAwB,CAAC,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMH,sBAAsB,GAAG,MAAAA,CAAA,KAAuC;EAC3E,MAAMrE,UAAU,GAAG,MAAMC,0BAA0B,CAAC,CAAC;EACrD,IAAI,CAACD,UAAU,IAAI,CAACtD,KAAK,CAACyC,OAAO,CAACa,UAAU,CAACE,WAAW,CAAC,EAAE,OAAO,EAAE;EAEpE,OAAOF,UAAU,CAACE,WAAW,CAACL,GAAG,CAAEiC,IAAS,IAAK;IAC/C;IACA,IAAI4C,eAAe,GAAG5C,IAAI,CAACtG,KAAK,IAAI,EAAE;IACtC,IAAImJ,gBAAgB,GAAG7C,IAAI,CAACnG,WAAW,IAAI,EAAE;;IAE7C;IACA,IAAI,CAAC+I,eAAe,IAAI5C,IAAI,CAACtF,SAAS,EAAE;MACtCkI,eAAe,GAAG,gBAAgB5C,IAAI,CAACtF,SAAS,IAAI,CAAC,CAAC;MACtD;MACA,IAAI,CAACkI,eAAe,EAAE;QACpBA,eAAe,GAAG,WAAW5C,IAAI,CAACtF,SAAS,IAAI,CAAC,CAAC;MACnD;MACA,IAAI,CAACkI,eAAe,EAAE;QACpBA,eAAe,GAAG,iBAAiB5C,IAAI,CAACtF,SAAS,IAAI,CAAC,CAAC;MACzD;MACA,IAAI,CAACkI,eAAe,EAAE;QACpBA,eAAe,GAAG,UAAU5C,IAAI,CAACtF,SAAS,IAAI,CAAC,CAAC;MAClD;IACF;;IAEA;IACA,IAAI,CAACkI,eAAe,EAAE;MACpBA,eAAe,GAAGC,gBAAgB;IACpC;IAEA,OAAO;MACLnD,IAAI,EAACM,IAAI,CAAC3F,IAAI,IAAI,EAAE;MACpBX,KAAK,EAAEsG,IAAI,CAACtG,KAAK,IAAI,EAAE;MACvBgB,SAAS,EAAEsF,IAAI,CAACtF,SAAS,IAAK,EAAE;MAChCtG,QAAQ,EAAEwO,eAAe;MACzB/I,WAAW,EAAEmG,IAAI,CAACnG,WAAW,IAAI,EAAE;MACnCvD,KAAK,EAAE0J,IAAI,CAAC1J,KAAK,IAAI,EAAE;MACvBqD,IAAI,EAAEqG,IAAI,CAACrG,IAAI,IAAI;IACrB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA,MAAMsI,wBAAwB,GAAGA,CAAA,KAAY;EAC3C;EACAS,wBAAwB,CAAC,CAAC;EAE1B,MAAMI,cAAc,GAAGxN,QAAQ,CAAC+B,aAAa,CAAC,KAAK,CAAC;EACpDyL,cAAc,CAAChQ,EAAE,GAAG,kCAAkC;EACtDgQ,cAAc,CAACjM,KAAK,CAACS,OAAO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;EAED;EACA,MAAMyL,SAAS,GAAG,MAAMrR,CAAC,CAAC,uBAAuB,CAAC,EAAE;EACpD,MAAMsR,YAAY,GAAG,KAAKtR,CAAC,CAAC,4CAA4C,CAAC,EAAE;EAC3E,MAAMuR,WAAW,GAAG,KAAKvR,CAAC,CAAC,mDAAmD,CAAC,EAAE;EACjF,MAAMwR,aAAa,GAAG,KAAKxR,CAAC,CAAC,0CAA0C,CAAC,EAAE;EAC1E,MAAMyR,UAAU,GAAG,KAAKzR,CAAC,CAAC,mCAAmC,CAAC,EAAE;EAChE,MAAM0R,SAAS,GAAG,KAAK1R,CAAC,CAAC,iCAAiC,CAAC,EAAE;EAE7DoR,cAAc,CAACO,SAAS,GAAG;AAC7B;AACA,MAAMN,SAAS;AACf;AACA,QAAQC,YAAY;AACpB,QAAQC,WAAW;AACnB,QAAQC,aAAa;AACrB,QAAQC,UAAU;AAClB,QAAQC,SAAS;AACjB;AACA,GAAG;EAED9N,QAAQ,CAACgD,IAAI,CAACC,WAAW,CAACuK,cAAc,CAAC;;EAEzC;EACAzJ,UAAU,CAAC,MAAM;IACf,IAAIyJ,cAAc,CAAC9J,UAAU,EAAE;MAC7B8J,cAAc,CAACjM,KAAK,CAACyC,OAAO,GAAG,KAAK;IACtC;EACF,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA,MAAMoJ,wBAAwB,GAAGA,CAAA,KAAY;EAC3C,MAAMY,mBAAmB,GAAGhO,QAAQ,CAACiO,cAAc,CAAC,kCAAkC,CAAC;EACvF,IAAID,mBAAmB,EAAE;IACvBA,mBAAmB,CAACE,MAAM,CAAC,CAAC;EAC9B;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAA2B;EAC5D3R,iBAAiB,GAAG,KAAK;EACzBC,YAAY,GAAG,EAAE;EACjBE,WAAW,CAAC8G,KAAK,CAAC,CAAC;EACnB/G,qBAAqB,GAAG,EAAE;EAC1BG,cAAc,GAAG,IAAI;;EAErB;EACA,IAAI;IACF,MAAM+L,UAAU,GAAG,MAAMC,0BAA0B,CAAC,CAAC;IACrD,IAAID,UAAU,IAAIA,UAAU,CAACE,WAAW,IAAIxD,KAAK,CAACyC,OAAO,CAACa,UAAU,CAACE,WAAW,CAAC,EAAE;MACjFrM,YAAY,GAAGmM,UAAU,CAACE,WAAW;MACrCpM,qBAAqB,GAAGkM,UAAU,CAAC7C,SAAS,IAAI,EAAE;;MAElD;MACAtJ,YAAY,CAAC8G,OAAO,CAAClG,OAAO,IAAI;QAC9B,IAAIA,OAAO,CAAC+G,KAAK,EAAE;UACjBzH,WAAW,CAACsL,GAAG,CAAC5K,OAAO,CAAC+G,KAAK,EAAE/G,OAAO,CAAC;QACzC;MACF,CAAC,CAAC;MAEFmK,OAAO,CAACC,GAAG,CAAC,eAAehL,YAAY,CAAC0B,MAAM,mDAAmD,CAAC;;MAElG;MACA1B,YAAY,CAAC8G,OAAO,CAAClG,OAAO,IAAI;QAC9B,IAAIA,OAAO,CAAC+G,KAAK,EAAE;UACjB,IAAI;YACF,MAAM2H,WAAW,GAAG/L,QAAQ,CAACgM,QAAQ,CACnC3O,OAAO,CAAC+G,KAAK,EACbpE,QAAQ,EACR,IAAI,EACJiM,WAAW,CAACC,uBAAuB,EACnC,IACF,CAAC,CAACC,eAA8B;YAEhC,IAAIJ,WAAW,EAAE;cACfzK,qCAAqC,CAACyK,WAAW,CAAC;YACpD;UACF,CAAC,CAAC,OAAO9H,KAAK,EAAE;YACd;UAAA;QAEJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,OAAOA,KAAK,EAAE;IACduD,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;EAC7E;;EAEA;EACA,IAAI,OAAO4E,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO,IAAID,MAAM,CAACC,OAAO,CAAC8B,SAAS,EAAE;IAC/E;IACA/B,MAAM,CAACC,OAAO,CAAC8B,SAAS,CAACC,WAAW,CAAC,CAACxC,OAAO,EAAEyC,OAAO,EAAEC,YAAY,KAAK;MACvE,IAAI1C,OAAO,CAACW,MAAM,KAAK,qBAAqB,EAAE;QAC5ChQ,iBAAiB,GAAGqP,OAAO,CAACvC,QAAQ;QACpCiF,YAAY,CAAC;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC;QAC/B,OAAO,IAAI;MACb;MAEA,IAAI3C,OAAO,CAACW,MAAM,KAAK,kBAAkB,EAAE;QACzC+B,YAAY,CAAC;UACXjF,QAAQ,EAAE9M,iBAAiB;UAC3BkN,aAAa,EAAEhN,qBAAqB;UACpC6M,YAAY,EAAE9M,YAAY,CAAC0B;QAC7B,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MAEA,IAAI0N,OAAO,CAACW,MAAM,KAAK,gBAAgB,EAAE;QACvC+B,YAAY,CAAC;UACX1G,IAAI,EAAEpL,YAAY;UAClBsJ,SAAS,EAAErJ;QACb,CAAC,CAAC;QACF,OAAO,IAAI;MACb;MAEA,IAAImP,OAAO,CAACW,MAAM,KAAK,kBAAkB,EAAE;QACzCrD,gBAAgB,CAAC,CAAC;QAClBoF,YAAY,CAAC;UAAEC,OAAO,EAAE;QAAK,CAAC,CAAC;QAC/B,OAAO,IAAI;MACb;IACF,CAAC,CAAC;EACJ,CAAC,MAAM,CACP;AACF,CAAC;;AAGD;AACAL,mBAAmB,CAAC,CAAC,CAAC/B,KAAK,CAACnI,KAAK,IAAI;EACnCuD,OAAO,CAACvD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;AAC9D,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,SAASwK,wBAAwBA,CAACpR,OAAY,EAQnD;EACA,OAAO;IACL+G,KAAK,EAAE/G,OAAO,CAAC+G,KAAK,IAAI,EAAE;IAC1BgB,SAAS,EAAE/H,OAAO,CAAC+H,SAAS,IAAI/H,OAAO,CAAC0H,IAAI,IAAI,EAAE;IAClDR,WAAW,EAAElH,OAAO,CAACkH,WAAW,IAAI,EAAE;IACtCzF,QAAQ,EAAEzB,OAAO,CAACyB,QAAQ,IAAIzB,OAAO,CAAC+G,KAAK,IAAI/G,OAAO,CAACkH,WAAW,IAAI,EAAE;IACxEvD,KAAK,EAAE3D,OAAO,CAAC2D,KAAK;IACpB+D,IAAI,EAAE1H,OAAO,CAAC0H,IAAI,IAAI,EAAE;IACxBvH,EAAE,EAAEH,OAAO,CAACG,EAAE,IAAI;EACpB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}