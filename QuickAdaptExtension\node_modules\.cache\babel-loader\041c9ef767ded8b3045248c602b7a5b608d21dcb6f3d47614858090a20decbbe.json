{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\drawer\\\\LogoutPopup.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Typography, Button, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { useTranslation } from 'react-i18next';\nimport { errorIcon } from '../../assets/icons/icons';\n\n// Define the types for the props\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LogoutPopup = ({\n  onClose,\n  onOk,\n  title,\n  description,\n  button1,\n  button2\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: true,\n    onClose: onClose,\n    sx: {\n      zIndex: 99999\n    },\n    PaperProps: {\n      style: {\n        borderRadius: \"4px\",\n        maxWidth: \"400px\",\n        textAlign: \"center\",\n        maxHeight: \"300px\",\n        boxShadow: \"none\"\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        padding: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          justifyContent: \"center\",\n          padding: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            // backgroundColor: \"#e4b6b0\",\n            borderRadius: \"50%\",\n            width: \"40px\",\n            height: \"40px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: errorIcon\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          fontSize: \"16px !important\",\n          fontWeight: 600,\n          padding: \"0 10px\"\n        },\n        children: translate(title)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        padding: \"20px !important\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography\n      // sx={{\n      //   fontSize: '14px',\n      //   lineHeight: '20px',\n      //   textAlign: 'center',\n      //   overflow: 'hidden',\n      //   textOverflow: 'ellipsis',\n      // }}\n      , {\n        children: translate(description)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        justifyContent: \"space-between\",\n        borderTop: \"1px solid var(--border-color)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        sx: {\n          color: \"var(--primarycolor)\",\n          border: \"1px solid var(--primarycolor)\",\n          borderRadius: \"4px\",\n          textTransform: \"capitalize\",\n          padding: \"var(--button-padding)\",\n          lineHeight: \"var(--button-lineheight)\"\n        },\n        children: translate(button1)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: onOk,\n        sx: {\n          color: \"var( --white-color)\",\n          background: \"var(--primarycolor)\",\n          borderRadius: \"4px\",\n          textTransform: \"capitalize\",\n          padding: \"var(--button-padding)\",\n          lineHeight: \"var(--button-lineheight)\"\n        },\n        children: translate(button2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(LogoutPopup, \"pr/MAE+x5KkRn8BZcdsayxNnBiM=\", false, function () {\n  return [useTranslation];\n});\n_c = LogoutPopup;\nexport default LogoutPopup;\nvar _c;\n$RefreshReg$(_c, \"LogoutPopup\");", "map": {"version": 3, "names": ["React", "Typography", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "useTranslation", "errorIcon", "jsxDEV", "_jsxDEV", "LogoutPopup", "onClose", "onOk", "title", "description", "button1", "button2", "_s", "t", "translate", "open", "sx", "zIndex", "PaperProps", "style", "borderRadius", "max<PERSON><PERSON><PERSON>", "textAlign", "maxHeight", "boxShadow", "children", "padding", "display", "justifyContent", "width", "height", "alignItems", "dangerouslySetInnerHTML", "__html", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "fontWeight", "borderTop", "onClick", "color", "border", "textTransform", "lineHeight", "background", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/drawer/LogoutPopup.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON>, <PERSON>po<PERSON>, <PERSON>ton, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport { errorIcon } from '../../assets/icons/icons';\r\n\r\n// Define the types for the props\r\ninterface LogoutPopupProps {\r\n  onClose: () => void;\r\n  onOk: () => void;\r\n  title: string;\r\n    description: string;\r\n  button1: string;\r\n  button2: string;\r\n}\r\n\r\nconst LogoutPopup: React.FC<LogoutPopupProps> = ({ onClose, onOk, title, description, button1, button2 }) => {\r\n  const { t: translate } = useTranslation();\r\n  return (\r\n    <Dialog\r\n      open\r\n      onClose={onClose}\r\n      sx={{ zIndex: 99999 }}\r\n      PaperProps={{\r\n        style: {\r\n          borderRadius: \"4px\",\r\n          maxWidth: \"400px\",\r\n          textAlign: \"center\",\r\n          maxHeight: \"300px\",\r\n          boxShadow: \"none\",\r\n        },\r\n      }}\r\n    >\r\n      <DialogTitle sx={{ padding: 0 }}>\r\n\t\t\t\t\t<div style={{ display: \"flex\", justifyContent: \"center\", padding: \"10px\" }}>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t// backgroundColor: \"#e4b6b0\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\twidth: \"40px\",\r\n\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n            <span dangerouslySetInnerHTML={{ __html: errorIcon }} />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"16px !important\", fontWeight: 600, padding: \"0 10px\" }}>\r\n          {translate(title)}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</DialogTitle>\r\n      {/* <DialogTitle sx={{ padding: \"0px !important\" }}>\r\n        <Typography\r\n          sx={{\r\n            fontFamily: 'Poppins',\r\n            fontSize: '16px !important',\r\n            fontWeight: 'bold',\r\n            lineHeight: '30px',\r\n            textAlign: 'center',\r\n          }}\r\n        >\r\n          {translateitle}\r\n        </Typography>\r\n      </DialogTitle> */}\r\n      <DialogContent sx={{ padding: \"20px !important\" }}>\r\n        <Typography\r\n          // sx={{\r\n          //   fontSize: '14px',\r\n          //   lineHeight: '20px',\r\n          //   textAlign: 'center',\r\n          //   overflow: 'hidden',\r\n          //   textOverflow: 'ellipsis',\r\n          // }}\r\n        >\r\n          {translate(description)}\r\n              </Typography>\r\n              {/* <Typography\r\n          sx={{\r\n            fontFamily: 'Poppins',\r\n            fontSize: '14px',\r\n            fontWeight: 200,\r\n            lineHeight: '20px',\r\n            textAlign: 'center',\r\n            whiteSpace: 'nowrap', \r\n            overflow: 'hidden',\r\n            textOverflow: 'ellipsis',\r\n          }}\r\n        >\r\n          {description1}\r\n              </Typography> */}\r\n      </DialogContent>\r\n      <DialogActions sx={{ justifyContent: \"space-between\", borderTop: \"1px solid var(--border-color)\" }}>\r\n        <Button\r\n          onClick={onClose}\r\n          sx={{\r\n            color: \"var(--primarycolor)\",\r\n            border:\"1px solid var(--primarycolor)\",\r\n            borderRadius: \"4px\",\r\n            textTransform: \"capitalize\",\r\n            padding: \"var(--button-padding)\",\r\n            lineHeight:\"var(--button-lineheight)\",\r\n          }}\r\n        >\r\n          {translate(button1)}\r\n        </Button>\r\n        <Button\r\n          onClick={onOk}\r\n          \r\n          sx={{\r\n              color: \"var( --white-color)\",\r\n\t\t\t\t\t\t\tbackground: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight:\"var(--button-lineheight)\"\r\n          }}\r\n        >\r\n          {translate(button2)}\r\n        </Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default LogoutPopup;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAcC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,aAAa,EAAEC,aAAa,QAAQ,eAAe;AAC1G,SAASC,cAAc,QAAQ,eAAe;AAE9C,SAASC,SAAS,QAAQ,0BAA0B;;AAEpD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAUA,MAAMC,WAAuC,GAAGA,CAAC;EAAEC,OAAO;EAAEC,IAAI;EAAEC,KAAK;EAAEC,WAAW;EAAEC,OAAO;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC3G,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGb,cAAc,CAAC,CAAC;EACzC,oBACEG,OAAA,CAACP,MAAM;IACLkB,IAAI;IACJT,OAAO,EAAEA,OAAQ;IACjBU,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAM,CAAE;IACtBC,UAAU,EAAE;MACVC,KAAK,EAAE;QACLC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,OAAO;QACjBC,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE;MACb;IACF,CAAE;IAAAC,QAAA,gBAEFrB,OAAA,CAACN,WAAW;MAACkB,EAAE,EAAE;QAAEU,OAAO,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACjCrB,OAAA;QAAKe,KAAK,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEF,OAAO,EAAE;QAAO,CAAE;QAAAD,QAAA,eAC1ErB,OAAA;UACCe,KAAK,EAAE;YACN;YACAC,YAAY,EAAE,KAAK;YACnBS,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdH,OAAO,EAAE,MAAM;YACfI,UAAU,EAAE,QAAQ;YACpBH,cAAc,EAAE;UACjB,CAAE;UAAAH,QAAA,eAEGrB,OAAA;YAAM4B,uBAAuB,EAAE;cAAEC,MAAM,EAAE/B;YAAU;UAAE;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNjC,OAAA,CAACT,UAAU;QAACqB,EAAE,EAAE;UAAEsB,QAAQ,EAAE,iBAAiB;UAAEC,UAAU,EAAE,GAAG;UAAEb,OAAO,EAAE;QAAS,CAAE;QAAAD,QAAA,EAC9EX,SAAS,CAACN,KAAK;MAAC;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAcZjC,OAAA,CAACL,aAAa;MAACiB,EAAE,EAAE;QAAEU,OAAO,EAAE;MAAkB,CAAE;MAAAD,QAAA,eAChDrB,OAAA,CAACT;MACC;MACA;MACA;MACA;MACA;MACA;MACA;MAAA;QAAA8B,QAAA,EAECX,SAAS,CAACL,WAAW;MAAC;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeN,CAAC,eAChBjC,OAAA,CAACJ,aAAa;MAACgB,EAAE,EAAE;QAAEY,cAAc,EAAE,eAAe;QAAEY,SAAS,EAAE;MAAgC,CAAE;MAAAf,QAAA,gBACjGrB,OAAA,CAACR,MAAM;QACL6C,OAAO,EAAEnC,OAAQ;QACjBU,EAAE,EAAE;UACF0B,KAAK,EAAE,qBAAqB;UAC5BC,MAAM,EAAC,+BAA+B;UACtCvB,YAAY,EAAE,KAAK;UACnBwB,aAAa,EAAE,YAAY;UAC3BlB,OAAO,EAAE,uBAAuB;UAChCmB,UAAU,EAAC;QACb,CAAE;QAAApB,QAAA,EAEDX,SAAS,CAACJ,OAAO;MAAC;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACTjC,OAAA,CAACR,MAAM;QACL6C,OAAO,EAAElC,IAAK;QAEdS,EAAE,EAAE;UACA0B,KAAK,EAAE,qBAAqB;UACnCI,UAAU,EAAE,qBAAqB;UACjC1B,YAAY,EAAE,KAAK;UACnBwB,aAAa,EAAE,YAAY;UAC3BlB,OAAO,EAAE,uBAAuB;UAChCmB,UAAU,EAAC;QACR,CAAE;QAAApB,QAAA,EAEDX,SAAS,CAACH,OAAO;MAAC;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACzB,EAAA,CA5GIP,WAAuC;EAAA,QAClBJ,cAAc;AAAA;AAAA8C,EAAA,GADnC1C,WAAuC;AA8G7C,eAAeA,WAAW;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}